---
description: WPF with IronPython using DCMvn (View, ViewModel, XAML). Clean, minimal pattern with WPFWindow and DCMvn MVVM.
globs: *.py,*.xaml
---

### Purpose
- Provide a concise, repeatable pattern to build WPF UIs in IronPython with DCMvn.
- Use `DCMvn.forms.wpfforms.WPFWindow` for XAML loading and `DCMvn.forms.mvvm` for MVVM.

### ViewModel (Python)
```python
# coding: utf-8
from DCMvn.forms.mvvm import ViewModelBase, RelayCommand

class ExampleViewModel(ViewModelBase):
    def __init__(self):
        ViewModelBase.__init__(self)
        self._title = "Hello"
        self.change_title_command = RelayCommand(self._on_change_title)

    @property
    def title(self):
        return self._title

    @title.setter
    def title(self, value):
        if value != self._title:
            self._title = value
            self.RaisePropertyChanged("title")

    def _on_change_title(self, _=None):
        self.title = "Changed"
```

### View (Python)
```python
# coding: utf-8
import os
from DCMvn.forms.wpfforms import WPFWindow

class ExampleView(WPFWindow):
    def __init__(self, view_model):
        xaml_path = os.path.join(os.path.dirname(__file__), "example_view.xaml")
        WPFWindow.__init__(self, xaml_path)
        self.DataContext = view_model
        self.view_model = view_model

        # Optional: expose owner for dialogs and cleanup on close
        self.view_model.owner_window = self
        self.Closing += self._on_window_closing

    def _on_window_closing(self, sender, e):
        try:
            if hasattr(self.view_model, 'owner_window'):
                self.view_model.owner_window = None
            if hasattr(self.view_model, 'cleanup'):
                self.view_model.cleanup()
        except Exception as ex:
            from DCMvn.core.framework import Debug
            Debug.WriteLine("Window cleanup error: {}".format(str(ex)))
```

### XAML (example_view.xaml)
```xml
<mah:MetroWindow
  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
  xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
  Title="Example" Width="420" SizeToContent="Height" WindowStartupLocation="CenterOwner">
  <StackPanel Margin="16">
    <TextBlock Text="{Binding title}" FontSize="20" Margin="0,0,0,12" />
    <Button Content="Change" Width="120" Command="{Binding change_title_command}" />
  </StackPanel>
</mah:MetroWindow>
```

### Run
```python
vm = ExampleViewModel()
view = ExampleView(vm)
view.ShowDialog()
```

### Notes
- DataContext is set in Python code-behind. Do not use design-time DataContext in XAML.
- Property names are lowercase for WPF bindings per DCMvn conventions.
- MahApps is provided by DCMvn by default; no extra setup required in views.