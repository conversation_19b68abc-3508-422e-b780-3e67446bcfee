# coding: utf-8
import clr
from DCMvn.core import DB
from DCMvn.core.framework import System
from DCMvn.forms import alert

from .clashes import MepMassObject, ArcObject, FloorMassObject
from .clashes import SolidClashExecutor, SolidClashResult, ColumnRayClashResult, WallCornerRayClashResult
from utils import get_arc_from_solid

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)


def execute_solid_intersection(condition_arc_elements, condition_masses):
    # type: (list[ArcObject], list[MepMassObject]) -> tuple[list[SolidClashResult], list[ArcObject]]  # noqa
    """Execute Solid Intersection check

    Args:
        condition_arc_elements (list[ArcObject]): filtered arc elements
        condition_masses (list[MepMassObject]): filtered mass elements

    Returns:
        tuple[list[SolidClashResult], list[ArcObject]]: list of clashes and list of arc elements with solid get failed
    """
    arc_none_solid = []  # type: list[ArcObject]  # noqa
    clashes = []  # type: list[SolidClashResult]  # noqa

    for arc in condition_arc_elements:
        arc_solid = arc.get_solid()
        if arc_solid is None:
            bbox = arc.get_bbox()
            if bbox is None:
                arc_none_solid.append(arc)
                continue
            ol = DB.Outline(bbox.Min, bbox.Max)
            if ol.IsEmpty:
                arc_none_solid.append(arc)
                continue
            bbox_intersect_filter = DB.BoundingBoxIntersectsFilter(ol)
        else:
            ol = arc.get_outline(arc_solid)
            bbox_intersect_filter = DB.BoundingBoxIntersectsFilter(ol)

        mass_intersect = condition_masses.Where(  # noqa
            lambda x: bbox_intersect_filter.PassesFilter(x.to_element())).ToList()

        for mass in mass_intersect:
            clash_executor = SolidClashExecutor(arc, mass)
            can_be_clashed = clash_executor.is_geo_available
            if can_be_clashed:
                clash_result = clash_executor.check_clash()
                if clash_result:
                    clashes.append(clash_result)

    if not clashes:
        return [], arc_none_solid
    
    group_by_mass = (clashes.GroupBy(lambda x: x.mep_mass_object.id)  # noqa
                     .Select(lambda x: x.ToList())
                     .Select(lambda x: max(x, key=lambda y: y.intersect_volume))
                     .OrderByDescending(lambda x: x.intersect_volume))
    
    group_by_arc = (group_by_mass.GroupBy(lambda x: x.arc_object.id)  # noqa
                    .Select(lambda x: x.ToList())
                    .Select(lambda x: max(x, key=lambda y: y.intersect_volume))
                    .OrderByDescending(lambda x: x.intersect_volume))

    return list(group_by_arc), arc_none_solid


def execute_raycast_intersection(arc_filter, condition_masses,
                                 current_doc):
    # type: (DB.ElementFilter, list[MepMassObject], DB.Document, DB.Transform) -> tuple[list[ColumnRayClashResult], list[MepMassObject]]  # noqa
    """Execute Raycast Intersection check

    Args:
        condition_masses (list[MepMassObject]): filtered mass_ elements
        arc_filter (DB.ElementFilter): arc element filter
        current_doc (DB.Document): current document

    Returns:
        tuple[list[SolidClashResult], list[MepMassObject]]: list of clashes and list of mass_ elements with solid get failed
    """
    clashes = []  # type: list[ColumnRayClashResult]  # noqa
    mass_none_solid = []  # type: list[MepMassObject]  # noqa

    if not isinstance(current_doc.ActiveView, DB.View3D):
        alert("Please open a 3D view\n💡 Do not hide any element want to check in View", warn_icon=True, exitscript=True)

    intersector = DB.ReferenceIntersector(arc_filter, DB.FindReferenceTarget.Face, current_doc.ActiveView)  # type: DB.ReferenceIntersector  # noqa
    intersector.FindReferencesInRevitLinks = True

    for mass_ in condition_masses:
        mass_solid = mass_.get_solid()
        if mass_solid is None:
            mass_none_solid.append(mass_)
            continue

        element = mass_.get_raycast_element(intersector)
        if element:
            clash_result = ColumnRayClashResult(ArcObject(element), mass_)
            clashes.append(clash_result)

    return clashes, mass_none_solid


def execute_raycast_intersection_floor(arc_filter, condition_masses, current_doc):
    # type: (DB.ElementFilter, list[MepMassObject], DB.Document) -> tuple[list[ColumnRayClashResult], list[MepMassObject]]  # noqa
    """Execute Raycast Intersection check

    Args:
        condition_masses (list[MepMassObject]): filtered mass elements
        arc_filter (DB.ElementFilter): arc element filter
        current_doc (DB.Document): current document
    Returns:
        tuple[list[SolidClashResult], list[MepMassObject]]: list of clashes and list of mass elements with solid get failed
    """
    clashes = []  # type: list[ClashResult]  # noqa
    mass_none_solid = []  # type: list[MepMassObject]  # noqa

    if not isinstance(current_doc.ActiveView, DB.View3D):
        alert("Please open a 3D view\n💡 Do not hide any element want to check in View", warn_icon=True, exitscript=True)

    intersector = DB.ReferenceIntersector(arc_filter, DB.FindReferenceTarget.Element, current_doc.ActiveView)  # type: DB.ReferenceIntersector  # noqa
    intersector.FindReferencesInRevitLinks = True

    max_wall_corner_mass_arc = 4  # for mass describe wall corner, its geometry has maximum 4 arcs

    for mass_ in condition_masses:
        mass_solid = mass_.get_solid()
        if mass_solid is None:
            mass_none_solid.append(mass_)
            continue

        arcs = get_arc_from_solid(mass_solid)
        if len(arcs) <= max_wall_corner_mass_arc:
            wall_corner_mass = FloorMassObject(mass_.to_element())
            elements = wall_corner_mass.get_raycast_elements(intersector, arcs)
            if elements:
                clashes.append(WallCornerRayClashResult([ArcObject(a) for a in elements], wall_corner_mass))
        else:
            element = mass_.get_raycast_element(intersector)
            if element:
                clashes.append(ColumnRayClashResult(ArcObject(element), mass_))

    return clashes, mass_none_solid
