# coding: utf-8
import os.path as op

from DCMvn.core import DB  # noqa
from DCMvn.io import get_config_property, set_config_property, pick_file
from DCMvn.forms.wpfforms import WPFWindow
from DCMvn.forms.mvvm import ViewModelBase, RelayCommand
from DCMvn.forms import alert
from DCMvn.core.framework import IO, Windows  # noqa
from pyrevit import script

from .. import constant


class SharedParameterPath(WPFWindow):
    def __init__(self, xaml_path):
        # type: (str, SharedParameterPathViewModel) -> SharedParameterPath
        WPFWindow.__init__(self, xaml_path, handle_esc=False)

    
class SharedParameterPathViewModel(ViewModelBase):
    def __init__(self, app, view):
        # type: (DB.Application, Windows.Window) -> SharedParameterPathViewModel
        ViewModelBase.__init__(self)
        self._window = view
        self._window.DataContext = self
        self._app = app
        self._register_list_path = get_config_property(constant.SHARED_PARAMETER_CREATOR_SECTION, constant.REGISTER_LIST_PATH)
        self._shared_parameter_path = app.SharedParametersFilename
        
        self._pick_file_command = RelayCommand(self.execute_pick_file, self.can_execute_pick_file)
        self._confirm_command = RelayCommand(self.execute_confirm, self.can_execute_confirm)

    @property
    def SharedParameterPath(self):
        return self._shared_parameter_path

    @SharedParameterPath.setter
    def SharedParameterPath(self, value):
        self._shared_parameter_path = value
        self.RaisePropertyChanged("SharedParameterPath")
    
    @property
    def RegisterListPath(self):
        return self._register_list_path
    
    @RegisterListPath.setter
    def RegisterListPath(self, value):
        self._register_list_path = value
        self.RaisePropertyChanged("RegisterListPath")
        
    @property
    def PickFileCommand(self):
        return self._pick_file_command

    def can_execute_pick_file(self, param):  # noqa
        return True

    def execute_pick_file(self, param):  # noqa
        if param == "xlsx":
            title = "Select Shared Parameter File"
            filter_ex = "Excel Workbook (*.xlsx)|*.xlsx" 
        else:
            title = "Select Register List"
            filter_ex = "Text files (*.txt)|*.txt"
        file_path = pick_file(title=title, files_filter=filter_ex)
        if file_path and IO.File.Exists(file_path):
            if param == "xlsx":
                self.RegisterListPath = file_path
            else:
                self.SharedParameterPath = file_path
                
    @property
    def ConfirmCommand(self):
        return self._confirm_command
    
    def can_execute_confirm(self, param):  # noqa
        return True
    
    def execute_confirm(self, param):  # noqa
        if not self.RegisterListPath or not IO.File.Exists(self.RegisterListPath):
            retry_register_path = alert("Invalid path\nPlease select register list file.", 
                                        warn_icon=True, retry=True, cancel=True, ok=False)
            if not retry_register_path:
                self._window.Close()
                script.exit()
            return
        if not self.SharedParameterPath or not IO.File.Exists(self.SharedParameterPath):
            retry_sharedparam_path = alert("Invalid path\nPlease select shared parameter file.", 
                                           warn_icon=True, retry=True, cancel=True, ok=False)
            if not retry_sharedparam_path:
                self._window.Close()
                script.exit()
            return
        
        self._app.SharedParametersFilename = self.SharedParameterPath
        set_config_property(constant.SHARED_PARAMETER_CREATOR_SECTION, constant.REGISTER_LIST_PATH, self.RegisterListPath)
        self._window.Close()

       
def get_shared_parameter_path(app):
    # type: (DB.Application) -> tuple
    """get shared parameter path and register list path

    Args:
        app (DB.Application): Revit Application

    Returns:
        Share&Register (tuple): shared parameter path and register list path
    """
    xaml_path = op.join(op.dirname(__file__), "SharedParameterPath.xaml")
    view = SharedParameterPath(xaml_path)
    view_model = SharedParameterPathViewModel(app, view)
    view.ShowDialog()
    return view_model.SharedParameterPath, view_model.RegisterListPath