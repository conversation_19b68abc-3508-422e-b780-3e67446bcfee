# coding: utf-8
from DCMvn.core import DB
from DCMvn.core.framework import Debug

class BaseModel(object):
    """Base class for DCMvn model objects."""
    
    def __init__(self):
        # type: () -> None
        self._cached_properties = {}
        
    def _get_cached_property(self, key, calculator_func):
        # type: (str, callable) -> object
        """Get cached property value or calculate if not cached."""
        if key not in self._cached_properties:
            try:
                self._cached_properties[key] = calculator_func()
            except Exception as ex:
                Debug.WriteLine("Error calculating property {}: {}".format(key, str(ex)))
                self._cached_properties[key] = None
        return self._cached_properties[key]
        
    def clear_cache(self):
        """Clear all cached properties."""
        self._cached_properties.clear()

class DataTransferObject(object):
    """Simple data container for transferring data between layers."""
    
    def __init__(self, **kwargs):
        # type: (**object) -> None
        for key, value in kwargs.items():
            setattr(self, key, value)
            
    def __repr__(self):
        # type: () -> str
        """String representation for debugging."""
        attrs = []
        for key, value in self.__dict__.items():
            if not key.startswith('_'):
                attrs.append("{}={}".format(key, repr(value)))
        return "{}({})".format(self.__class__.__name__, ", ".join(attrs))