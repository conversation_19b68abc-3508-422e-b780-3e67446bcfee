from DCMvn.core import DB

class RevitEntity:
    def __init__(self, revit_element):
        """
        Revit entity base on Autodesk.Revit.DB.Element
        Args:
            revit_element: Autodesk.Revit.DB.Element
        """
        self._element = revit_element

    @property
    def id(self):
        return self._element.Id
    
    @property
    def revit_guid(self):
        guid_ = self._element.get_Parameter(DB.BuiltInParameter.IFC_GUID)
        return guid_.AsString() if guid_ else None
    
    @property
    def revit_category(self):
        try:
            return self._element.Category.Name
        except:
            return "N/A"
        
    @property
    def revit_level(self):
        family_level = self._element.get_Parameter(DB.BuiltInParameter.FAMILY_LEVEL_PARAM)
        routing_level = self._element.get_Parameter(DB.BuiltInParameter.RBS_START_LEVEL_PARAM)
        if family_level:
            return family_level.AsValueString()
        elif routing_level:
            return routing_level.AsValueString()
        else:
            return "N/A"
            
