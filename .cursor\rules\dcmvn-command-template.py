# coding: utf-8
from DCMvn.forms.mvvm import <PERSON><PERSON><PERSON><PERSON><PERSON>
from DCMvn.core.framework import Debug

class ExampleCommand(RelayCommand):
    """Example command following DCMvn patterns."""
    
    def __init__(self, view_model):
        # type: (object) -> None
        super(ExampleCommand, self).__init__(self.execute, self.can_execute)
        self.view_model = view_model
        
    def execute(self, parameter):
        # type: (object) -> None
        """Execute the command."""
        try:
            # Command logic here
            Debug.WriteLine("Executing command")
            
            # Update view model properties
            self.view_model.RaisePropertyChanged("some_property")
            
        except Exception as ex:
            Debug.WriteLine("Command execution error: {}".format(str(ex)))
            
    def can_execute(self, parameter):
        # type: (object) -> bool
        """Check if command can execute."""
        try:
            return self.view_model is not None
        except Exception:
            return False