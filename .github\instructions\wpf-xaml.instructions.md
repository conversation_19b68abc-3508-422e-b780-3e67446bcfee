---
applyTo: "**/*.xaml"
---

# WPF and XAML Guidelines for DCMvn

## XAML Structure and Binding
Follow this structure for WPF views:
```xaml
<Window x:Class="MainView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="DCMvn Application" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="10">
            <TextBlock Text="{Binding Title}" Foreground="White" FontSize="16"/>
        </Border>
        
        <!-- Content -->
        <Grid Grid.Row="1" Margin="10">
            <!-- Your content here -->
        </Grid>
        
        <!-- Footer with buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="10">
            <Button Content="Execute" Command="{Binding ExecuteCommand}" 
                    Margin="5" Padding="10,5"/>
            <Button Content="Cancel" Click="Cancel_Click" 
                    Margin="5" Padding="10,5"/>
        </StackPanel>
    </Grid>
</Window>
```

## Data Binding Best Practices
- Use `{Binding PropertyName}` for ViewModel properties
- Use `{Binding CommandName}` for RelayCommand bindings
- Implement INotifyPropertyChanged in ViewModels
- Use ObservableCollection for dynamic lists

## Control Templates and Styling
- Define styles in ResourceDictionary
- Use consistent color schemes
- Implement proper focus handling
- Use validation templates for user input

## Code-Behind Patterns
Keep code-behind minimal:
```python
def __init__(self, view_model):
    self.InitializeComponent()
    self.DataContext = view_model
    self.view_model = view_model

def Cancel_Click(self, sender, e):
    self.Close()
```

## IronPython WPF Integration
- Use `clr.AddReference("PresentationFramework")`
- Import `System.Windows` namespaces
- Handle events properly with IronPython syntax
- Use proper dispose patterns for resources
