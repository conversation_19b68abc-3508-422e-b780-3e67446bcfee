---
description: ViewModel patterns for DCMvn MVVM architecture
globs: ["**/viewmodel/*.py"]
alwaysApply: true
---

# ViewModel Patterns

## ViewModel Responsibilities
- Manage UI state and data binding
- Orchestrate service calls and business logic
- Handle command implementations
- Manage ObservableCollections for UI binding
- Coordinate between different sub-ViewModels

## DCMvn ViewModel Patterns
- Inherit from `DCMvn.forms.mvvm.ViewModelBase`
- Use `ObservableCollection[T]()` for collections bound to UI
- Call `RaisePropertyChanged("property_name")` after property changes
- Store service references in constructor
- Initialize commands as `RelayCommand` instances

## Property Implementation
- Use private fields with public properties
- Implement proper getters and setters
- Always call `RaisePropertyChanged` in setters when value changes
- Use lowercase property names for WPF binding consistency
- Cache expensive calculations in private fields

## Command Handling
- Initialize commands in constructor: `self.command_name = RelayCommand(execute_func, can_execute_func)`
- Implement separate execute and can_execute methods
- Handle exceptions within command methods
- Update relevant properties after command execution

## Sub-ViewModel Coordination
- Compose complex ViewModels from smaller, focused sub-ViewModels
- Pass service references to sub-ViewModels
- Coordinate state changes between parent and child ViewModels
- Use events or callbacks for cross-ViewModel communication

@main-viewmodel-pattern.py