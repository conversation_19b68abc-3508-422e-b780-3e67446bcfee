# coding: utf-8

from DCMvn.core import DB
from pyrevit.compat import get_elementid_value_func
from ..models import (BaseMep, BaseSpatial, RevitSpatial, GenericSpatial,  # noqa: F401
                      ParameterPair, DetectionResult)
from ..utils import get_element_center_point
from .detection import Geo<PERSON>Dete<PERSON>, SpatialIndexer
from .strategies import RevitSpatialStrategy, GenericSpatialStrategy

class DetectionService(object):
    """Service for detecting spatial relationships between MEP elements and spatial elements."""
    
    def __init__(self, document, advanced_options=None):
        # type: (DB.Document, object) -> None
        self.document = document
        self.advanced_options = advanced_options
        
        self.geometry_detector = GeometryDetector(self.document, advanced_options)
        self.revit_strategy = RevitSpatialStrategy(self.geometry_detector)
        self.generic_strategy = GenericSpatialStrategy(self.geometry_detector)
        self.indexer = SpatialIndexer(self.document, advanced_options)
        self.get_elementid_value = get_elementid_value_func()
        
    def detect_mep_spatial_relationships(self, mep_elements, spatial_elements, parameter_pairs):
        # type: (list[BaseMep], list[BaseSpatial], list[ParameterPair]) -> list[DetectionResult]
        results = []  # type: list[DetectionResult]

        # Pre-classify MEP elements for performance
        mep_classifications = []  # type: list[tuple[BaseMep, dict]]
        for mep_element in mep_elements:
            mep_type_info = self.classify_mep_element(mep_element)
            mep_classifications.append((mep_element, mep_type_info))
        
        # Build mep_to_spatials_map using indexer and detection strategies
        mep_to_spatials_map = self.mep_to_spatial_map(spatial_elements, mep_classifications)
        
        # Create results for all MEP elements
        allow_multiple = self.advanced_options.allow_multiple_values if self.advanced_options else False
        for mep_element, mep_type_info in mep_classifications:
            mep_id = self.get_elementid_value(mep_element.id)
            matching_spatials = mep_to_spatials_map.get(mep_id, [])  # type: list[tuple[BaseSpatial, float]]
            
            if matching_spatials:
                # Apply multiple value constraint
                if not allow_multiple and len(matching_spatials) > 1:
                    # Sort spatials by distance
                    matching_spatials.sort(key=lambda s: s[1])
                    matching_spatials = [matching_spatials[0]]

                matching_spatials = [s[0] for s in matching_spatials]
                for parameter_pair in parameter_pairs:
                    result = DetectionResult(mep_element, matching_spatials, parameter_pair, "primary")
                    results.append(result)
            else:
                for parameter_pair in parameter_pairs:
                    result = DetectionResult(mep_element, [], parameter_pair, "no_match")
                    results.append(result)

        return results


    def mep_to_spatial_map(self, spatial_elements, mep_classifications):
        # type: (list[BaseSpatial], list[tuple[BaseMep, dict]]) -> dict[int, list[tuple[BaseSpatial, float]]]
        mep_to_spatials_map = {}  # type: dict[int, list[tuple[BaseSpatial, float]]]

        all_spatial_elements = [s for s in spatial_elements if isinstance(s, (RevitSpatial, GenericSpatial))]

        for spatial_element in all_spatial_elements:
            candidate_meps = self.indexer.filter_mep_elements_by_spatial_bbox(spatial_element, mep_classifications)

            strategy = self.revit_strategy if isinstance(spatial_element, RevitSpatial) else self.generic_strategy

            for mep_element, mep_type_info in candidate_meps:
                is_match, distance = strategy.detect(mep_element, mep_type_info, spatial_element)
                if is_match:
                    mep_id = self.get_elementid_value(mep_element.id)
                    if mep_id not in mep_to_spatials_map:
                        mep_to_spatials_map[mep_id] = []
                    mep_to_spatials_map[mep_id].append((spatial_element, distance))

        return mep_to_spatials_map


    @staticmethod
    def classify_mep_element(mep_element):
        # type: (BaseMep) -> dict
        """Pre-classify MEP element to avoid repeated type checks during detection."""
        element = mep_element.element
        facing_direction = element.FacingOrientation if isinstance(element, DB.FamilyInstance) else None
        hand_direction = element.HandOrientation if isinstance(element, DB.FamilyInstance) else None
        
        # Cache location information to avoid repeated access
        location = element.Location
        has_location_curve = isinstance(location, DB.LocationCurve) and location.Curve is not None
        has_location_point = isinstance(location, DB.LocationPoint)
        
        # Determine element classification
        is_family_instance = isinstance(element, DB.FamilyInstance)
        is_direct_shape = isinstance(element, DB.DirectShape)
        is_curve_element = not is_family_instance and not is_direct_shape and has_location_curve
        
        # Pre-compute geometry data for performance
        center_point = None
        curve = None
        point = None
        
        if has_location_curve:
            curve = location.Curve
        elif has_location_point:
            point = location.Point  # noqa
        
        # Get room calculation points (highest priority for spatial detection)
        room_calc_points = mep_element.room_calculation_points
        
        # Only compute center point if needed (when no other location available and no room calc points)
        if not has_location_curve and not has_location_point and not room_calc_points:
            center_point = get_element_center_point(mep_element)
        
        return {
            'facing_direction': facing_direction,
            'hand_direction': hand_direction,
            'is_family_instance': is_family_instance,
            'is_direct_shape': is_direct_shape,
            'is_curve_element': is_curve_element,
            'has_location_curve': has_location_curve,
            'has_location_point': has_location_point,
            'location': location,
            'curve': curve,
            'point': point,
            'center_point': center_point,
            'room_calc_points': room_calc_points
        }
