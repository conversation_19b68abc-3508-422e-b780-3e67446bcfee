<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>TabooZone QA Report - pyRevit Compatible</title>
    <style>
        /* pyRevit compatible styling - inline to avoid external dependencies */
        body {
            font-family: Segoe UI, Arial, sans-serif;
            margin: 8px;
            background-color: #ffffff;
            font-size: 12px;
        }
        
        h2 {
            margin: 4px 0 8px 0;
            color: #2c3e50;
            font-size: 16px;
        }
        
        h3 {
            margin: 10px 0 6px 0;
            color: #34495e;
            font-size: 14px;
        }
        
        .header-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 16px;
        }
        
        /* Summary cards - simplified for pyRevit */
        .summary-container {
            margin: 8px 0 16px 0;
        }
        
        .summary-row {
            display: inline-block;
            margin: 4px 8px 4px 0;
            padding: 6px 10px;
            border-radius: 4px;
            border: 1px solid rgba(0,0,0,0.1);
            min-width: 120px;
            vertical-align: top;
        }
        
        .card-success { background: #d5f5e3; }
        .card-error { background: #fadbd8; }
        .card-warning { background: #fcf3cf; }
        
        .card-label {
            font-size: 11px;
            opacity: 0.8;
            margin-bottom: 2px;
        }
        
        .card-value {
            font-size: 16px;
            font-weight: 600;
        }
        
        /* Table styling for pyRevit */
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 8px 0;
            font-size: 11px;
        }
        
        th {
            text-align: left;
            padding: 6px 8px;
            background: #f1f2f6;
            border: 1px solid #ddd;
            font-weight: 600;
            font-size: 11px;
        }
        
        td {
            padding: 6px 8px;
            border: 1px solid #ddd;
            vertical-align: top;
        }
        
        /* Row styling */
        .row-matched-pass { background: #eafaf1; }
        .row-matched-fail { background: #fdecea; }
        .row-missing { background: #fdecea; }
        .row-extra { background: #fdecea; }
        .row-duplicate { background: #fff4d6; }
        
        /* Composite GUID styling for pyRevit */
        .composite-guid {
            font-family: Consolas, monospace;
            font-size: 10px;
            color: #666;
            line-height: 1.3;
            max-width: 280px;
        }
        
        .guid-part {
            display: block;
            margin-bottom: 2px;
            padding: 1px 3px;
            background: #f8f9fa;
            border-radius: 2px;
        }
        
        /* Info block styling */
        .info-block {
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
            background: #f9f9f9;
            border: 1px solid rgba(0,0,0,0.08);
            font-size: 11px;
            color: #444;
        }
        
        .status-banner {
            margin: 12px 0;
            padding: 8px;
            border-radius: 4px;
            background: #f4f6f6;
            font-weight: bold;
            font-size: 12px;
        }
        
        /* pyRevit linkified elements */
        .linkified {
            color: #0066cc;
            text-decoration: underline;
            cursor: pointer;
        }
        
        /* Features section */
        .features-section {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            margin: 8px 0;
        }
        
        .features-title {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 13px;
            font-weight: 600;
        }
        
        .features-list {
            margin: 4px 0;
            color: #6c757d;
            font-size: 11px;
            line-height: 1.4;
        }
        
        .features-list li {
            margin-bottom: 3px;
        }
        
        /* Note section */
        .note-section {
            margin-top: 16px;
            padding: 8px;
            background: #e3f2fd;
            border-radius: 4px;
            border-left: 3px solid #2196f3;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <!-- Header section following pyRevit patterns -->
    <div style="font-family:Segoe UI, Arial, sans-serif;margin:8px 0;">
        <h2>TabooZone QA Report - Complex Many-to-Many Validation</h2>
        <div class="header-info">
            ARC: Architectural Elements | Mass: Mass Elements | Tol: 50.0 mm
        </div>
    </div>

    <!-- Missing section -->
    <h3>Missing in Mass (ARC present, Mass missing)</h3>
    <table>
        <tr>
            <th>Composite GUID</th>
            <th>ARC Elements</th>
            <th>ARC Info</th>
        </tr>
        <tr class="row-missing">
            <td class="composite-guid">
                <span class="guid-part">a1b2c3d4-e5f6-7890-abcd-ef1234567890</span>
                <span class="guid-part">b2c3d4e5-f6g7-8901-bcde-f23456789012</span>
            </td>
            <td>DB.Element(156789), DB.Element(234567)</td>
            <td>ID:156789 | Walls | Basic Wall: Generic - 200mm<br>ID:234567 | Structural Columns | Concrete-Rectangular-Column: 400 x 600mm</td>
        </tr>
    </table>

    <!-- Extra section -->
    <h3>Extra in Mass (Mass present, ARC missing)</h3>
    <table>
        <tr>
            <th>Composite GUID</th>
            <th>Mass Element</th>
            <th>Mass Info</th>
        </tr>
        <tr class="row-extra">
            <td class="composite-guid">
                <span class="guid-part">f6g7h8i9-j0k1-2345-fghi-************</span>
                <span class="guid-part">g7h8i9j0-k1l2-3456-ghij-************</span>
            </td>
            <td><span class="linkified">DB.Element(789012)</span></td>
            <td>ID:789012 | Mass | Mass: Conceptual Mass</td>
        </tr>
    </table>

    <!-- Duplicate GUIDs section -->
    <h3>Duplicate GUIDs in ARC</h3>
    <table>
        <tr>
            <th>Composite GUID</th>
            <th>ARC Element</th>
            <th>ARC Info</th>
        </tr>
        <tr class="row-duplicate">
            <td class="composite-guid">
                <span class="guid-part">h8i9j0k1-l2m3-4567-hijk-************</span>
                <span class="guid-part">i9j0k1l2-m3n4-5678-ijkl-************</span>
            </td>
            <td>DB.Element(890123)</td>
            <td>ID:890123 | Walls | Basic Wall: Exterior - Brick on CMU</td>
        </tr>
    </table>

    <h3>Duplicate GUIDs in Mass</h3>
    <i>No duplicate Mass GUIDs.</i>
