# coding: utf8
"""
Update Revit Space Numbers and Names from Linked IFC Model
Matches spaces by GUID and updates Name and Number parameters from the linked model
"""
import clr
import os

from DCMvn.core import DB, HOST_APP
from DCMvn.forms import alert
from DCMvn.core.framework import System, Debug
from DCMvn.coreutils.assemblyhandler import load_miniexcel

# Load MiniExcel assemblies for potential reporting
load_miniexcel()
from MiniExcelLibs import MiniExcel

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

# Add Dictionary and OrderedDictionary support
clr.AddReference("System.Collections.Specialized")
from System.Collections.Generic import Dictionary
from System.Collections.Specialized import OrderedDictionary

doc = HOST_APP.doc
uidoc = HOST_APP.uidoc

# Parameter configuration - matching space_export.py
MreProjectIdentifier = "_AWA_"
IfcSpaceTypeParameter = "Export to IFC As"
IfcSpaceTypeValue = "IfcSpaceType"
LevelParameter = "IfcDecomposes"
SpaceNameParameter = "MRE_ARC.Raumname"
SpaceNumberParameter = "MRE_ARC.Raunummer Türschild"

def get_link_data(document):
    """Get the linked document containing IFC spaces"""
    link_instance = (DB.FilteredElementCollector(document)
                     .OfClass(DB.RevitLinkInstance)
                     .WhereElementIsNotElementType()
                     .FirstOrDefault(lambda x: x.Name.Contains(MreProjectIdentifier)))
    
    if not link_instance:
        return None
    
    return link_instance.GetLinkDocument()

def get_ifc_spaces_from_link(link_document):
    """Get all IFC spaces from the linked document"""
    if not link_document:
        return []
    
    collector = DB.FilteredElementCollector(link_document)
    spaces = (collector
              .OfCategory(DB.BuiltInCategory.OST_GenericModel)
              .WhereElementIsNotElementType()
              .Where(lambda x: x.LookupParameter(IfcSpaceTypeParameter) and 
                               x.LookupParameter(IfcSpaceTypeParameter).AsString() == IfcSpaceTypeValue)
              .ToList())
    return spaces

def get_revit_spaces(document):
    """Get all MEP spaces from the current Revit document"""
    collector = DB.FilteredElementCollector(document)
    spaces = (collector
              .OfCategory(DB.BuiltInCategory.OST_MEPSpaces)
              .WhereElementIsNotElementType()
              .ToList())
    return spaces

def extract_ifc_space_info(ifc_space):
    """Extract GUID, Name, and Number from IFC space"""
    try:
        guid_param = ifc_space.get_Parameter(DB.BuiltInParameter.IFC_GUID)
        name_param = ifc_space.LookupParameter(SpaceNameParameter)
        number_param = ifc_space.LookupParameter(SpaceNumberParameter)
        
        guid = guid_param.AsString() if guid_param else ""
        name = name_param.AsString() if name_param else ""
        number = number_param.AsString() if number_param else ""
        
        return {
            "guid": guid,
            "name": name,
            "number": number,
            "element": ifc_space
        }
    except Exception as ex:
        Debug.WriteLine("Error extracting IFC space info: {}".format(str(ex)))
        return None

def extract_revit_space_info(revit_space):
    """Extract GUID, Name, and Number from Revit space"""
    try:
        guid_param = revit_space.get_Parameter(DB.BuiltInParameter.IFC_GUID)
        name_param = revit_space.get_Parameter(DB.BuiltInParameter.ROOM_NAME)
        number_param = revit_space.get_Parameter(DB.BuiltInParameter.ROOM_NUMBER)
        
        guid = guid_param.AsString() if guid_param else ""
        name = name_param.AsString() if name_param else ""
        number = number_param.AsString() if number_param else ""
        
        return {
            "guid": guid,
            "name": name,
            "number": number,
            "element": revit_space,
            "name_param": name_param,
            "number_param": number_param
        }
    except Exception as ex:
        Debug.WriteLine("Error extracting Revit space info: {}".format(str(ex)))
        return None

def create_guid_mapping(ifc_spaces):
    """Create a mapping of GUID to IFC space information"""
    guid_map = {}
    
    for ifc_space in ifc_spaces:
        info = extract_ifc_space_info(ifc_space)
        if info and info["guid"] and info["guid"].strip():
            guid_map[info["guid"]] = info
    
    return guid_map

def update_revit_spaces(revit_spaces, ifc_guid_map):
    """Update Revit spaces with names and numbers from IFC spaces based on GUID matching"""
    
    update_results = {
        "matched_count": 0,
        "updated_count": 0,
        "no_match_count": 0,
        "error_count": 0,
        "updates": [],
        "no_matches": [],
        "errors": []
    }
    
    # Start a transaction for updating spaces
    with DB.Transaction(doc, "Update Space Names and Numbers from Link") as trans:
        trans.Start()
        
        try:
            for revit_space in revit_spaces:
                revit_info = extract_revit_space_info(revit_space)
                
                if not revit_info:
                    update_results["error_count"] += 1
                    update_results["errors"].append({
                        "element_id": revit_space.Id.IntegerValue,
                        "error": "Could not extract space information"
                    })
                    continue
                
                revit_guid = revit_info["guid"]
                
                if not revit_guid or not revit_guid.strip():
                    update_results["no_match_count"] += 1
                    update_results["no_matches"].append({
                        "element_id": revit_space.Id.IntegerValue,
                        "current_name": revit_info["name"],
                        "current_number": revit_info["number"],
                        "reason": "No GUID in Revit space"
                    })
                    continue
                
                # Check if we have a matching IFC space
                if revit_guid in ifc_guid_map:
                    update_results["matched_count"] += 1
                    ifc_info = ifc_guid_map[revit_guid]
                    
                    # Check if update is needed
                    name_needs_update = revit_info["name"] != ifc_info["name"]
                    number_needs_update = revit_info["number"] != ifc_info["number"]
                    
                    if name_needs_update or number_needs_update:
                        try:
                            # Update name if different
                            if name_needs_update and revit_info["name_param"]:
                                revit_info["name_param"].Set(ifc_info["name"])
                            
                            # Update number if different
                            if number_needs_update and revit_info["number_param"]:
                                revit_info["number_param"].Set(ifc_info["number"])
                            
                            update_results["updated_count"] += 1
                            update_results["updates"].append({
                                "element_id": revit_space.Id.IntegerValue,
                                "guid": revit_guid,
                                "old_name": revit_info["name"],
                                "new_name": ifc_info["name"],
                                "old_number": revit_info["number"],
                                "new_number": ifc_info["number"],
                                "name_updated": name_needs_update,
                                "number_updated": number_needs_update
                            })
                            
                        except Exception as ex:
                            update_results["error_count"] += 1
                            update_results["errors"].append({
                                "element_id": revit_space.Id.IntegerValue,
                                "guid": revit_guid,
                                "error": "Failed to update parameters: {}".format(str(ex))
                            })
                    else:
                        # Already matches - no update needed
                        Debug.WriteLine("Space {} already has correct name and number".format(revit_space.Id.IntegerValue))
                else:
                    update_results["no_match_count"] += 1
                    update_results["no_matches"].append({
                        "element_id": revit_space.Id.IntegerValue,
                        "guid": revit_guid,
                        "current_name": revit_info["name"],
                        "current_number": revit_info["number"],
                        "reason": "No matching GUID in IFC link"
                    })
            
            trans.Commit()
            
        except Exception as ex:
            trans.RollBack()
            Debug.WriteLine("Transaction failed: {}".format(str(ex)))
            raise ex
    
    return update_results

def create_ordered_row(key_value_pairs):
    """Create an OrderedDictionary from a list of (key, value) tuples to ensure column order"""
    ordered_dict = OrderedDictionary()
    for key, value in key_value_pairs:
        ordered_dict[key] = value
    return ordered_dict

def generate_update_report(update_results, ifc_guid_map):
    """Generate a detailed Excel report of the update process"""
    
    # Create sheets for different types of results
    sheets = Dictionary[str, object]()
    
    # Summary sheet
    summary_data = []
    summary_row = create_ordered_row([
        ("Total_Revit_Spaces", update_results["matched_count"] + update_results["no_match_count"] + update_results["error_count"]),
        ("Total_IFC_Spaces", len(ifc_guid_map)),
        ("Matched_by_GUID", update_results["matched_count"]),
        ("Updated_Spaces", update_results["updated_count"]),
        ("No_Match_Found", update_results["no_match_count"]),
        ("Errors", update_results["error_count"]),
        ("Success_Rate_%", round((update_results["updated_count"] / float(update_results["matched_count"])) * 100, 1) if update_results["matched_count"] > 0 else 0.0)
    ])
    summary_data.append(summary_row)
    sheets["Summary"] = summary_data
    
    # Updates sheet
    updates_data = []
    for update in update_results["updates"]:
        row_data = [
            ("Element_ID", update["element_id"]),
            ("GUID", update["guid"]),
            ("Old_Name", update["old_name"]),
            ("New_Name", update["new_name"]),
            ("Old_Number", update["old_number"]),
            ("New_Number", update["new_number"]),
            ("Name_Changed", "Yes" if update["name_updated"] else "No"),
            ("Number_Changed", "Yes" if update["number_updated"] else "No")
        ]
        updates_data.append(create_ordered_row(row_data))
    sheets["Updates Applied"] = updates_data
    
    # No matches sheet
    no_matches_data = []
    for no_match in update_results["no_matches"]:
        row_data = [
            ("Element_ID", no_match["element_id"]),
            ("GUID", no_match.get("guid", "")),
            ("Current_Name", no_match["current_name"]),
            ("Current_Number", no_match["current_number"]),
            ("Reason", no_match["reason"])
        ]
        no_matches_data.append(create_ordered_row(row_data))
    sheets["No Matches"] = no_matches_data
    
    # Errors sheet
    errors_data = []
    for error in update_results["errors"]:
        row_data = [
            ("Element_ID", error["element_id"]),
            ("GUID", error.get("guid", "")),
            ("Error", error["error"])
        ]
        errors_data.append(create_ordered_row(row_data))
    sheets["Errors"] = errors_data
    
    return sheets

def main():
    """Main execution function"""
    
    print("=== UPDATE SPACE NAMES AND NUMBERS FROM LINK ===")
    
    # Get linked document
    link_doc = get_link_data(doc)
    if not link_doc:
        alert("No linked document found with identifier: {}".format(MreProjectIdentifier))
        return
    
    print("Found linked document: {}".format(link_doc.Title))
    
    # Get IFC spaces from link
    ifc_spaces = get_ifc_spaces_from_link(link_doc)
    print("Found {} IFC spaces in linked document".format(len(ifc_spaces)))
    
    if len(ifc_spaces) == 0:
        alert("No IFC spaces found in linked document")
        return
    
    # Get Revit spaces from current document
    revit_spaces = get_revit_spaces(doc)
    print("Found {} Revit spaces in current document".format(len(revit_spaces)))
    
    if len(revit_spaces) == 0:
        alert("No Revit spaces found in current document")
        return
    
    # Create GUID mapping from IFC spaces
    ifc_guid_map = create_guid_mapping(ifc_spaces)
    print("Created GUID mapping for {} IFC spaces".format(len(ifc_guid_map)))
    
    # Confirm with user before proceeding
    proceed = alert(
        "Ready to update Revit spaces:\n\n"
        "IFC Spaces: {}\n"
        "Revit Spaces: {}\n"
        "IFC Spaces with GUID: {}\n\n"
        "This will update Name and Number parameters in Revit spaces\n"
        "based on GUID matches with the linked IFC model.\n\n"
        "Do you want to proceed?".format(len(ifc_spaces), len(revit_spaces), len(ifc_guid_map)),
        yes=True, no=True
    )
    
    if not proceed:
        print("Operation cancelled by user")
        return
    
    # Perform the update
    print("\nStarting update process...")
    update_results = update_revit_spaces(revit_spaces, ifc_guid_map)
    
    # Print results
    print("\n=== UPDATE RESULTS ===")
    print("Total Revit Spaces: {}".format(len(revit_spaces)))
    print("Matched by GUID: {}".format(update_results["matched_count"]))
    print("Updated: {}".format(update_results["updated_count"]))
    print("No Match: {}".format(update_results["no_match_count"]))
    print("Errors: {}".format(update_results["error_count"]))
    
    # Generate and save report
    if update_results["updated_count"] > 0 or update_results["no_match_count"] > 0 or update_results["error_count"] > 0:
        save_report = alert(
            "Update completed!\n\n"
            "Updated: {} spaces\n"
            "No match: {} spaces\n"
            "Errors: {} spaces\n\n"
            "Would you like to save a detailed report?".format(
                update_results["updated_count"],
                update_results["no_match_count"],
                update_results["error_count"]
            ),
            yes=True, no=True
        )
        
        if save_report:
            try:
                from DCMvn.io import save_excel_file
                
                export_path = save_excel_file("Save Space Update Report")
                if export_path:
                    # Clean file before save
                    if os.path.exists(export_path):
                        os.remove(export_path)
                    
                    # Generate and save report
                    report_sheets = generate_update_report(update_results, ifc_guid_map)
                    MiniExcel.SaveAs(export_path, report_sheets)
                    
                    # Ask to open folder
                    open_folder = alert(
                        "Report saved successfully to:\n{}\n\nOpen folder?".format(export_path),
                        yes=True, no=True
                    )
                    if open_folder:
                        folder_path = os.path.dirname(export_path)
                        os.startfile(folder_path)
                        
            except Exception as ex:
                alert("Failed to save report: {}".format(str(ex)))
    else:
        alert("No updates were needed. All spaces already have matching names and numbers.")
    
    print("=== OPERATION COMPLETED ===")

if __name__ == "__main__":
    main()
