# coding: utf-8
import clr

from DCMvn.core import DB, HOST_APP, UI
from DCMvn.core.framework import List, System

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

doc = HOST_APP.doc
uidoc = HOST_APP.uidoc

# MreProjectIdentifier = "0061721_MRE"
MreProjectIdentifier = "_AWA_"
IfcSpaceTypeParameter = "Export to IFC As"
IfcSpaceTypeValue = "IfcSpaceType"
# LevelParameter = "AW_ARC.Geschossnummer"
LevelParameter = "IfcDecomposes"
# SpaceNameParameter = "AW_ARC.Raumname"
SpaceNameParameter = "MRE_ARC.Raumname"
# SpaceNumberParameter = "AW_ARC.Raumnummer ARC"
# SpaceNumberParameter = "AW_ARC.Raumnummer"
SpaceNumberParameter = "MRE_ARC.Raunummer Türschild"

def get_link_data(document):
    link_instance = (DB.FilteredElementCollector(document)
                     .OfClass(DB.RevitLinkInstance)
                     .WhereElementIsNotElementType()
                     .FirstOrDefault(lambda x: x.Name.Contains(MreProjectIdentifier)))  # type: DB.RevitLinkInstance
    if link_instance:
        link_doc = link_instance.GetLinkDocument()
        return (link_doc, link_instance)
    return (None, None)

def get_not_generic_spaces(link_instance):
    spaces = (DB.FilteredElementCollector(doc, doc.ActiveView.Id, link_instance.Id)
              .OfCategory(DB.BuiltInCategory.OST_GenericModel)
              .WhereElementIsNotElementType()
              .Where(lambda x: x.LookupParameter(IfcSpaceTypeParameter).AsString() == IfcSpaceTypeValue and x.LookupParameter(LevelParameter).AsString() == doc.ActiveView.GenLevel.Name)
              .Select(lambda x: x.Id)
              .ToList())
    if spaces and len(spaces) > 0:
        all_elements = DB.FilteredElementCollector(doc, doc.ActiveView.Id, link_instance.Id).Excluding(List[DB.ElementId](spaces))
    else:
        all_elements = DB.FilteredElementCollector(doc, doc.ActiveView.Id, link_instance.Id)
    return all_elements


link_doc, link_instance = get_link_data(doc)
if not link_instance or not link_doc:
    spaces = (DB.FilteredElementCollector(doc, doc.ActiveView.Id)
              .OfCategory(DB.BuiltInCategory.OST_GenericModel)
              .WhereElementIsNotElementType()
              .Where(lambda x: x.LookupParameter(IfcSpaceTypeParameter).AsString() == IfcSpaceTypeValue and x.LookupParameter(LevelParameter).AsString() == doc.ActiveView.GenLevel.Name)
              .Select(lambda x: x.Id)
              .ToList())
    if spaces and len(spaces) > 0:
        all_elements = DB.FilteredElementCollector(doc, doc.ActiveView.Id).Excluding(List[DB.ElementId](spaces)).ToElementIds()
    else:
        all_elements = DB.FilteredElementCollector(doc, doc.ActiveView.Id).ToElementIds()
    
    with DB.Transaction(doc, "Hide Elements") as t:
        t.Start()
        # hide all elements
        doc.ActiveView.HideElements(all_elements)
        t.Commit()
    
    
else:   
    all_elements = get_not_generic_spaces(link_instance)
    refs = all_elements.Select(lambda x: DB.Reference(x).CreateLinkReference(link_instance)).ToList()
    uidoc.Selection.SetReferences(refs)
    uidoc.Application.PostCommand(UI.RevitCommandId.LookupPostableCommandId(UI.PostableCommand.HideElements))



