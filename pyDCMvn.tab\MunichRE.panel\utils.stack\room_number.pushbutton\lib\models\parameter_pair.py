# coding: utf-8
from DCMvn.core import DB
from DCMvn.core.framework import Debug  # noqa: F401
from DCMvn.forms.mvvm import ViewModelBase
from .parameter_wrapper import ParameterWrapper # noqa: F401


class ParameterPair(ViewModelBase):
    def __init__(self, source_parameters=None, target_parameters=None):
        # type: (list[ParameterWrapper], list[ParameterWrapper]) -> None
        ViewModelBase.__init__(self)
        self.__source_parameters = source_parameters
        self.__target_parameters = target_parameters
        self.__source_param_index = 0
        self.__target_param_index = 0
        self.__source_param = self.__source_parameters[0] if source_parameters else None
        self.__target_param = self.__target_parameters[0] if target_parameters else None
    
    @property
    def source_param_index(self):
        # type: () -> int
        """Get the source parameter index"""
        return self.__source_param_index
    
    @source_param_index.setter
    def source_param_index(self, value):
        # type: (int) -> None
        if self.__source_param_index != value:
            self.__source_param_index = value
            self.RaisePropertyChanged("source_param_index")
            self.RaisePropertyChanged("source_param_name")
            self.RaisePropertyChanged("source_param_wrapper")
    
    @property
    def target_param_index(self):
        # type: () -> int
        """Get the target parameter index"""
        return self.__target_param_index
    
    @target_param_index.setter
    def target_param_index(self, value):
        # type: (int) -> None
        if self.__target_param_index != value:
            self.__target_param_index = value
            self.RaisePropertyChanged("target_param_index")
            self.RaisePropertyChanged("target_param_name")
            self.RaisePropertyChanged("target_param_wrapper")
    
    @property
    def source_param_wrapper(self):
        # type: () -> ParameterWrapper | None
        """Get the source parameter wrapper from index"""
        if (self.__source_parameters and 
            0 <= self.__source_param_index < len(self.__source_parameters)):
            return self.__source_parameters[self.__source_param_index]
        return None
    
    @property
    def target_param_wrapper(self):
        # type: () -> ParameterWrapper | None
        """Get the target parameter wrapper from index"""
        if (self.__target_parameters and 
            0 <= self.__target_param_index < len(self.__target_parameters)):
            return self.__target_parameters[self.__target_param_index]
        return None
    
    @property
    def source_param_name(self):
        # type: () -> str
        """Get the source parameter name for UI binding"""
        wrapper = self.source_param_wrapper
        return wrapper.name if wrapper else ""
    
    @property
    def target_param_name(self):
        # type: () -> str
        """Get the target parameter name for UI binding"""
        wrapper = self.target_param_wrapper
        return wrapper.name if wrapper else ""
    
    def get_source_value(self, spatial_element, use_string_for_specific=True):
        # type: (DB.Element, bool) -> str | float | int | DB.ElementId | None
        """Get value from source parameter on spatial element
        
        Args:
            spatial_element (DB.Element): The spatial element to get the value from
            use_string_for_specific (bool): Whether to return integer or ElementId as value string

        Returns:
            str | float | int | DB.ElementId | None: The value from the source parameter
        """
        source_wrapper = self.source_param_wrapper
        if not source_wrapper or not spatial_element:
            return None
            
        param = source_wrapper.get_parameter_from_element(spatial_element)
        if not param:
            return None
        if param.StorageType == DB.StorageType.String:
            return param.AsString()
        elif param.StorageType == DB.StorageType.Double:
            return param.AsDouble()
        elif param.StorageType == DB.StorageType.Integer:
            return param.AsInteger() if not use_string_for_specific else param.AsValueString()
        elif param.StorageType == DB.StorageType.ElementId:
            return param.AsElementId() if not use_string_for_specific else param.AsValueString()
        
        return None
    
    def set_target_value(self, target_element, source_value, override_existing=True):
        # type: (DB.Element, object, bool) -> bool
        """Set value on target parameter of target element"""
        target_wrapper = self.target_param_wrapper
        if not target_wrapper or not target_element or source_value is None:
            return False
            
        param = target_wrapper.get_parameter_from_element(target_element)
        if not param or param.IsReadOnly:
            return False
            
        # Check if parameter already has a value and we shouldn't override
        if not override_existing and param.HasValue:
            return False
            
        try:
            if param.StorageType == DB.StorageType.String:
                param.Set(str(source_value) if source_value is not None else "")
                return True
            elif param.StorageType == DB.StorageType.Double:
                if isinstance(source_value, (int, float)):
                    param.Set(float(source_value))
                    return True
                elif isinstance(source_value, str):
                    param.Set(float(source_value))
                    return True
            elif param.StorageType == DB.StorageType.Integer:
                if isinstance(source_value, int):
                    param.Set(source_value)
                    return True
                elif isinstance(source_value, str):
                    param.Set(int(source_value))
                    return True
            elif param.StorageType == DB.StorageType.ElementId:
                if isinstance(source_value, DB.ElementId):
                    param.Set(source_value)
                    return True
        except Exception as e:
            Debug.WriteLine("Error setting target value: {}".format(e))
            return False
            
        return False
    
    def transfer_parameter(self, source_element, target_element, override_existing=True):
        # type: (DB.Element, DB.Element, bool) -> bool
        """Transfer parameter value from source to target element"""
        source_value = self.get_source_value(source_element)
        if source_value is None:
            return False
        return self.set_target_value(target_element, source_value, override_existing)
    
    def is_valid(self):
        # type: () -> bool
        """Check if both source and target parameter wrappers are set"""
        return (self.source_param_wrapper is not None and 
                self.target_param_wrapper is not None)

    def __eq__(self, other):
        # type: (object) -> bool
        if not isinstance(other, ParameterPair):
            return False
        return (self.source_param_wrapper == other.source_param_wrapper and
                self.target_param_wrapper == other.target_param_wrapper)
    
    def __hash__(self):
        # type: () -> int
        """Hash method to support set operations for deduplication"""
        source_hash = hash(self.source_param_name) if self.source_param_name else 0
        target_hash = hash(self.target_param_name) if self.target_param_name else 0
        return hash((source_hash, target_hash))
    
    def __str__(self):
        # type: () -> str
        source_name = self.source_param_name or "None"
        target_name = self.target_param_name or "None"
        return "ParameterPair(Source: '{}' -> Target: '{}')".format(source_name, target_name)
    
    def __repr__(self):
        # type: () -> str
        return self.__str__()
