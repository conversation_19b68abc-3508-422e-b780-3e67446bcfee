import System
import clr

from DCMvn.core.framework import IDictionary
from DCMvn.core import DB, HOST_APP
from DCMvn.coreutils.assemblyhandler import load_miniexcel
from DCMvn.revit.selection import DSelection
from pyrevit import script

load_miniexcel()
from MiniExcelLibs import MiniExcel

from MunichRE.constants import *
import ast

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

# clr.AddReference("RevitNodes")
# import Revit
#
# clr.ImportExtensions(Revit.GeometryConversion)
# clr.ImportExtensions(Revit.Elements)

output = script.get_output()

file_path = r"F:\DIG_GiangVu\workspace\pyDCMvn\pyDCMvn.MunichRE.extension\pyDCMvn.tab\Data\IFC\02_Ifc_HG_HZGA_Placing.xlsx"

excel = MiniExcel.Query(file_path, useHeaderRow=True).Cast[ # noqa
    IDictionary[str, object]]()  # type: IDictionary[str, object]  # noqa

linked_ele = DSelection(is_linked=True).pick()
guid = linked_ele.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
data_excel = excel.FirstOrDefault(lambda x: getattr(x, GLOBAL_ID) == guid)  # noqa

# height = re.sub(r"[^\d.]", "", getattr(data_excel, DUCT_HEIGHT))  # noqa
# width = re.sub(r"[^\d.]", "", getattr(data_excel, DUCT_WIDTH))
nominal_diameter = ast.literal_eval(getattr(data_excel, PIPE_NOMINAL_DIAMETER))[0]
length = getattr(data_excel, PIPE_LENGTH)

origin = ast.literal_eval(getattr(data_excel, CENTROID_MM))
direct_x = ast.literal_eval(getattr(data_excel, DIRECTION_X))
direct_y = ast.literal_eval(getattr(data_excel, DIRECTION_Y))
direct_z = ast.literal_eval(getattr(data_excel, DIRECTION_Z))
half_distance = ast.literal_eval(getattr(data_excel, HALF_DISTANCE_MM))


def to_internal_unit(value):
    return DB.UnitUtils.ConvertToInternalUnits(value, DB.UnitTypeId.Millimeters)


transform = DB.Transform.Identity  # type: DB.Transform
transform.Origin = DB.XYZ(*[to_internal_unit(i) for i in origin])
transform.BasisX = DB.XYZ(*direct_x)
transform.BasisY = DB.XYZ(*direct_y)
transform.BasisZ = DB.XYZ(*direct_z)

bbox = DB.BoundingBoxXYZ()
bbox.Transform = transform
bbox.Min = DB.XYZ(*[-to_internal_unit(i) for i in half_distance])
bbox.Max = DB.XYZ(*[to_internal_unit(i) for i in half_distance])

min_pt = bbox.Min
max_pt = bbox.Max

_pt0 = min_pt
_pt1 = DB.XYZ(max_pt.X, min_pt.Y, min_pt.Z)
_pt2 = DB.XYZ(max_pt.X, max_pt.Y, min_pt.Z)
_pt3 = DB.XYZ(min_pt.X, max_pt.Y, min_pt.Z)
_pt4 = DB.XYZ(min_pt.X, min_pt.Y, max_pt.Z)
_pt5 = DB.XYZ(max_pt.X, min_pt.Y, max_pt.Z)
_pt6 = max_pt
_pt7 = DB.XYZ(min_pt.X, max_pt.Y, max_pt.Z)

edge1 = DB.Line.CreateBound(_pt0, _pt1).CreateTransformed(transform)
edge2 = DB.Line.CreateBound(_pt0, _pt3).CreateTransformed(transform)
edge3 = DB.Line.CreateBound(_pt0, _pt4).CreateTransformed(transform)

edge_dict = {
    _pt1: edge1,
    _pt3: edge2,
    _pt4: edge3
}

group_lenth = edge_dict.items().OrderByDescending(  # noqa
    lambda x: abs(x[1].Length - length)).ToList()
cross_points = group_lenth.Take(2).Select(lambda x: x[0]).ToList()
side_edges = group_lenth.Take(2).Select(lambda x: x[1]).ToList()
direction_edge = group_lenth.Last()[1]

edge = DB.Line.CreateBound(cross_points[0], cross_points[1]).CreateTransformed(transform)
start_point = transform.Origin + direction_edge.Direction * direction_edge.Length / 2
end_point = transform.Origin - direction_edge.Direction * direction_edge.Length / 2

pipe_type_id = DB.ElementId(99508)
levelid = DB.FilteredElementCollector(HOST_APP.doc).OfClass(DB.Level).FirstOrDefault(lambda x: x.Name == getattr(data_excel, STOREY_NAME)).Id
systemTypeid = DB.FilteredElementCollector(HOST_APP.doc).OfCategory(DB.BuiltInCategory.OST_PipingSystem).FirstElementId()
with DB.Transaction(HOST_APP.doc, "Create Pipe") as t:
    t.Start()
    pipe_ = DB.Plumbing.Pipe.Create(HOST_APP.doc, systemTypeid, pipe_type_id, levelid,
                                    start_point, end_point)

    pipe_.get_Parameter(DB.BuiltInParameter.RBS_PIPE_DIAMETER_PARAM).Set(to_internal_unit(nominal_diameter))
    pipe_.get_Parameter(DB.BuiltInParameter.IFC_GUID).Set(guid)
    t.Commit()
print(direction_edge.Direction)
print(start_point)
print(end_point)
print(getattr(data_excel, GLOBAL_ID))
print(getattr(data_excel, PIPE_NOMINAL_DIAMETER))
print(getattr(data_excel, PIPE_LENGTH))
print(getattr(data_excel, STOREY_NAME))
