# coding: utf-8
from DCMvn.core import DB, UI, HOST_APP
from DCMvn.core.framework import Debug

class ActionEventHandler(UI.IExternalEventHandler):
    """Thread-safe handler for executing Revit API operations."""
    
    def __init__(self):
        self._external_event = UI.ExternalEvent.Create(self)
        self._action = None
        
    def GetName(self):
        """Return handler name for debugging."""
        return "ActionEventHandler"
        
    def Execute(self, application):
        """Execute queued action on Revit's main thread."""
        if self._action is None:
            return
            
        try:
            self._action(application)
        except Exception as e:
            Debug.WriteLine("External event execution error: {}".format(e))
        finally:
            self._action = None
            
    def Raise(self, action):
        """Queue action for safe execution."""
        # Direct execution if already in API context
        if HOST_APP.uiapp.ActiveAddInId is not None:
            try:
                action(HOST_APP.uiapp)
                return
            except Exception as e:
                Debug.WriteLine("Direct API execution error: {}".format(e))
                return
                
        # Queue for external event execution
        if self._action is None:
            self._action = action
        else:
            # Combine with existing action
            previous_action = self._action
            def combined_action(ui_app):
                try:
                    previous_action(ui_app)
                    action(ui_app)
                except Exception as e:
                    Debug.WriteLine("Combined action error: {}".format(e))
            self._action = combined_action
            
        self._external_event.Raise()
        
    def Cancel(self):
        """Cancel pending action."""
        self._action = None
        
    def Dispose(self):
        """Clean up resources."""
        if self._external_event:
            self._external_event.Dispose()
            self._external_event = None

def execute_with_transaction(doc, transaction_name, operation):
    """Execute operation within a safe transaction."""
    try:
        with DB.Transaction(doc, transaction_name) as trans:
            trans.Start()
            result = operation(doc)
            trans.Commit()
            return result
    except Exception as e:
        Debug.WriteLine("Transaction error in {}: {}".format(transaction_name, e))
        return None