from System.Windows import MessageBox  # noqa
from System.Windows.Input import Key, ModifierKeys, Keyboard  # noqa

from DCMvn.core.framework import Clipboard, Controls
from DCMvn.forms.wpfforms import WPFWindow
from search_elements_viewmodel import SearchElementsViewModel

class SearchElements(WPFWindow):
    def __init__(self, xaml_path, view_model):
        # type: (str, SearchElementsViewModel) -> None
        WPFWindow.__init__(self, xaml_source=xaml_path, handle_esc=False)
        self.DataContext = view_model
        self.GuidInputTextBox.PreviewKeyDown += self.TextBox_PreviewKeyDown

    def TextBox_PreviewKeyDown(self, sender, e):  # noqa
        if e.Key == Key.V and (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control:
            if isinstance(sender, Controls.TextBox):  # noqa
                clipboard_text = Clipboard.GetText()
                if clipboard_text:
                    processed_text = clipboard_text.replace('\t', '\n')
                    sender.SelectedText = processed_text
                    e.Handled = True
