# Pre-commit configuration for pyDCMvn.MunichRE
# See https://pre-commit.com for more information
# See https://docs.astral.sh/uv/guides/integration/pre-commit/ for uv integration

repos:
  # uv hooks for dependency management
  - repo: https://github.com/astral-sh/uv-pre-commit
    # uv version - update this to match your uv version
    rev: 0.8.9
    hooks:
      # Keep uv.lock file up to date when pyproject.toml changes
      - id: uv-lock
      
      # Keep requirements.txt in sync with uv.lock
      - id: uv-export
        args: [--format, requirements-txt, --output-file, requirements.txt]
      
      # Keep simple requirements.txt in sync (without hashes)
      # - id: uv-export
      #   name: uv-export-simple
      #   args: [--format, requirements-txt, --no-hashes, --output-file, requirements-simple.txt]

  # Jupyter notebook output clearing
  - repo: local
    hooks:
      - id: jupyter-nb-clear-output
        name: Clear Jupyter notebook outputs
        entry: uv run jupyter nbconvert --clear-output --inplace
        language: system
        files: \.ipynb$
        require_serial: false
