# coding: utf-8
"""
Spatial Geometry Tests (Tests 1-4)
Tests for spatial element geometry, bounding boxes, offsets, and center points.
"""
import clr
from DCMvn.core import DB
from DCMvn.core.framework import Debug, Trace
from DCMvn.forms import alert
from tests.test_utils import (TestOptions, SpatialElementSelector, SpatialWrapperFactory, 
                             GeometryHelper, TestLogger)

clr.AddReference("System.Core")


def test_01_trace_linked_spatial_geometry():
    """
    Test 1: Select linked spatial (Generic, Room/Space) then Trace geometry.
    Visualizes the raw geometry of a selected linked spatial element.
    """
    TestLogger.log_test_start("01 - Trace Linked Spatial Geometry")
    
    try:
        # Select linked spatial element
        alert("Test 1: Select a linked spatial element to trace its geometry")
        spatial_element, link_instance, doc_wrapper = SpatialElementSelector.pick_linked_spatial()
        
        if not spatial_element:
            TestLogger.log_test_end("01 - Trace Linked Spatial Geometry", success=False)
            return
        
        # Log element information
        TestLogger.log_element_info(spatial_element, "Linked Spatial Element")
        TestLogger.log_element_info(link_instance, "Link Instance")
        
        # Create spatial wrapper
        spatial_wrapper = SpatialWrapperFactory.create_spatial_wrapper(spatial_element, doc_wrapper)
        if not spatial_wrapper:
            Debug.WriteLine("Failed to create spatial wrapper")
            TestLogger.log_test_end("01 - Trace Linked Spatial Geometry", success=False)
            return
        
        # Get and trace geometry
        geometry = spatial_wrapper.geometry
        TestLogger.log_geometry_info(geometry, "Spatial Element Geometry")
        
        # Additional geometry details
        if geometry:
            if hasattr(geometry, 'Volume'):
                Debug.WriteLine("  Volume: {0:.2f} cubic feet".format(geometry.Volume))
            if hasattr(geometry, 'SurfaceArea'):
                Debug.WriteLine("  Surface Area: {0:.2f} square feet".format(geometry.SurfaceArea))
        
        alert("Test 1 Completed!\nSpatial geometry has been traced.\nCheck Output window for details.")
        TestLogger.log_test_end("01 - Trace Linked Spatial Geometry", success=True)
        
    except Exception as e:
        Debug.WriteLine("Error in test_01: {0}".format(str(e)))
        alert("Test 1 Failed: {0}".format(str(e)))
        TestLogger.log_test_end("01 - Trace Linked Spatial Geometry", success=False)


def test_02_trace_linked_spatial_bounding_box():
    """
    Test 2: Select linked spatial (Generic, Room/Space) then Trace bounding box.
    Visualizes the bounding box of a selected linked spatial element.
    """
    TestLogger.log_test_start("02 - Trace Linked Spatial Bounding Box")
    
    try:
        # Select linked spatial element
        alert("Test 2: Select a linked spatial element to trace its bounding box")
        spatial_element, link_instance, doc_wrapper = SpatialElementSelector.pick_linked_spatial()
        
        if not spatial_element:
            TestLogger.log_test_end("02 - Trace Linked Spatial Bounding Box", success=False)
            return
        
        # Log element information
        TestLogger.log_element_info(spatial_element, "Linked Spatial Element")
        
        # Create spatial wrapper
        spatial_wrapper = SpatialWrapperFactory.create_spatial_wrapper(spatial_element, doc_wrapper)
        if not spatial_wrapper:
            Debug.WriteLine("Failed to create spatial wrapper")
            TestLogger.log_test_end("02 - Trace Linked Spatial Bounding Box", success=False)
            return
        
        # Get and trace bounding box
        bbox = spatial_wrapper.bounding_box
        TestLogger.log_bounding_box_info(bbox, "Spatial Element Bounding Box")
        
        # Additional bounding box calculations
        if bbox:
            volume = (bbox.Max.X - bbox.Min.X) * (bbox.Max.Y - bbox.Min.Y) * (bbox.Max.Z - bbox.Min.Z)
            Debug.WriteLine("  Bounding Box Volume: {0:.2f} cubic feet".format(volume))
        
        alert("Test 2 Completed!\nBounding box has been traced.\nCheck Output window for details.")
        TestLogger.log_test_end("02 - Trace Linked Spatial Bounding Box", success=True)
        
    except Exception as e:
        Debug.WriteLine("Error in test_02: {0}".format(str(e)))
        alert("Test 2 Failed: {0}".format(str(e)))
        TestLogger.log_test_end("02 - Trace Linked Spatial Bounding Box", success=False)


def test_03_trace_linked_spatial_expanded_bounding_box():
    """
    Test 3: Select linked spatial then trace bounding box with offset 300mm side + top.
    Shows expanded bounding box for proximity detection.
    """
    TestLogger.log_test_start("03 - Trace Expanded Bounding Box (300mm offset)")
    
    try:
        options = TestOptions()
        
        # Select linked spatial element
        alert("Test 3: Select a linked spatial element for expanded bounding box (300mm offset)")
        spatial_element, link_instance, doc_wrapper = SpatialElementSelector.pick_linked_spatial()
        
        if not spatial_element:
            TestLogger.log_test_end("03 - Trace Expanded Bounding Box", success=False)
            return
        
        # Log element information
        TestLogger.log_element_info(spatial_element, "Linked Spatial Element")
        
        # Create spatial wrapper
        spatial_wrapper = SpatialWrapperFactory.create_spatial_wrapper(spatial_element, doc_wrapper)
        if not spatial_wrapper:
            Debug.WriteLine("Failed to create spatial wrapper")
            TestLogger.log_test_end("03 - Trace Expanded Bounding Box", success=False)
            return
        
        # Get original bounding box
        original_bbox = spatial_wrapper.bounding_box
        TestLogger.log_bounding_box_info(original_bbox, "Original Bounding Box")
        
        if not original_bbox:
            Debug.WriteLine("No bounding box available for expansion")
            TestLogger.log_test_end("03 - Trace Expanded Bounding Box", success=False)
            return
        
        # Create expanded bounding box
        expanded_outline = GeometryHelper.create_expanded_outline(
            original_bbox, 
            options.proximity_distance_internal
        )
        
        if expanded_outline:
            # Convert outline back to bounding box for tracing
            expanded_bbox = DB.BoundingBoxXYZ()
            expanded_bbox.Min = expanded_outline.MinimumPoint
            expanded_bbox.Max = expanded_outline.MaximumPoint
            
            TestLogger.log_bounding_box_info(expanded_bbox, "Expanded Bounding Box (300mm offset)")
            
            # Show size differences
            original_volume = ((original_bbox.Max.X - original_bbox.Min.X) * 
                             (original_bbox.Max.Y - original_bbox.Min.Y) * 
                             (original_bbox.Max.Z - original_bbox.Min.Z))
            expanded_volume = ((expanded_bbox.Max.X - expanded_bbox.Min.X) * 
                             (expanded_bbox.Max.Y - expanded_bbox.Min.Y) * 
                             (expanded_bbox.Max.Z - expanded_bbox.Min.Z))
            
            Debug.WriteLine("Volume Comparison:")
            Debug.WriteLine("  Original: {0:.2f} cubic feet".format(original_volume))
            Debug.WriteLine("  Expanded: {0:.2f} cubic feet".format(expanded_volume))
            Debug.WriteLine("  Increase: {0:.1f}%".format((expanded_volume - original_volume) / original_volume * 100))
            
            # Trace the expanded outline
            Trace.Write(expanded_outline)
        
        alert("Test 3 Completed!\nExpanded bounding box (300mm) traced.\nCheck Output window for details.")
        TestLogger.log_test_end("03 - Trace Expanded Bounding Box", success=True)
        
    except Exception as e:
        Debug.WriteLine("Error in test_03: {0}".format(str(e)))
        alert("Test 3 Failed: {0}".format(str(e)))
        TestLogger.log_test_end("03 - Trace Expanded Bounding Box", success=False)


def test_04_trace_linked_spatial_center_point():
    """
    Test 4: Select linked spatial (Generic, Room/Space) then trace center point.
    Calculates and visualizes the center point of a spatial element.
    """
    TestLogger.log_test_start("04 - Trace Linked Spatial Center Point")
    
    try:
        # Select linked spatial element
        alert("Test 4: Select a linked spatial element to trace its center point")
        spatial_element, link_instance, doc_wrapper = SpatialElementSelector.pick_linked_spatial()
        
        if not spatial_element:
            TestLogger.log_test_end("04 - Trace Linked Spatial Center Point", success=False)
            return
        
        # Log element information
        TestLogger.log_element_info(spatial_element, "Linked Spatial Element")
        
        # Create spatial wrapper
        spatial_wrapper = SpatialWrapperFactory.create_spatial_wrapper(spatial_element, doc_wrapper)
        if not spatial_wrapper:
            Debug.WriteLine("Failed to create spatial wrapper")
            TestLogger.log_test_end("04 - Trace Linked Spatial Center Point", success=False)
            return
        
        # Get center point using helper
        from lib.utils import get_element_center_point
        center_point = get_element_center_point(spatial_wrapper)
        
        if center_point:
            Trace.Write(center_point)    
        else:
            Debug.WriteLine("Could not calculate center point")
        
        alert("Test 4 Completed!\nCenter point has been traced.\nCheck Output window for details.")
        TestLogger.log_test_end("04 - Trace Linked Spatial Center Point", success=True)
        
    except Exception as e:
        Debug.WriteLine("Error in test_04: {0}".format(str(e)))
        alert("Test 4 Failed: {0}".format(str(e)))
        TestLogger.log_test_end("04 - Trace Linked Spatial Center Point", success=False)


# Test runner for spatial geometry tests
def run_spatial_geometry_tests():
    """Run all spatial geometry tests (1-4)."""
    Debug.WriteLine("\n" + "=" * 60)
    Debug.WriteLine("RUNNING SPATIAL GEOMETRY TESTS (1-4)")
    Debug.WriteLine("=" * 60)
    
    tests = [
        ("Test 1 - Trace Geometry", test_01_trace_linked_spatial_geometry),
        ("Test 2 - Trace Bounding Box", test_02_trace_linked_spatial_bounding_box),
        ("Test 3 - Trace Expanded Bounding Box", test_03_trace_linked_spatial_expanded_bounding_box),
        ("Test 4 - Trace Center Point", test_04_trace_linked_spatial_center_point)
    ]
    
    alert("Starting Spatial Geometry Tests\n\nYou will be prompted to select elements for each test.\nTests 1-4 will run sequentially.")
    
    for test_name, test_function in tests:
        try:
            Debug.WriteLine("\n{0}".format("=" * 40))
            Debug.WriteLine("Preparing: {0}".format(test_name))
            Debug.WriteLine("=" * 40)
            test_function()
        except Exception as e:
            Debug.WriteLine("Failed to run {0}: {1}".format(test_name, str(e)))
    
    Debug.WriteLine("\n" + "=" * 60)
    Debug.WriteLine("SPATIAL GEOMETRY TESTS COMPLETED")
    Debug.WriteLine("=" * 60)
    alert("All Spatial Geometry Tests Completed!\nCheck Output window for detailed results.")


# Individual test runners for manual execution
def run_test_01():
    """Run only Test 1."""
    test_01_trace_linked_spatial_geometry()

def run_test_02():
    """Run only Test 2."""
    test_02_trace_linked_spatial_bounding_box()

def run_test_03():
    """Run only Test 3."""
    test_03_trace_linked_spatial_expanded_bounding_box()

def run_test_04():
    """Run only Test 4."""
    test_04_trace_linked_spatial_center_point()