# coding: utf-8
from System.Windows import Clipboard, MessageBox  # noqa: F401
from System.Windows.Controls import TextBox
from System.Windows.Input import Key, ModifierKeys, Keyboard  # noqa: F401
from DCMvn.forms.wpfforms import WPFWindow

from .geometryalignment_viewmodel import GeometryAlignmentViewModel


class GeometryAlignmentView(WPFWindow):
    def __init__(self, xaml_path, view_model):
        # type: (str, GeometryAlignmentViewModel) -> None
        WPFWindow.__init__(self, xaml_path)
        self.DataContext = view_model
        self.GuidsInputTextBox.PreviewKeyDown += self.TextBox_PreviewKeyDown

    def TextBox_PreviewKeyDown(self, sender, e):  # noqa
        if e.Key == Key.V and (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control:
            if isinstance(sender, TextBox):
                clipboard_text = Clipboard.GetText()
                if clipboard_text:
                    # Process the clipboard text to replace tabs with new lines
                    processed_text = clipboard_text.replace('\t', '\n')
                    sender.SelectedText = processed_text
                    e.Handled = True



