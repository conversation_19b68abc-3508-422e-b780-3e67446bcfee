**Introduce**

- **Purpose**: Spatial Mapping Tool maps spatial parameters (e.g., room/space numbers) from source spatial elements to target MEP elements based on geometric spatial relationships. It supports native Revit Rooms/Spaces, IFC/DirectShape spatial elements, and multiple MEP element types.
- **Input**:
  - Source selection: document (host or link/IFC) and spatial category (Rooms, Spaces, Generic/IFC)
  - Target selection: one or more MEP categories (e.g., Duct, Pipe, Cable Tray, equipment families)
  - Configuration: advanced options (above-space offset, proximity mapping, multiple values, overrides, logging)
- **Output**:
  - Parameter assignments on target MEP elements based on detected spatial relationships
  - Optional detailed HTML report with metrics

**How to use the tool**
1. Select the source document and spatial category. The tool collects spatial elements automatically and discovers available source parameters.
2. Select target MEP categories and (optionally) filter to active view only. The tool groups and counts target elements, and discovers applicable target parameters.
3. Configure parameter mappings. Use the suggested defaults or add/remove mappings. Only valid and de-duplicated pairs are applied.
4. Open Advanced Options to set detection behavior (above-space distance, proximity mapping distance, override rules, multiple values, logging).
5. Run the mapping. The tool executes thread-safely via external events and Revit transactions, then shows results and optional HTML report.

**Algorithm**

- **Detection architecture**:
  - Strategy pattern with two strategies:
    - RevitSpatialStrategy for native Rooms/Spaces (uses IsPointInRoom/IsPointInSpace APIs)
    - GenericSpatialStrategy for IFC/DirectShape (bounding box + mesh/proximity/ray casting)
  - Ten core scenarios cover 5 MEP element types × 2 spatial types (Revit vs Generic)
- **Progressive pipeline** (early-exit on first success):
  - Room calculation points → direct point-in-spatial → bounding box containment → above-space Z-offset check → proximity geometry (circle/rectangle) → ray casting (for generic)
- **Parameter application**:
  - Validated `ParameterPair` objects ensure name/type compatibility; duplicates skipped
  - Multiple values optionally joined with a separator; overrides configurable
- **Thread safety & transactions**: All writes are marshalled through an external event handler and wrapped in a Revit `Transaction`.

**Limitations**
- Accuracy near boundaries depends on available geometry and Revit API point-in-room/space behavior.
- IFC/DirectShape detection may be slower on very complex meshes; proximity and ray casting costs rise with geometry complexity.
- Parameter type compatibility is enforced; non-string targets generally require matching storage types.
- Correct results assume valid transforms for linked documents and well-formed element geometry/bounding boxes.

**Product**

- **Link of Tool**: https://dcm-vn.com/index.php/f/1390054
- **Test Environment**:
  - Test suite: use Shift+Right Click on the tool icon to execute the test suite
  - Model: O1--_ASS_440_5-_FA-_K0_0000_-_VA - Munich RE
- **Link of Introduce Video**: https://dcm-vn.com/index.php/f/1390054

