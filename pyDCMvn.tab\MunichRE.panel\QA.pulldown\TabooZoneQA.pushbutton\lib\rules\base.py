# coding: utf-8
"""
Rule framework for TabooZone QA

Provides a lightweight, plugin-like registry so new rules can be added
without modifying the core workflow.
"""
from DCMvn.core import DB
from DCMvn.core.framework import Debug


class TabooRule(object):
    """Base class for TabooZone QA rules.

    Subclasses should set:
      - rule_id: stable identifier (e.g., "O1_BEAMS_U1U2")
      - display_name: human-friendly name

    And implement:
      - collect_arc_elements(document_or_context)
      - collect_mass_elements(document_or_context)

    Optionally override:
      - arc_guid_param_spec()
      - mass_guid_param_spec()
      - tolerance_mm()  # default centroid tolerance in mm
      - extra_excel_columns()  # dynamic columns for reporting
    """
    rule_id = "BASE"
    display_name = "Base Rule"

    def collect_arc_elements(self, arrc_doc):  # type: (DB.Document) -> list[DB.Element]
        raise NotImplementedError

    def collect_mass_elements(self, mass_doc):  # type: (DB.Document) -> list[DB.Element]
        raise NotImplementedError

    @property
    def arc_guid_param_spec(self):  # type: () -> DB.BuiltInParameter | DB.Definition | str
        """Return parameter spec for ARC elements' GUIDs."""
        return DB.BuiltInParameter.IFC_GUID

    @property
    def mass_guid_param_spec(self):  # type: () -> DB.BuiltInParameter | DB.Definition | str
        """Return parameter spec for MASS elements' GUIDs."""
        return DB.BuiltInParameter.IFC_GUID

    @property
    def tolerance_mm(self):  # type: () -> float
        return 0.0

    @property
    def extra_excel_columns(self):  # type: () -> dict[str, str]
        """Return mapping {column_name: callable(row_ctx) -> value}.
        Row context will include DTOs; see ExcelReporter for details.
        """
        return {}


class RuleRegistry(object):
    """Global registry of TabooRule classes."""
    _rules = {}

    @classmethod
    def register(cls, rule_cls):
        try:
            if not hasattr(rule_cls, "rule_id"):
                raise ValueError("Rule class must define rule_id")
            rid = rule_cls.rule_id
            cls._rules[rid] = rule_cls
            Debug.WriteLine("Successfully registered rule: {} -> {}".format(rid, rule_cls.__name__))
            return rule_cls
        except Exception as ex:
            Debug.WriteLine("RuleRegistry.register error: {}".format(str(ex)))
            return rule_cls

    @classmethod
    def get(cls, rule_id):
        return cls._rules.get(rule_id)

    @classmethod
    def all_rules(cls):
        return dict(cls._rules)


def register_rule(rule_cls):
    """Decorator to register a TabooRule subclass."""
    return RuleRegistry.register(rule_cls)

