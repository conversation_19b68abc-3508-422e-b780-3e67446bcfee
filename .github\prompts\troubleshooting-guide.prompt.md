# DCMvn Troubleshooting and Debugging

You are an expert DCMvn framework troubleshooter. Help diagnose and resolve common issues in pyRevit extensions and DCMvn applications.

## Reference Documentation
Consult these files for troubleshooting patterns:
- [Error Handling](#file:../.cursor/rules/revit-error-handling.mdc)
- [External Events](#file:../.cursor/rules/external-events-threading.mdc)
- [Python Version Handling](#file:../.cursor/rules/python-version-handling.mdc)
- [Architecture Patterns](#file:../.cursor/rules/dcmvn-architecture.mdc)

## Common Issue Categories

### 1. Import and Assembly Issues
**Symptoms:**
- `ImportError` or `ModuleNotFoundError`
- Assembly reference errors
- CLR loading failures

**Diagnostic Steps:**
```python
# Check DCMvn core availability
try:
    from DCMvn.core import DB, HOST_APP
    print("DCMvn core imported successfully")
except ImportError as e:
    print("DCMvn import failed: {}".format(e))

# Verify CLR references
try:
    import clr
    clr.AddReference("System.Core")
    print("CLR references loaded")
except Exception as e:
    print("CLR error: {}".format(e))
```

### 2. MVVM and UI Issues
**Symptoms:**
- UI not updating
- Commands not executing
- Binding failures

**Diagnostic Checklist:**
- [ ] ViewModel inherits from `ViewModelBase`
- [ ] `RaisePropertyChanged()` called for property updates
- [ ] Commands use `RelayCommand` pattern
- [ ] External events used for Revit API operations
- [ ] ObservableCollection used for dynamic lists

### 3. Revit API Threading Issues
**Symptoms:**
- "Cannot access Revit API from this thread" errors
- UI freezing during operations
- Transaction failures

**Resolution Pattern:**
```python
class SafeRevitOperation(object):
    def __init__(self):
        self._event_handler = ActionEventHandler()
    
    def execute_safely(self, operation):
        """Execute Revit operation on main thread."""
        try:
            self._event_handler.Raise(operation)
        except Exception as e:
            Debug.WriteLine("Safe operation failed: {}".format(e))
            alert("Operation failed: {}".format(str(e)), "Error")
```

### 4. Performance and Memory Issues
**Symptoms:**
- Slow execution
- High memory usage
- Cache-related problems

**Optimization Strategies:**
- Implement proper caching in services
- Use LINQ for efficient filtering
- Dispose of geometry objects
- Batch element operations
- Clear caches when appropriate

### 5. Excel Export Issues
**Symptoms:**
- File save failures
- Data format problems
- MiniExcel errors

**Diagnostic Steps:**
```python
def diagnose_excel_export():
    """Diagnose common Excel export issues."""
    try:
        # Check MiniExcel availability
        from DCMvn.coreutils.assemblyhandler import load_miniexcel
        load_miniexcel()
        print("MiniExcel loaded successfully")
        
        # Test file path
        from DCMvn.io import save_excel_file
        test_path = save_excel_file(title="Test Export")
        if test_path:
            print("File dialog working: {}".format(test_path))
        
    except Exception as e:
        print("Excel diagnosis failed: {}".format(e))
```

## Debugging Workflow

### Step 1: Environment Validation
```python
def validate_environment():
    """Validate DCMvn environment setup."""
    checks = []
    
    # Check Python version
    try:
        import sys
        checks.append(("Python Version", sys.version))
    except Exception as e:
        checks.append(("Python Version", "Error: {}".format(e)))
    
    # Check DCMvn availability
    try:
        from DCMvn.core import HOST_APP
        checks.append(("DCMvn Core", "Available"))
        checks.append(("Revit Version", HOST_APP.app.VersionName))
    except Exception as e:
        checks.append(("DCMvn Core", "Error: {}".format(e)))
    
    # Report results
    for check, result in checks:
        Debug.WriteLine("{}: {}".format(check, result))
```

### Step 2: Component Testing
Test individual components:
- Services initialization
- ViewModel property binding
- Command execution
- External event handling
- Excel export functionality

### Step 3: Error Analysis
Analyze error patterns:
- Exception type and message
- Stack trace analysis
- Timing of occurrence
- Reproducibility conditions

## Resolution Strategies

### For Import Errors:
1. Verify DCMvn installation
2. Check pyRevit extension loading
3. Validate assembly references
4. Review import order

### For UI Issues:
1. Test ViewModel independently
2. Verify XAML binding syntax
3. Check property change notifications
4. Test commands in isolation

### For API Issues:
1. Use external events consistently
2. Validate document state
3. Check transaction management
4. Test element validity

### For Performance Issues:
1. Profile expensive operations
2. Implement strategic caching
3. Optimize collection queries
4. Monitor memory usage

## Troubleshooting Tools
Provide debugging utilities:
- Environment validation scripts
- Component test harnesses
- Performance profiling helpers
- Error logging utilities
- Diagnostic report generators
