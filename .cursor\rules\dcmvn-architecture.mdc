---
description: DCMvn architecture patterns and MVVM implementation for pyRevit applications
alwaysApply: true
---

# DCMvn Architecture Patterns

## Core Framework Integration
- **Primary imports**: `from DCMvn.core import DB, HOST_APP, get_output`
- **Framework utilities**: `from DCMvn.core.framework import System, List, Debug, Trace, ObservableCollection`
- **MVVM base classes**: `from DCMvn.forms.mvvm import ViewModelBase, RelayCommand`
- **User notifications**: `from DCMvn.forms import alert`
- **CLR setup**: Always add `clr.AddReference("System.Core")` and `clr.ImportExtensions(System.Linq)`

## File Headers and Compatibility
- **IronPython 2.7 (default)**: `# coding: utf-8`
- **Python 3 (when needed)**: `#! python3` (no coding declaration needed)
- Follow pyRevit documentation for engine specifications

## MVVM Architecture Implementation
- **ViewModels**: Inherit from `ViewModelBase`, coordinate services and UI state
- **Commands**: Use `RelayCommand(execute_method, can_execute_method)` pattern
- **Collections**: Use `ObservableCollection[T]()` for UI-bound data
- **Property updates**: Always call `RaisePropertyChanged("property_name")`
- **Thread safety**: Use external events for Revit API operations
- **Service injection**: Pass document and services through constructor

## Service Layer Pattern
- **CollectorService**: Element collection and filtering with caching
- **DetectionService**: Spatial relationship and geometric calculations
- **ReportService**: Data processing and output generation
- Initialize services with document reference in ViewModels

[dcmvn-viewmodel-template.py](mdc:dcmvn-viewmodel-template.py)
[dcmvn-command-template.py](mdc:dcmvn-command-template.py)