# coding: utf-8
import clr
import System

from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.revit.transaction import transaction_wrapper
from DCMvn.revit.query import get_name
from pyrevit import forms, script

from MunichRE.excel_reader import select_ifc_data, save_failed_report_to_excel
from MunichRE.geometry.pipe import PipeSegment
from MunichRE.geometry.duct import DuctSegment
from MunichRE.geometry.cabletray import CableTraySegment
from MunichRE.constants import RVT_CATEGORY, GLOBAL_ID, IFC_ELEMENT, ELEMENT_NAME

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

output = get_output()
logger = script.get_logger()
DUCT_CATEGORY = "Ducts"
PIPE_CATEGORY = "Pipes"
CABLETRAY_CATEGORY = "Cable Trays"
ACEPTED_CATEGORIES = [DUCT_CATEGORY, PIPE_CATEGORY, CABLETRAY_CATEGORY]


def get_type_dict(builtin_cate):
    curve_types = DB.FilteredElementCollector(HOST_APP.doc).OfCategory(
        builtin_cate).WhereElementIsElementType().ToList()
    if builtin_cate == DB.BuiltInCategory.OST_CableTray:
        cable_dict = {}
        for i in curve_types:
            family_name = i.get_Parameter(DB.BuiltInParameter.SYMBOL_FAMILY_NAME_PARAM).AsString()
            type_name = i.get_Parameter(DB.BuiltInParameter.SYMBOL_NAME_PARAM).AsString()
            cable_dict[family_name + ": " + type_name] = i.Id
        return cable_dict
    return {get_name(i): i.Id for i in curve_types}


def get_pipe_segment_types(type_dict):
    type_size_dict = {}
    for name, type_id in type_dict.items():
        pipe_type = HOST_APP.doc.GetElement(type_id)  # type: DB.MEPCurveType
        manager = pipe_type.RoutingPreferenceManager  # type: DB.RoutingPreferenceManager

        total_segments = manager.GetNumberOfRules(DB.RoutingPreferenceRuleGroupType.Segments)

        segments = []
        for i in range(total_segments):
            segment = manager.GetRule(DB.RoutingPreferenceRuleGroupType.Segments,
                                      index=i)  # type: DB.RoutingPreferenceRule
            segments.append(segment)

        nominal_sizes = set()
        for segment in segments:
            segment_type = segment.MEPPartId  # type: DB.ElementId
            segment_element = HOST_APP.doc.GetElement(segment_type)  # type: DB.Plumbing.PipeSegment  # noqa
            sizes = segment_element.GetSizes().Select(lambda x: x.NominalDiameter).ToList()
            nominal_sizes.update(sizes)

        type_size_dict[name] = nominal_sizes

    return type_size_dict


def get_level_dict():
    levels = DB.FilteredElementCollector(HOST_APP.doc).OfClass(DB.Level).ToElements()
    level_dict = {get_name(i): i.Id for i in levels}
    return level_dict


def try_get_value(obj, key, error_message):
    try:
        return obj[key]
    except KeyError:
        raise ValueError(error_message)


def find_mep_curve(expanded_object):
    cate = getattr(expanded_object, RVT_CATEGORY)
    if cate == DUCT_CATEGORY:
        return DuctSegment(expanded_object)
    elif cate == PIPE_CATEGORY:
        return PipeSegment(expanded_object)
    elif cate == CABLETRAY_CATEGORY:
        return CableTraySegment(expanded_object)


# FIXME: This script is not working as expected, for specific reason of IFC geometry data
@transaction_wrapper("Create MEP Curve")
def main():
    # Data from Excel
    selected_data = select_ifc_data()
    excel_path = selected_data.excel_path
    suffix = excel_path.split("\\")[-1].split(".")[0]
    flow_segment_objects = list(selected_data.data.Where(lambda x: getattr(x, RVT_CATEGORY) in ACEPTED_CATEGORIES))

    # Level
    level_dict = get_level_dict()

    # Duct
    duct_type_dict = get_type_dict(DB.BuiltInCategory.OST_DuctCurves)
    duct_system_type_dict = get_type_dict(DB.BuiltInCategory.OST_DuctSystem)

    # Cable Tray
    cabletray_type_dict = get_type_dict(DB.BuiltInCategory.OST_CableTray)

    # Pipe
    pipe_system_type_dict = get_type_dict(DB.BuiltInCategory.OST_PipingSystem)
    pipe_type_dict = get_type_dict(DB.BuiltInCategory.OST_PipeCurves)
    segment_dict = get_pipe_segment_types(pipe_type_dict)

    # create mepcurves
    mepcurves_failed = []

    max_value = len(flow_segment_objects)
    with forms.ProgressBar(cancellable=True) as pb:
        for counter in range(0, max_value):
            flow_object = flow_segment_objects[counter]
            try:
                mepcure = find_mep_curve(flow_object)
                if not mepcure:
                    continue
                levelid = level_dict[mepcure.ifc_level]

                if isinstance(mepcure, PipeSegment):
                    systemTypeid = try_get_value(pipe_system_type_dict, mepcure.rvt_system,
                                                 "Column RVT.SystemType is empty")
                    pipe_typeId = try_get_value(pipe_type_dict, mepcure.rvt_type, "Pipe Type is not defined")
                    nominal_diameter_by_type = try_get_value(segment_dict, mepcure.rvt_type, "Pipe Type is not defined")
                    mepcure.create_pipe(systemTypeid, pipe_typeId, nominal_diameter_by_type, levelid)
                elif isinstance(mepcure, DuctSegment):
                    systemTypeid = try_get_value(duct_system_type_dict, mepcure.rvt_system,
                                                 "Column RVT.SystemType is empty")
                    duct_typeid = try_get_value(duct_type_dict, mepcure.rvt_type, "Duct Type is not defined")
                    mepcure.create_duct(systemTypeid, duct_typeid, levelid)
                elif isinstance(mepcure, CableTraySegment):
                    cabletray_family = mepcure.rvt_family
                    cabletray_type = mepcure.rvt_type
                    cabletray_key = cabletray_family + ": " + cabletray_type
                    cabletray_typeid = try_get_value(cabletray_type_dict, cabletray_key,
                                                     "Cable Tray Type is not defined")
                    mepcure.create_cabletray(cabletray_typeid, levelid)

            except Exception as e:
                mepcurves_failed.append({
                    'id': getattr(flow_object, GLOBAL_ID),
                    "IfcElement": getattr(flow_object, IFC_ELEMENT),
                    "Name": getattr(flow_object, ELEMENT_NAME),
                    'error': str(e)
                })
                logger.error("Failed to create {} - {}".format(getattr(flow_object, GLOBAL_ID), e))

            if pb.cancelled:
                break
            else:
                pb.update_progress(counter, max_value)

    if mepcurves_failed:
        output.print_md("# Total Failed: {}".format(len(mepcurves_failed)))
        save_failed_report_to_excel(mepcurves_failed, suffix)


if __name__ == "__main__":
    main()
