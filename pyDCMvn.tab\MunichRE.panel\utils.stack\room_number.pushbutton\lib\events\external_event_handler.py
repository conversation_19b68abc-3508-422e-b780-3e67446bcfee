# coding: utf-8
from DCMvn.core import DB, UI, HOST_APP
from DCMvn.core.framework import Debug


class ExternalEventHandler(UI.IExternalEventHandler):
    def __init__(self):
        self._external_event = UI.ExternalEvent.Create(self)  # type: UI.ExternalEvent
        self._identifier = None
        
    def GetName(self):
        # type: () -> str
        """Return the name of the external event handler."""
        if self._identifier:
            return self._identifier
        else:
            return self.__class__.__name__
        
    def Execute(self, application):
        # type: (UI.UIApplication) -> None
        """Override this method in derived classes to implement the event logic."""
        pass
    
    def Raise(self):
        # type: () -> None
        """Raise the external event to be processed by Revit."""
        self._external_event.Raise()
        
    def Dispose(self):
        # type: () -> None
        """Dispose of the external event."""
        if self._external_event:
            self._external_event.Dispose()
            self._external_event = None


class ActionEventHandler(ExternalEventHandler):
    def __init__(self):
        # type: () -> None
        ExternalEventHandler.__init__(self)
        self._action = None  # type: callable | None
        
    def Execute(self, application):
        # type: (UI.UIApplication) -> None
        """Callback invoked by Revit. Not used to be called in user code."""
        if self._action is None:
            return
            
        try:
            self._action(application)
        except Exception as e:
            Debug.WriteLine("Error executing action: {}".format(e))
        finally:
            self._action = None
                
    def Raise(self, action):
        # type: (callable) -> None
        """
        Instructing Revit to queue a handler and raise (signal) the external event.
        
        Revit will wait until it is ready to process the event and then
        it will execute its event handler by calling the Execute method.
        Revit processes external events only when no other commands or
        edit modes are currently active in Revit.
        
        Args:
            action: Function that takes UIApplication as parameter
        """
        # Check if Revit is in API mode (if available)
        if HOST_APP.uiapp.ActiveAddInId is not None:
            self._action = action
            return
            
        if self._action is None:
            self._action = action
        else:
            # Combine actions - create a wrapper that calls both
            previous_action = self._action
            def combined_action(uiApp):
                previous_action(uiApp)
                action(uiApp)
            self._action = combined_action
            
        # Call the base class Raise method to trigger the external event
        ExternalEventHandler.Raise(self)
        
    def Cancel(self):
        # type: () -> None
        """
        Clears the call queue of subscribed delegates.
        The queue can be cleaned up before the first delegate is invoked.
        """
        self._action = None
        
        
    
    
