# coding: utf-8
from DCMvn.core import DB
from DCMvn.core.framework import Trace  # noqa: F401
from ...models import RevitSpatial, GenericSpatial, BaseSpatial, BaseMep  # noqa: F401
from ...utils import (convert_mm_to_internal, sample_curve_points, get_element_center_point,
                     translate_point_by_z, create_reference_intersector, check_ray_distance,
                     create_rectangle_from_curve, create_circle)

class GeometryDetector(object):
    """Handles advanced geometry detection algorithms for spatial relationships."""

    def __init__(self, document, advanced_options=None):
        # type: (DB.Document, object) -> None
        self.document = document
        self.advanced_options = advanced_options
        
        self._z_offset = None
        self._proximity_distance = None
        self._allow_multiple = None
        self._map_above = None
        self._use_proximity = None
        
        if self.advanced_options:
            self._z_offset = - convert_mm_to_internal(self.advanced_options.above_allowed_distance)
            self._proximity_distance = convert_mm_to_internal(self.advanced_options.nearest_allowed_distance)
            self._allow_multiple = self.advanced_options.allow_multiple_values
            self._map_above = self.advanced_options.map_elements_above_spaces
            self._use_proximity = self.advanced_options.use_proximity_mapping
        else:
            self._allow_multiple = False
            self._map_above = False
            self._use_proximity = False

        # This is the default distance
        # in case is_point_inside true return this value and that mean the point is inside the spatial element
        # when multiple spatials match and use single value only the one with the lowest distance will be used
        self._default_distance = 0

    # #########################################################################################
    # Point detection methods
    # #########################################################################################

    def check_points(self, points, spatial_element, show_visual=False):
        # type: (list[DB.XYZ], BaseSpatial, bool) -> bool | tuple[bool, float]
        """Check if any point in the list is inside the spatial element.
        
        Uses direct detection first. Only if direct detection fails completely,
        then try advanced options (above detection, proximity) as fallback methods.

        Args:
            points (list[DB.XYZ]): List of points to check
            spatial_element (BaseSpatial): Spatial element to check against
            show_visual (bool): If True, traces geometry visually for debugging

        Returns:
            tuple[bool, float]: True if any point is inside the spatial element and distance to the hit point
        """
        if not points:
            return False, 0
        
        # Priority 1: Direct point-in-spatial check (most accurate)
        for point in points:
            if show_visual and point:
                Trace.Write(point)  # Show points being tested
            if point and spatial_element.is_point_inside(point):
                return True, self._default_distance
        
        # Priority 2: Check points with vertical offset if enabled (for elements above spaces)
        if self._map_above and self._z_offset:
            for point in points:
                if point:
                    translated_point = translate_point_by_z(point, self._z_offset)
                    if spatial_element.is_point_inside(translated_point):
                        return True, self._default_distance
        
        # Priority 3: Proximity check as final fallback (least accurate)
        if self._use_proximity and self._proximity_distance:
            for point in points:
                is_nearby, distance = self._check_point_nearby(point, spatial_element, show_visual)
                if is_nearby:
                    return True, distance
        return False, 0

    # #########################################################################################
    # Above detection methods
    # #########################################################################################

    def check_curve_above(self, curve, spatial_element):
        # type: (DB.Curve, BaseSpatial) -> tuple[bool, float]
        """Check above detection by sampling curve points and translating by -Z.
        This method is used to check if the curve is above the spatial element.

        Args:
            curve (DB.Curve): The curve to check
            spatial_element (BaseSpatial): The spatial element to check against

        Returns:
            tuple[bool, float]: True if the curve is above the spatial element and default distance
        """
        try:
            if not self._map_above:
                return False, 0
                
            sample_points = sample_curve_points(curve, 3)
            
            for point in sample_points:
                translated_point = translate_point_by_z(point, self._z_offset)
                if spatial_element.is_point_inside(translated_point):
                    return True, self._default_distance
            return False, 0
        except:  # noqa
            return False, 0

    # #########################################################################################
    # Intersection detection methods
    # #########################################################################################

    def check_intersection(self, mep_element, spatial_element, mep_type_info=None, show_visual=False):
        # type: (BaseMep, BaseSpatial, dict, bool) -> tuple[bool, float]
        """Check proximity detection using appropriate method based on spatial type.
        
        Args:
            mep_element (BaseMep): The MEP element to check
            spatial_element (BaseSpatial): The spatial element to check against
            mep_type_info (dict): The MEP type information
            show_visual (bool): If True, traces geometry visually for debugging
        
        Returns:
            tuple[bool, float]: True if the MEP element is nearby the spatial element and distance to the hit point
        """
        if isinstance(spatial_element, RevitSpatial):
            return self._check_nearby_with_geometry_intersection(mep_element, spatial_element, mep_type_info, show_visual)
        elif isinstance(spatial_element, GenericSpatial):
            return self._check_nearby_with_raycasting_intersection(mep_element, spatial_element, mep_type_info, show_visual)
        else:
            return False, 0

    def _check_nearby_with_geometry_intersection(self, mep_element, spatial_element, mep_type_info=None, show_visual=False):
        # type: (BaseMep, BaseSpatial, dict, bool) -> tuple[bool, float]
        """Check proximity for RevitSpatial using geometry intersection:
        - Rectangle for curve-based elements
        - Circle for point-based and unknown cases
        """
        if not self._use_proximity:
            return False, 0

        try:
            # Handle curve-based elements with rectangle lines
            if mep_type_info and mep_type_info.get('has_location_curve') and mep_type_info.get('curve'):
                curve = mep_type_info['curve']
                if show_visual:
                    Trace.Write(curve)  # Show the MEP curve
                return self._check_curve_nearby_geometry(curve, spatial_element, show_visual)
            # Handle point-based elements with circle
            else:
                if mep_type_info and mep_type_info.get('point'):
                    mep_center = mep_type_info['point']
                elif mep_type_info and mep_type_info.get('center_point'):
                    mep_center = mep_type_info['center_point']
                else:
                    mep_center = get_element_center_point(mep_element)
                if not mep_center:
                    return False, 0
                if show_visual:
                    Trace.Write(mep_center)  # Show the MEP center point
                return self._check_point_nearby_geometry(mep_center, spatial_element, show_visual)
        except:  # noqa
            return False, 0

    def _check_nearby_with_raycasting_intersection(self, mep_element, spatial_element, mep_type_info=None, show_visual=False):
        # type: (BaseMep, BaseSpatial, dict, bool) -> tuple[bool, float]
        """Check nearby detection using ReferenceIntersector ray casting for GenericSpatial only."""
        try:
            if not self._use_proximity:
                return False, 0
            
            # Get start point (MEP element center) from pre-computed data if available
            if mep_type_info and mep_type_info.get('point'):
                start_point = mep_type_info['point']
            elif mep_type_info and mep_type_info.get('center_point'):
                start_point = mep_type_info['center_point']
            else:
                start_point = get_element_center_point(mep_element)
            if not start_point:
                return False, 0
                
            # Get end point (spatial element center)
            end_point = get_element_center_point(spatial_element)
            if not end_point:
                return False, 0
                
            return self._check_point_nearby_raycasting(start_point, spatial_element, show_visual)
            
        except:  # noqa
            return False, 0



    # #########################################################################################
    # Nearby detection methods
    # #########################################################################################

    def _check_point_nearby(self, point, spatial_element, show_visual=False):
        # type: (DB.XYZ, BaseSpatial, bool) -> tuple[bool, float]
        """Check if a point is within proximity distance of a spatial element."""
        try:
            if isinstance(spatial_element, RevitSpatial):
                return self._check_point_nearby_geometry(point, spatial_element, show_visual)
            elif isinstance(spatial_element, GenericSpatial):
                return self._check_point_nearby_raycasting(point, spatial_element, show_visual)
            return False, 0
        except:  # noqa
            return False, 0

    def _check_point_nearby_raycasting(self, point, spatial_element, show_visual=False):
        # type: (DB.XYZ, GenericSpatial, bool) -> tuple[bool, float]
        """Check nearby detection using ReferenceIntersector ray casting for GenericSpatial only."""
        try:
            if not self._use_proximity:
                return False, 0
            center_point = get_element_center_point(spatial_element)
            intersector = create_reference_intersector(self.document, spatial_element)
            if intersector:
                return check_ray_distance(point, center_point, intersector, self._proximity_distance, spatial_element.element, show_visual)
            return False, 0
        except:  # noqa
            return False, 0

    def _check_point_nearby_geometry(self, point, spatial_element, show_visual=False):
        # type: (DB.XYZ, RevitSpatial, bool) -> tuple[bool, float]
        """Check nearby detection using geometry intersection
        
        Args:
            point (DB.XYZ): Point to check proximity for
            spatial_element (RevitSpatial): Spatial element to check against
            show_visual (bool): If True, traces geometry visually for debugging
        
        Returns:
            tuple[bool, float]: True if point is nearby and distance to intersection
        """
        try:
            if not self._use_proximity:
                return False, 0

            options = DB.SolidCurveIntersectionOptions()
            options.ResultType = DB.SolidCurveIntersectionMode.CurveSegmentsInside
            proximity_circle = create_circle(point, self._proximity_distance)
            
            if show_visual and proximity_circle:
                Trace.Write(proximity_circle)  # Visual trace of proximity circle
                
            if proximity_circle and isinstance(spatial_element.geometry, DB.Solid):
                intersection_result = spatial_element.geometry.IntersectWithCurve(proximity_circle, options)  # type: DB.SolidCurveIntersection
                is_clash = intersection_result and intersection_result.SegmentCount > 0  # noqa
                if is_clash:
                    longest_curve_segment = self._get_longest_curve_segment(intersection_result)  # type: DB.Curve
                    if show_visual:
                        Trace.Write(longest_curve_segment)  # Visual trace of intersection segment
                    return True, longest_curve_segment.Length
            return False, 0
        except:  # noqa
            return False, 0

    def _check_curve_nearby_geometry(self, curve, spatial_element, show_visual=False):
        # type: (DB.Curve, RevitSpatial, bool) -> tuple[bool, float]
        """Check nearby detection using geometry intersection for curve-based elements
        
        Args:
            curve (DB.Curve): Curve to check proximity for
            spatial_element (RevitSpatial): Spatial element to check against
            show_visual (bool): If True, traces geometry visually for debugging
        
        Returns:
            tuple[bool, float]: True if curve is nearby and distance to intersection
        """

        try:
            if not self._use_proximity:
                return False, 0

            options = DB.SolidCurveIntersectionOptions()
            options.ResultType = DB.SolidCurveIntersectionMode.CurveSegmentsInside
            rectangle_lines = create_rectangle_from_curve(curve, self._proximity_distance)
            
            if show_visual and rectangle_lines:
                for line in rectangle_lines:
                    Trace.Write(line)  # Visual trace of proximity rectangle lines
                    
            if rectangle_lines and isinstance(spatial_element.geometry, DB.Solid):
                for line in rectangle_lines:
                    intersection_result = spatial_element.geometry.IntersectWithCurve(line, options)
                    is_clash = intersection_result and intersection_result.SegmentCount > 0  # noqa
                    if is_clash:
                        longest_curve_segment = self._get_longest_curve_segment(intersection_result)
                        if show_visual:
                            Trace.Write(longest_curve_segment)  # Visual trace of intersection segment
                        return True, longest_curve_segment.Length
            return False, 0
        except:  # noqa
            return False, 0

    @staticmethod
    def _get_longest_curve_segment(intersection_result):
        # type: (DB.SolidCurveIntersection) -> DB.Curve
        """Get the longest curve segment from an intersection result."""
        longest_curve_segment = intersection_result.GetCurveSegment(0)
        for i in range(intersection_result.SegmentCount):
            curve_segment = intersection_result.GetCurveSegment(i)  # type: DB.Curve
            if curve_segment.Length > longest_curve_segment.Length:
                longest_curve_segment = curve_segment
        return longest_curve_segment