---
description: 'DCMvn framework-specific README generator that analyzes .github/copilot directory and DCMvn codebase to create comprehensive repository documentation with pyRevit extension setup, DCMvn architecture overview, development workflow, and framework integration guidelines.'
---

# DCMvn README Blueprint Generator

## Configuration Variables
${README_STYLE="Comprehensive|Standard|Minimal"} <!-- Level of detail in README -->
${TARGET_AUDIENCE="Developers|Users|Both"} <!-- Primary audience for documentation -->
${INCLUDE_SETUP="true|false"} <!-- Include detailed setup instructions -->
${INCLUDE_EXAMPLES="true|false"} <!-- Include code examples and usage -->
${INCLUDE_ARCHITECTURE="true|false"} <!-- Include architecture diagrams and explanations -->
${DCMVN_FOCUS="Framework|Scripts|Integration|All"} <!-- DCMvn component focus -->

## Generated Prompt

"Analyze the DCMvn framework codebase and .github/copilot directory to generate a comprehensive README.md that serves as the definitive guide for understanding, setting up, and contributing to the pyDCMvn.MunichRE extension. Follow this DCMvn-specific approach:

## DCMvn README Structure Analysis

### 1. Repository Context Discovery

First, scan the DCMvn repository to identify:

**DCMvn Framework Components**:
- Analyze `lib/DCMvn/` for core framework architecture
- Identify ViewModelBase, RelayCommand, and service patterns
- Document external event patterns for thread safety
- Map service layer components (CollectorService, DetectionService, ReportService)

**pyRevit Extension Structure**:
- Analyze `pyDCMvn.tab/` folder structure and bundle organization
- Document panel, stack, and pushbutton organization
- Identify script types and their purposes
- Map Excel integration workflows in `Data/IFC/`

**Development Environment**:
- Scan `dev/pyDCMvn.MunichRE/` for C# project structure
- Identify Avalonia and OpenSilver implementations
- Document build configurations and deployment patterns
- Analyze `.github/` copilot instructions and development guidelines

### 2. DCMvn README Content Generation

Generate a comprehensive README.md with the following structure:

```markdown
# pyDCMvn.MunichRE Extension

${README_STYLE == "Comprehensive" ? 
`> **DCMvn Framework for Revit Automation**  
> A comprehensive pyRevit extension providing data control, geometry alignment, parameter management, and QA automation tools for MunichRE's Revit workflows. Built on the DCMvn framework with MVVM architecture, external event threading, and Excel integration capabilities.` :
README_STYLE == "Standard" ?
`> **DCMvn Framework Extension**  
> pyRevit extension for Revit automation with data control, geometry management, and QA tools.` :
`> **DCMvn pyRevit Extension**  
> Revit automation tools for MunichRE workflows.`}

## Overview

${README_STYLE == "Comprehensive" || README_STYLE == "Standard" ? 
`The pyDCMvn.MunichRE extension is a sophisticated pyRevit-based toolkit designed for MunichRE's specific Revit automation needs. It provides:

- **Data Control**: Parameter management and Excel-based workflows
- **Geometry Control**: Element alignment and spatial operations  
- **Quality Assurance**: Automated validation and reporting tools
- **Parameter Management**: Shared parameter creation and mapping
- **Excel Integration**: Bidirectional data exchange with Excel workbooks

Built on the DCMvn framework, the extension follows MVVM architecture patterns with thread-safe Revit API operations and comprehensive error handling.` :
`DCMvn framework extension for Revit automation and data management.`}

## Key Features

### 🔧 Data Control Stack
- **Parameter Transfer**: Excel-to-Revit parameter mapping with validation
- **Parameter Management**: Get/set parameter values with type safety
- **Data Validation**: Comprehensive parameter value checking

### 📐 Geometry Control Stack  
- **Element Alignment**: Precision alignment tools for geometric consistency
- **MEP Placement**: Curve and instance placement from coordinate data
- **Spatial Operations**: Advanced geometric relationship detection

### ✅ Quality Assurance Tools
- **Distance Validation**: Bottom slab clearance checking
- **Excel Comparison**: Model vs. spreadsheet data verification
- **GUID Alignment**: Element identification consistency
- **Taboo Zone QA**: Spatial constraint validation

### 🔧 Utility Tools
- **Room Numbering**: Automated room number assignment
- **Space Management**: MEP space organization
- **Taboo Zone Definition**: Spatial constraint setup

## Technology Stack

${INCLUDE_ARCHITECTURE ? 
`### DCMvn Framework Architecture

\`\`\`
┌─────────────────────────────────────────────────────────────┐
│                     pyRevit Extension                       │
├─────────────────────────────────────────────────────────────┤
│  UI Layer (XAML/WPF)                                       │
│  ├── Panels & Stacks ──── ViewModels (MVVM)               │
│  └── Commands ──────────── RelayCommand Pattern            │
├─────────────────────────────────────────────────────────────┤
│  DCMvn Framework                                            │
│  ├── ViewModelBase ────── Property Change Notification      │
│  ├── External Events ──── Thread-Safe Revit API Access     │
│  └── Service Layer ────── Business Logic Abstraction       │
├─────────────────────────────────────────────────────────────┤
│  Service Layer                                              │
│  ├── CollectorService ──── Element Collection & Caching    │
│  ├── DetectionService ──── Spatial Relationships          │
│  └── ReportService ────── Data Processing & Excel Output   │
├─────────────────────────────────────────────────────────────┤
│  Integration Layer                                          │
│  ├── Revit API ────────── Document & Element Operations    │
│  ├── Excel (MiniExcel) ── Data Import/Export              │
│  └── .NET Framework ───── System Integration              │
└─────────────────────────────────────────────────────────────┘
\`\`\`

### Core Technologies` : "### Technologies"}

- **pyRevit**: ${README_STYLE == "Comprehensive" ? "Extension framework for Revit automation and UI development" : "Revit extension framework"}
- **IronPython 2.7**: ${README_STYLE == "Comprehensive" ? "Python runtime for .NET integration and Revit API access" : "Python runtime for Revit API"}
- **DCMvn Framework**: ${README_STYLE == "Comprehensive" ? "Custom MVVM framework with thread-safe external events" : "Custom MVVM framework"}
- **Revit API**: ${README_STYLE == "Comprehensive" ? "Native Autodesk Revit API for document and element manipulation" : "Revit document and element operations"}
- **MiniExcel**: ${README_STYLE == "Comprehensive" ? "High-performance Excel file processing library" : "Excel file processing"}

${INCLUDE_ARCHITECTURE ? 
`### C# Components

- **Avalonia UI**: Cross-platform UI framework for advanced interfaces
- **OpenSilver**: Web-based UI components for browser deployment
- **.NET Framework**: Core runtime for C# component integration` : ""}

${INCLUDE_SETUP ? 
`## Installation & Setup

### Prerequisites

1. **Autodesk Revit** (2022, 2023, or 2024)
2. **pyRevit** (latest stable version)
   \`\`\`powershell
   # Install pyRevit
   winget install pyRevit.pyRevit
   \`\`\`

### Extension Installation

1. **Clone Repository**:
   \`\`\`bash
   git clone https://github.com/MunichRE/pyDCMvn.MunichRE.extension.git
   cd pyDCMvn.MunichRE.extension
   \`\`\`

2. **Install Extension**:
   \`\`\`powershell
   # Add extension to pyRevit
   pyrevit extend ui pyDCMvn.MunichRE.extension ./
   \`\`\`

3. **Reload pyRevit**:
   - Restart Revit or use pyRevit reload command
   - Verify "MunichRE" tab appears in Revit ribbon

### Development Setup

${README_STYLE == "Comprehensive" ? 
`1. **Development Environment**:
   \`\`\`powershell
   # Install .NET SDK for C# components
   winget install Microsoft.DotNet.SDK.8
   
   # Install VS Code with Python extension
   winget install Microsoft.VisualStudioCode
   \`\`\`

2. **DCMvn Framework Dependencies**:
   - All dependencies included in \`lib/DCMvn/\` directory
   - MiniExcel assembly automatically loaded
   - No additional Python packages required

3. **C# Project Build**:
   \`\`\`powershell
   # Build Avalonia components
   dotnet build pyDCMvn.tab/dev/pyDCMvn.MunichRE/pyDCMvn.MunichRE.Avalonia.csproj
   
   # Build OpenSilver components  
   dotnet build pyDCMvn.tab/dev/pyDCMvn.MunichRE/pyDCMvn.MunichRE.sln
   \`\`\`

4. **Validate Installation**:
   \`\`\`python
   # Test DCMvn framework imports
   from DCMvn.core import DB, HOST_APP
   from DCMvn.forms.mvvm import ViewModelBase, RelayCommand
   \`\`\`` :
`For development setup, see [Development Guide](.github/DEVELOPMENT.md)`}` : ""}

## Usage

${INCLUDE_EXAMPLES ? 
`### Basic Operations

#### Parameter Transfer from Excel

\`\`\`python
# Example: Transfer parameters from Excel to Revit elements
from DCMvn.core import DB, get_output
from DCMvn.io import save_excel_file
from DCMvn.forms import alert

# Validate document before proceeding
if not validate_document_requirements():
    sys.exit()

# Load Excel data and map to Revit parameters
try:
    # Implementation follows DCMvn patterns
    result = transfer_parameters_from_excel(excel_path, mapping_config)
    alert("Parameters transferred successfully", "Success")
except Exception as e:
    alert("Parameter transfer failed: " + str(e), "Error")
\`\`\`

#### Geometry Alignment

\`\`\`python
# Example: Align elements using DCMvn geometry services
from DCMvn.services import DetectionService, CollectorService

# Initialize services with document context
collector = CollectorService(doc)
detector = DetectionService(doc)

# Perform alignment with external event for thread safety
alignment_handler = create_alignment_external_event()
alignment_handler.execute_alignment(source_elements, target_reference)
\`\`\`

#### MVVM ViewModel Implementation

\`\`\`python
# Example: DCMvn ViewModel for parameter management
class ParameterTransferViewModel(ViewModelBase):
    def __init__(self):
        ViewModelBase.__init__(self)
        self._excel_path = ""
        self._transfer_command = RelayCommand(self.execute_transfer)
        
    @property 
    def excel_path(self):
        return self._excel_path
        
    @excel_path.setter
    def excel_path(self, value):
        self._excel_path = value
        self.RaisePropertyChanged("excel_path")
        
    def execute_transfer(self, parameter):
        # External event for thread-safe Revit API access
        transfer_handler.raise_transfer_event(self._excel_path)
\`\`\`` :
`### Quick Start

1. **Open Revit** with a project containing elements
2. **Navigate** to the MunichRE tab in the Revit ribbon  
3. **Select** appropriate tool from Data Control, Geometry Control, or QA sections
4. **Follow** tool-specific dialogs and prompts
5. **Review** results in Revit model or generated Excel reports`}

## Project Structure

${README_STYLE == "Comprehensive" || README_STYLE == "Standard" ? 
`\`\`\`
pyDCMvn.MunichRE.extension/
├── .github/                     # GitHub configuration and Copilot instructions
│   ├── copilot-instructions.md  # Primary DCMvn development guidelines
│   ├── instructions/            # Granular development patterns
│   └── prompts/                 # Advanced prompt engineering files
├── pyDCMvn.tab/                 # Main pyRevit extension directory
│   ├── bundle.yaml              # Extension configuration
│   ├── Data/IFC/                # Excel files for element placement
│   ├── dev/pyDCMvn.MunichRE/    # C# projects (Avalonia, OpenSilver)
│   ├── MunichRE.panel/          # Tool panels and stacks
│   │   ├── datacontrol.stack/   # Parameter and data management tools
│   │   ├── geometrycontrol.stack/ # Alignment and placement tools
│   │   ├── QA.pulldown/         # Quality assurance automation
│   │   └── lib/MunichRE/        # DCMvn framework and utilities
│   └── Parameter.stack/         # Shared parameter management
├── extension.json               # pyRevit extension metadata
└── README.md                   # This documentation
\`\`\`` :
`Basic extension structure with scripts, data files, and C# components.`}

## DCMvn Framework Components

${DCMVN_FOCUS.includes("Framework") || DCMVN_FOCUS == "All" ? 
`### Core Framework

- **ViewModelBase**: Base class for MVVM pattern implementation
- **RelayCommand**: Command pattern for UI operations  
- **External Events**: Thread-safe Revit API access patterns
- **Service Layer**: Business logic abstraction and caching

### Service Architecture

- **CollectorService**: Element collection with performance caching
- **DetectionService**: Spatial relationship and geometric calculations
- **ReportService**: Data processing and Excel report generation` : ""}

${DCMVN_FOCUS.includes("Scripts") || DCMVN_FOCUS == "All" ? 
`### Script Organization

- **Data Control**: Parameter management and Excel workflows
- **Geometry Control**: Element placement and alignment operations
- **QA Tools**: Validation and compliance checking
- **Utilities**: Helper tools for common operations` : ""}

${DCMVN_FOCUS.includes("Integration") || DCMVN_FOCUS == "All" ? 
`### Integration Patterns

- **Excel Workflows**: MiniExcel-based data import/export
- **Thread Safety**: External event patterns for UI operations
- **Error Handling**: Comprehensive exception management with user notifications
- **Performance**: Caching strategies for expensive Revit API operations` : ""}

## Development

### Contributing

${README_STYLE == "Comprehensive" ? 
`1. **Read Guidelines**: Review \`.github/copilot-instructions.md\` for DCMvn patterns
2. **Follow Standards**: Adhere to established DCMvn coding conventions  
3. **Test Thoroughly**: Validate in Revit environment with representative models
4. **Document Changes**: Update relevant instruction files for new patterns
5. **External Events**: Ensure thread safety for all Revit API operations

### Code Standards

- **DCMvn Patterns**: Follow ViewModelBase and service layer architecture
- **Error Handling**: Use DCMvn alert() for user notifications
- **Thread Safety**: Implement external events for Revit API access
- **Performance**: Use caching patterns from CollectorService
- **Documentation**: Include docstrings and usage examples` :
`See \`.github/CONTRIBUTING.md\` for detailed contribution guidelines.`}

### Building C# Components

${INCLUDE_SETUP ? 
`\`\`\`powershell
# Build Avalonia project
dotnet build pyDCMvn.tab/dev/pyDCMvn.MunichRE/pyDCMvn.MunichRE.Avalonia.csproj

# Build OpenSilver solution
dotnet build pyDCMvn.tab/dev/pyDCMvn.MunichRE/pyDCMvn.MunichRE.sln

# Run OpenSilver in browser (development)
dotnet run --project pyDCMvn.tab/dev/pyDCMvn.MunichRE/pyDCMvn.MunichRE.OpenSilver.Browser.csproj
\`\`\`` :
`Use provided VS Code tasks for building C# components.`}

## Support

${TARGET_AUDIENCE == "Users" || TARGET_AUDIENCE == "Both" ? 
`### For Users

- **Documentation**: Comprehensive tool guides in \`docs/\` directory
- **Training Materials**: Video tutorials and step-by-step guides
- **Support Tickets**: Internal MunichRE support system
- **FAQ**: Common questions and troubleshooting tips` : ""}

${TARGET_AUDIENCE == "Developers" || TARGET_AUDIENCE == "Both" ? 
`### For Developers  

- **DCMvn Framework**: Architecture documentation in \`.github/instructions/\`
- **API Reference**: Code documentation and examples
- **Development Setup**: Environment configuration guides
- **Testing**: Unit and integration testing patterns` : ""}

## License

${README_STYLE == "Comprehensive" ? 
`This project is proprietary software developed for MunichRE internal use. All rights reserved.

### Third-Party Components

- **pyRevit**: MIT License
- **MiniExcel**: MIT License  
- **Avalonia**: MIT License
- **OpenSilver**: Open Source License` :
`Proprietary software for MunichRE internal use.`}

---

${README_STYLE == "Comprehensive" ? 
`*Built with the DCMvn framework for robust, thread-safe Revit automation workflows.*` :
`*DCMvn Framework - pyRevit Extension for MunichRE*`}
\`\`\`

### 3. DCMvn-Specific Enhancements

The generated README should include:

**DCMvn Framework Integration**:
- Highlight MVVM architecture and external event patterns
- Document service layer components and their purposes  
- Explain thread safety requirements for Revit API operations
- Include Excel integration workflows and data transformation patterns

**pyRevit Extension Context**:
- Explain bundle and stack organization within pyRevit framework
- Document script types and their specific purposes
- Include installation and setup instructions for pyRevit environment
- Highlight C# component integration with Avalonia and OpenSilver

**Development Workflow Integration**:
- Reference .github/copilot-instructions.md for development standards
- Include links to granular instruction files for specific patterns
- Document build processes for C# components
- Explain testing strategies for Revit API integration

**User Experience Focus**:
- Provide clear usage examples with DCMvn framework patterns
- Include screenshots or diagrams where appropriate
- Document error handling and troubleshooting approaches
- Explain Excel workflow integration and data requirements

This comprehensive README serves as both user documentation and developer onboarding guide, ensuring clear understanding of the DCMvn framework architecture and development patterns."

## Expected Output

A comprehensive README.md file that provides clear documentation for the pyDCMvn.MunichRE extension, highlighting the DCMvn framework architecture, pyRevit integration patterns, and development workflow while serving both user and developer audiences effectively.
