# coding: utf8
import clr
from DCMvn.core import DB, HOST_APP
from DCMvn.core.framework import System, Trace, Debug
from DCMvn.forms import alert
from DCMvn.revit.selection import DSelection
from pyrevit import script

from lib.factory.curve_factory import CurveFactory
from lib.factory.spatial_factory import SpatialFactory
from lib.cached_collector import CachedCollector
from lib.generic_space import GenericSpace
from lib.space_props import GenericSpaceProperties

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

doc = HOST_APP.doc

# from archive import ifc_to_space_manual
#
# script.exit()

if __shiftclick__: # type: ignore # noqa
    result = alert("Automatically create spaces from linked generic spaces?\n\n"
                   "This take time, are you sure you want to continue?",
                   warn_icon=True, yes=True, no=True)
    if not result:
        script.exit()

    HOST_COLLECTOR = CachedCollector()

    # print(len(HOST_COLLECTOR.linked_generic_spaces))
    # script.exit()
    with DB.Transaction(doc, "Create Space") as t:
        t.Start()
        
        generics_spaces = HOST_COLLECTOR.linked_generic_spaces
        # generics_spaces = HOST_COLLECTOR.collect_linked_generic_spaces(active_view=True)
        # generics_spaces = DB.FilteredElementCollector(HOST_APP.doc, HOST_APP.active_view.Id) \
        #         .OfCategory(DB.BuiltInCategory.OST_GenericModel) \
        #         .WhereElementIsNotElementType() \
        #         .Where(lambda x: x.get_Parameter(GenericSpaceProperties.ExportToIfcAs).AsString() == GenericSpaceProperties.IfcSpaceType) \
        #         .ToList()
        # generics_spaces = [DSelection(is_linked=True).pick()]
        Debug.WriteLine(len(generics_spaces))
        for generic_space in generics_spaces:
            space2 = GenericSpace(generic_space, HOST_COLLECTOR)
            try:
                location = CurveFactory.get_point_inside_largest_curve_array(space2.curve_arrays)
                
                # Create separators
                SpatialFactory.create_separators(space2, HOST_COLLECTOR, create_space=True)
            
                # Create space
                new_space = SpatialFactory.create_space(space2, location)
                
                # Map properties
                SpatialFactory.map_properties(new_space, space2)
            except Exception:
                import traceback
                print(space2.guid + " " + traceback.format_exc())
            continue
        
        t.Commit()
else:
    from archive.space_export import main
    main()

def serialize_solid(solid):
    # type: (DB.Solid) -> dict | None
    """Convert Revit Solid to serializable dict."""
    try:
        faces = []
        for face in solid.Faces:
            # Get face vertices (as XYZ points)
            edge_loops = []
            for i in range(face.EdgeLoops.Size):
                loop = face.EdgeLoops.get_Item(i)
                points = [edge.AsCurve().GetEndPoint(0).ToString() for edge in loop]
                edge_loops.append(points)
            faces.append({
                "area": face.Area,
                "edge_loops": edge_loops
            })
        return {
            "volume": solid.Volume,
            "faces": faces
        }
    except Exception as e:
        Debug.WriteLine("Solid serialization error: {}".format(str(e)))
        return None


# def serialized_space_solid(space, spatial_element_geometry_calculator):
#     # type: (DB.Mechanical.Space, DB.SpatialElementGeometryCalculator) -> str | None
#     """Serialize space solid geometry."""
#     if DB.SpatialElementGeometryCalculator.CanCalculateGeometry(space):

#         geometry_result = spatial_element_geometry_calculator.CalculateSpatialElementGeometry(space)  # type: DB.SpatialElementGeometryResults
#         if geometry_result:
#             solid = geometry_result.GetGeometry()  # type: DB.Solid
#             # print(serialize_solid(solid))
#             return solid
#     return None
# try:
#     a = (DB.FilteredElementCollector(doc)
#         .OfCategory(DB.BuiltInCategory.OST_MEPSpaces)
#         .WhereElementIsNotElementType()
#         .Where(lambda x: x.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString() == "1egwi48mbyHRNRRVP0YOtU")
#         .FirstOrDefault()) # type: DB.Mechanical.Space
#     spatial_geometry_calculator = DB.SpatialElementGeometryCalculator(doc)
#     if a:
#         serialized_space = serialized_space_solid(a, spatial_geometry_calculator)
#         Trace.Write(serialized_space)
# except Exception as e:
#     print("Error: {}".format(str(e)))