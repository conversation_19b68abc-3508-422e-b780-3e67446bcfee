# -*- coding: utf-8 -*-
# Imports
import os
import xlrd
import clr
clr.AddReference("System")
import System
clr.AddReference("System.Windows.Forms")
from System.Windows.Forms import OpenFileDialog
from Autodesk.Revit.DB import *
from Autodesk.Revit.UI import *
from pyrevit import forms, script
from DCMvn.core.framework import List
from DCMvn.core import DB, REVIT_VERSION, ApplicationServices, RevitExceptions  # noqa
from Autodesk.Revit.DB import UnitTypeId, UnitUtils
# Variables
doc = __revit__.ActiveUIDocument.Document
uidoc = __revit__.ActiveUIDocument
output = script.get_output()

# Open file dialogs
def open_file_dialog(title):
    dialog = OpenFileDialog()
    dialog.Filter = "Excel Files|*.xlsx;*.xls"
    dialog.Title = title
    return dialog

dialog_mapping = open_file_dialog("Select a Mapping List")
dialog_data = open_file_dialog("Select an Excel Data File")


def safe_get_parameter(element, param_name):
    if param_name.startswith("BuiltInParameter."):
        enum_name = param_name.split("BuiltInParameter.", 1)[1]
        bip = getattr(BuiltInParameter, enum_name, None)
        if bip is not None:
            return element.get_Parameter(bip)
        return None            # tên enum không hợp lệ
    else:
        return element.LookupParameter(param_name)


separator = "#" * 90 + "\n"

def get_all_spec_types():
    all_spec_types = {}
    if REVIT_VERSION <= 2021:
        values = Enum.GetValues(DB.ParameterType)
        for value in values:
            try:
                all_spec_types[DB.LabelUtils.GetLabelFor(value)] = value
            except Exception:
                pass
    else:
        for attribute in dir(DB.SpecTypeId):
            value = getattr(DB.SpecTypeId, attribute)
            if isinstance(value, DB.ForgeTypeId):
                try:
                    all_spec_types[DB.LabelUtils.GetLabelForSpec(value)] = value
                except RevitExceptions.ArgumentException:
                    pass
    return all_spec_types

# @staticmethod
def get_unit_type_id(spec_type_name):
    all_spec_types = get_all_spec_types()
    spec_type_id = all_spec_types.get(spec_type_name)
    if not isinstance(spec_type_id, DB.ForgeTypeId):
        raise TypeError("Expected ForgeTypeId, but got {} for '{}'".format(type(spec_type_id), spec_type_name))
    format_opt = doc.GetUnits().GetFormatOptions(spec_type_id)
    return format_opt.GetUnitTypeId()

def read_excel_sheet(file_path):
    return xlrd.open_workbook(file_path).sheet_by_index(0)

def read_mapping_list(file_path_):
    sheet = read_excel_sheet(file_path_)
    if sheet.nrows < 2:
        raise ValueError("Mapping list must contain at least two rows.")
    mappings, param_types = {}, {}
    for row in range(1, sheet.nrows):
        key, value, param_type = (sheet.cell_value(row, i).strip() for i in range(3)) if sheet.ncols > 2 else (sheet.cell_value(row, 0).strip(), sheet.cell_value(row, 1).strip(), "")
        if key and value:
            mappings[key] = value
            if param_type:
                param_types[key] = param_type
    primary_key_excel, primary_key_rvt = (sheet.cell_value(1, i).strip() for i in range(2))
    if not primary_key_excel or not primary_key_rvt:
        raise ValueError("Primary key mapping is missing in the second row of the mapping list.")
    return mappings, primary_key_excel, primary_key_rvt, param_types

def read_excel_data(file_path_, mappings, primary_key_excel):
    sheet = read_excel_sheet(file_path_)
    headers = {sheet.cell_value(0, col).strip(): col for col in range(sheet.ncols)}
    key_col = headers.get(primary_key_excel)
    value_cols = {param: headers.get(param) for param in mappings if param != primary_key_excel}
    if key_col is None or any(val is None for val in value_cols.values()):
        raise ValueError("Missing required columns in the Excel file.")
    return {
        str(sheet.cell_value(row, key_col)).strip(): {
            param: str(sheet.cell_value(row, value_cols[param])).strip()
            for param in value_cols if value_cols[param] is not None
        }
        for row in range(1, sheet.nrows) if sheet.cell_value(row, key_col)
    }

def load_rvt_data():
    categories_filter = ElementMulticategoryFilter(List[ElementId]([c.Id for c in doc.Settings.Categories if c.CategoryType == CategoryType.Model]))
    return FilteredElementCollector(doc).WherePasses(categories_filter).WhereElementIsNotElementType()

def is_numeric(value):
    """Check if a value is numeric."""
    try:
        float(value)
        return True
    except (ValueError, TypeError):
        return False

def is_integer(value):
    try:
        int(value)
        return float(value).is_integer()
    except ValueError:
        return False

def format_numeric_string(v):
    if is_numeric(v):
        return str(int(float(v))) if float(v).is_integer() else str(float(v))
    return str(v)


def update_revit_parameters(excel_data_, mappings, primary_key_rvt, primary_key_excel,param_types={}):
    """Update Revit elements with matching IFC_GUID values and count updates."""
    elements = list(load_rvt_data())
    unit_mapping = get_all_spec_types()
    total_global_ids = len(excel_data_.items())  # Count of GlobalId from Excel
    updated_elements = {}
    failed_updates = []
    skipped_keys = []
    unmatched_excel_keys = set(excel_data_.keys())  # New: Set of all Excel primary keys
    t = Transaction(doc, "Set Parameters From Excel")
    t.Start()
    try:
        for element in elements:
            rvt_guid_param = safe_get_parameter(element, primary_key_rvt)
            if not rvt_guid_param:
                continue
            rvt_guid_value = rvt_guid_param.AsString().strip() if rvt_guid_param.AsString() else None
            if rvt_guid_value and rvt_guid_value in excel_data_:
                unmatched_excel_keys.remove(rvt_guid_value)  # New: Remove matched key
                updated_elements[rvt_guid_value] = []
                for excel_param, rvt_param in mappings.items():
                    if excel_param == primary_key_excel or rvt_param == primary_key_rvt:
                        skipped_keys.append(rvt_guid_value)
                        continue  # Skip setting value for the primary key
                    param = element.LookupParameter(rvt_param)
                    if param and not param.IsReadOnly:
                        try:
                            param_value = excel_data_[rvt_guid_value][excel_param]
                            if param_value is None or param_value == "" or str(param_value).strip() == "":
                                continue  # Skip setting value if the cell is blank or empty

                            storage_type_map = {
                                StorageType.String: format_numeric_string,
                                StorageType.Double: lambda v: int(float(v)) if is_integer(v) else float(v) if is_numeric(v) else None,
                                StorageType.Integer: lambda v: int(float(v)) if is_numeric(v) else None,
                                StorageType.ElementId: lambda v: ElementId(int(float(v))) if is_numeric(v) else None
                            }
                            if param.StorageType in storage_type_map:
                                converted_value = storage_type_map[param.StorageType](param_value)
                                if converted_value is not None:
                                    param_type = param_types.get(excel_param, None)

                                    if param_type in unit_mapping:
                                        unit_type = get_unit_type_id(param_type)
                                        if unit_type:
                                            internal_value = UnitUtils.ConvertToInternalUnits(converted_value, unit_type)
                                            param.Set(internal_value)
                                        else:
                                            print("Warning: {} is undefined.".format(unit_type))
                                            param.Set(converted_value)
                                    else:
                                        param.Set(converted_value)
                                else:
                                    failed_updates.append((rvt_guid_value, rvt_param, "Skipped due to non-numeric value"))
                            updated_elements[rvt_guid_value].append(rvt_param)
                        except Exception as e:
                            failed_updates.append((rvt_guid_value, rvt_param, str(e)))
        t.Commit()
        unmatched_count = len(unmatched_excel_keys)  # New: Count of unmatched Excel keys
        not_set_param = len(failed_updates)

        if unmatched_excel_keys:
            output.print_html('<strong style="color: red">Unmatched Excel primary keys:</strong>')
            for key in unmatched_excel_keys:
                output.print_html('{}'.format(key))

        if failed_updates:
            output.print_html('<strong style="color: red">Failed Updates:</strong>')
            for failed in failed_updates:
                output.print_html('{}, Parameter: {}, Error: {}'.format(*failed))
        print(separator)
        output.print_html('Total successful elements updated: <strong style="color: green">{}</strong> out of <strong>{}</strong>. There are <strong style="color: red">{}</strong> skipped elements and <strong style="color: red">{}</strong> skipped parameters.'.format(len(updated_elements.keys()), total_global_ids, unmatched_count,not_set_param))

    except Exception as e:
        t.RollBack()
        print("Error encountered: {}".format(e))

if dialog_mapping.ShowDialog() == System.Windows.Forms.DialogResult.OK:
    mapping_file_path = dialog_mapping.FileName
    try:
        mappings, primary_key_excel, primary_key_rvt, param_types = read_mapping_list(mapping_file_path)
        if dialog_data.ShowDialog() == System.Windows.Forms.DialogResult.OK:
            try:
                data_file_path = dialog_data.FileName
                excel_data = read_excel_data(data_file_path, mappings, primary_key_excel)
                update_revit_parameters(excel_data, mappings, primary_key_rvt, primary_key_excel, param_types)
            except Exception as e:
                print("Process aborted due to error in data file: {}".format(e))
        else:
            print("No data file selected. Process aborted.")
    except Exception as e:
        print("Process aborted due to error in mapping file: {}".format(e))
else:
    print("No mapping file selected. Process aborted.")

output.close_others()

