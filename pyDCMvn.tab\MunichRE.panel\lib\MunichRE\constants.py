# coding: utf-8
import os.path as op

IFC_EXCEL_FOLER = op.join(op.abspath(__file__).split("MunichRE.panel")[0], "Data", "IFC")
REGISTER_EXCEL = op.join(op.abspath(__file__).split("MunichRE.panel")[0], "Data", "Register.xlsx")
EXCEL_EXTENSION = ".xlsx"
IGNORE_MISSING_EXCEL = "IgnoredMissing"
IGNORE_MISSING_EXCEL_EXTENSION = "_{}{}".format(IGNORE_MISSING_EXCEL, EXCEL_EXTENSION)
IGNORE_ALIGNED_EXCEL = "IgnoredAligned"
IGNORE_ALIGNED_EXCEL_EXTENSION = "_{}{}".format(IGNORE_ALIGNED_EXCEL, EXCEL_EXTENSION)
NOT_APPLY = "Not Apply"

############################################  IFC   ############################################
GLOBAL_ID = "GlobalId"
IFC_ELEMENT = "IfcElement"
ELEMENT_NAME = "Name"
SYSTEM_NAME = "SystemName"
STOREY_NAME = "RVT.StoreyName"
STOREY_ELEVATION = "StoreyElevation"
RVT_SYSTEM_TYPE = "RVT.SystemType"
RVT_TYPE_NAME = "RVT.TypeName"
RVT_CATEGORY = "RVT.Category"
RVT_FAMILY_NAME = "RVT.FamilyName"

# FLOW_SEGMENT = "IfcFlowSegment"
INSULATION = "IfcCovering"

# Pipe Segment
PIPE_LENGTH = ["KBP.[l] Laenge", "Pset_FlowSegmentPipeSegment.Length"]
PIPE_NOMINAL_DIAMETER = ["KBP.[d1] DN", "Pset_PipeSegmentTypeCommon.NominalDiameter"]

#  Duct Segment
DUCT_LENGTH = "Pset_DuctSegmentTypeCommon.Length"
DUCT_WIDTH = "Pset_DuctSegmentTypeCommon.NominalDiameterOrWidth"
DUCT_HEIGHT = "Pset_DuctSegmentTypeCommon.NominalHeight"
DUCT_SHAPE = "Pset_DuctSegmentTypeCommon.Shape"
RECTANGULAR_SHAPE = "RECTANGULAR"
ROUND_SHAPE = "ROUND"

# Cable Segment
CABLE_LENGTH = ["KBP.[l] Laenge in mm"]
CABLE_WIDTH = ["KBP.[a] Breite in mm"]
CABLE_HEIGHT = ["KBP.[b] Hoehe in mm"]

# Oriented Boundin Box
CENTROID = "ElementCentroid (ft)"
CENTROID_MM = "ElementCentroid (mm)"
DIRECTION_X = "ElementDirection_X"
DIRECTION_Y = "ElementDirection_Y"
DIRECTION_Z = "ElementDirection_Z"
HALF_DISTANCE = "ElementHalfDistance"
HALF_DISTANCE_MM = "ElementHalfDistance (mm)"

############################################  Register   ############################################
SYSTEM_FULL_NAME = "System Full Name"
SYSTEM_TYPE_NAME = "System Type Name/Service Type/Workset"
MODEL_NAME = "Model Name"
MODEL_HZG = "Model HZG"
MODEL_KLT = "Model KLT"
MODEL_RLT = "Model RLT"
MODEL_SPR = "Model SPR"
MODEL_SAN = "Model SAN"
MODEL_ELE = "Model ELE"
