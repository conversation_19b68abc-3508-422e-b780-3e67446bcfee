﻿<mah:MetroWindow
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Share Parameter Path"
    Width="400"
    ShowCloseButton="False"
    ShowInTaskbar="False"
    ShowMaxRestoreButton="False"
    ShowMinButton="False"
    SizeToContent="Height"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">

    <Grid Margin="5,0,5,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>

        <Label
            Grid.Row="0"
            Grid.Column="0"
            Margin="5,0,10,0"
            VerticalAlignment="Center"
            Content="📝 Register List:" />

        <TextBox
            x:Name="RegisterPathTextBox"
            Grid.Row="0"
            Grid.Column="1"
            Margin="0,5,5,5"
            IsReadOnly="True"
            Text="{Binding RegisterListPath, UpdateSourceTrigger=PropertyChanged}"
            ToolTip="{Binding RegisterListPath, UpdateSourceTrigger=PropertyChanged}" />

        <Button
            Grid.Row="0"
            Grid.Column="2"
            Width="30"
            Margin="0,5"
            Command="{Binding PickFileCommand}"
            CommandParameter="xlsx"
            Content="..."
            ToolTip="Select Excel register list" />

        <Label
            Grid.Row="1"
            Grid.Column="0"
            Margin="5,0,10,0"
            VerticalAlignment="Center"
            Content="🕹️ Shared Parameter:" />

        <TextBox
            x:Name="SharedParameterPathTextBox"
            Grid.Row="1"
            Grid.Column="1"
            Margin="0,5,5,5"
            IsReadOnly="True"
            Text="{Binding SharedParameterPath, UpdateSourceTrigger=PropertyChanged}"
            ToolTip="{Binding SharedParameterPath, UpdateSourceTrigger=PropertyChanged}" />

        <Button
            Grid.Row="1"
            Grid.Column="2"
            Width="30"
            Margin="0,5"
            Command="{Binding PickFileCommand}"
            CommandParameter="txt"
            Content="..."
            ToolTip="Select shared parameter" />

        <Button
            Grid.Row="2"
            Grid.ColumnSpan="3"
            Margin="0,5"
            HorizontalAlignment="Right"
            Command="{Binding ConfirmCommand}"
            Content="Confirm" />

    </Grid>

</mah:MetroWindow>