# -*- coding: utf-8 -*-
# Imports
import os
import sys
import clr
import datetime
from collections import defaultdict

# .NET Framework references
clr.AddReference("System")
from System.Collections.Specialized import OrderedDictionary

# Revit API references
from Autodesk.Revit.DB import *
from Autodesk.Revit.UI import *
from pyrevit import forms, script

# Import your libraries
from DCMvn.io import save_excel_file
from MunichRE.excel_reader import MiniExcel

# Variables
doc = __revit__.ActiveUIDocument.Document
uidoc = __revit__.ActiveUIDocument
output = script.get_output()
VAR_PAR_VAL_MANUFACTURER = "_"

def show_parameter_transfer_dialog():
    """Show WPF dialog for selecting transfer options and Excel reporting"""
    try:
        # Create custom WPF form with checkboxes
        components = [
            forms.Label('Select Parameter Transfer Operations:'),
            forms.Separator(),
            forms.CheckBox('option1_checkbox', 'Option 1: Transfer [Workset] to [SI_TE_DM_Workset]', default=False),
            forms.CheckBox('option2_checkbox', 'Option 2: Set BO parameters to \'_\' and BuildingCode to any input', default=False),
            forms.Separator(),
            forms.CheckBox('excel_report_checkbox', 'Export to Excel', default=True),
            forms.Separator(),
        ]
        
        form = forms.FlexForm('Parameter Transfer Options', components)
        form.show()
        
        # Check if form was cancelled
        if not form:
            return [], False
            
        # Get selected options
        selected_options = []
        if hasattr(form, 'option1_checkbox') and form.option1_checkbox:
            selected_options.append(1)
        if hasattr(form, 'option2_checkbox') and form.option2_checkbox:
            selected_options.append(2)
            
        # Get Excel report preference
        report_to_excel = hasattr(form, 'excel_report_checkbox') and form.excel_report_checkbox
        
        # Validate that at least one option is selected
        if not selected_options:
            forms.alert("Please select at least one operation to perform.", title="No Options Selected")
            return [], False
        
        return selected_options, report_to_excel
        
    except Exception as e:
        print("Dialog error: {}".format(str(e)))
        # Fallback to simple input
        choice = forms.alert(
            "Select parameter transfer option:\n"
            "Click 'Option 1' for Workset transfer\n"
            "Click 'Option 2' for BO parameters\n"
            "Click 'Both' for both options",
            title="Parameter Transfer",
            options=["Option 1", "Option 2", "Both", "Cancel"]
        )
        
        # Fallback also asks about Excel export
        export_choice = forms.alert(
            "Export results to Excel?",
            title="Excel Export",
            options=["Yes", "No"]
        )
        export_to_excel = export_choice == "Yes"
        
        if choice == "Option 1":
            return [1], export_to_excel
        elif choice == "Option 2":
            return [2], export_to_excel
        elif choice == "Both":
            return [1, 2], export_to_excel
        else:
            return [], False

def get_parameter_by_name(element, param_name):
    """Get parameter by name from element"""
    try:
        # Try to get as shared parameter first
        param = element.LookupParameter(param_name)
        if param:
            return param
        
        # Try built-in parameters
        if param_name == "Workset":
            return element.get_Parameter(BuiltInParameter.ELEM_PARTITION_PARAM)
        
        return None
    except:
        return None

def get_type_parameter_by_name(element_type, param_name):
    """Get type parameter by name"""
    try:
        return element_type.LookupParameter(param_name)
    except:
        return None

def set_parameter_value(param, value):
    """Set parameter value safely"""
    try:
        if param and not param.IsReadOnly:
            if param.StorageType == StorageType.String:
                param.Set(str(value))
                return True
            elif param.StorageType == StorageType.Integer:
                if isinstance(value, str) and value.isdigit():
                    param.Set(int(value))
                    return True
                elif isinstance(value, int):
                    param.Set(value)
                    return True
            elif param.StorageType == StorageType.Double:
                if isinstance(value, (int, float)):
                    param.Set(float(value))
                    return True
        return False
    except:
        return False

def get_model_category_elements():
    """Get only elements that belong to Model categories, excluding Annotation, Analytical, and specific unwanted categories"""
    try:
        # Define categories to exclude
        excluded_category_names = [
            "Materials",
            "Primary Contours", 
            "Project Information",
            "Sun Path",
            "Internal Origin",
            "Survey Point",
            "Project Base Point",
            "Material Assets",
            "Pipe Segments",
            "Legend Components",
            "Cameras",
            "Sheets",
            "Detail Items",
            "<Sketch>",
            "Lines",
            "Center Line",
            "Center line",
            "HVAC Zones",
            "Piping Systems"
        ]
        
        # Get all elements that are not element types
        collector = FilteredElementCollector(doc).WhereElementIsNotElementType()
        all_elements = collector.ToElements()
        
        model_elements = []
        excluded_count = 0
        excluded_by_type = 0
        excluded_by_name = 0
        
        for element in all_elements:
            try:
                # Check if element has a category
                if not element.Category:
                    excluded_count += 1
                    continue
                
                # Check if it's NOT a Model category
                if element.Category.CategoryType != CategoryType.Model:
                    excluded_by_type += 1
                    excluded_count += 1
                    continue
                
                # Check if category name is in excluded list
                category_name = element.Category.Name
                if category_name in excluded_category_names:
                    excluded_by_name += 1
                    excluded_count += 1
                    continue
                
                # If we get here, it's a valid Model category element
                model_elements.append(element)
                
            except:
                # If we can't determine category, exclude the element
                excluded_count += 1
                continue
        
        print("Filtered elements: {} Model category elements found".format(len(model_elements)))
        print("  - {} excluded by category type (Annotation/Analytical)".format(excluded_by_type))
        print("  - {} excluded by specific category name".format(excluded_by_name))
        print("  - {} other exclusions".format(excluded_count - excluded_by_type - excluded_by_name))
        
        return model_elements
        
    except Exception as e:
        print("Error filtering Model category elements: {}".format(str(e)))
        # Fallback to all elements if filtering fails
        collector = FilteredElementCollector(doc).WhereElementIsNotElementType()
        return collector.ToElements()

def option1_transfer_workset():
    """Option 1: Transfer Workset to SI_TE_DM_Workset"""
    print("Starting Option 1: Workset Transfer...")
    print("Processing only Model category elements, excluding Annotation and Analytical categories...")
    
    # Get only Model category elements
    elements = get_model_category_elements()
    
    success_count = 0
    failure_count = 0
    results = []
    
    t = Transaction(doc, "Transfer Workset Parameters")
    t.Start()
    
    try:
        for element in elements:
            try:
                # Skip elements that don't have workset parameter
                workset_param = get_parameter_by_name(element, "Workset")
                if not workset_param:
                    continue
                
                # Get workset value
                if workset_param.StorageType == StorageType.Integer:
                    workset_id = workset_param.AsInteger()
                    if workset_id > 0:
                        workset = doc.GetWorksetTable().GetWorkset(WorksetId(workset_id))
                        workset_name = workset.Name
                    else:
                        workset_name = "None"
                else:
                    workset_name = workset_param.AsString() or "None"
                
                # Get target parameter
                target_param = get_parameter_by_name(element, "SI_TE_DM_Workset")
                if target_param and set_parameter_value(target_param, workset_name):
                    success_count += 1
                    results.append({
                        'ElementId': element.Id.IntegerValue,
                        'Category': element.Category.Name if element.Category else "Unknown",
                        'WorksetValue': workset_name,
                        'Status': 'Success'
                    })
                else:
                    failure_count += 1
                    results.append({
                        'ElementId': element.Id.IntegerValue,
                        'Category': element.Category.Name if element.Category else "Unknown",
                        'WorksetValue': workset_name,
                        'Status': 'Failed - Parameter not found or read-only'
                    })
                    
            except Exception as e:
                failure_count += 1
                results.append({
                    'ElementId': element.Id.IntegerValue,
                    'Category': element.Category.Name if element.Category else "Unknown",
                    'WorksetValue': 'Error',
                    'Status': 'Failed - ' + str(e)
                })
        
        t.Commit()
        
    except Exception as e:
        t.RollBack()
        print("Transaction failed: " + str(e))
        return 0, 0, []
    
    print("Option 1 completed: {} successful, {} failed".format(success_count, failure_count))
    return success_count, failure_count, results

def option2_set_bo_parameters():
    """Option 2: Set BO parameters and BuildingCode"""
    print("Starting Option 2: BO Parameters...")

    # Ask user for BuildingCode value once
    building_code_value = forms.ask_for_string(
        default="HG",
        prompt="Enter value for SI_TE_BO_BuildingCode:",
        title="BuildingCode Input"
    )

    # Define target parameters
    instance_parameters = [
        "SI_TE_BO_Abwärme [kW]",
        "SI_TE_BO_Ansilumen",
        "SI_TE_BO_Anschlusstyp und POE-Klasse",
        "SI_TE_BO_Ausgang Schaltleistung",
        "SI_TE_BO_Ausgang Spannung",
        "SI_TE_BO_Ausgangssignal",
        "SI_TE_BO_Baujahr",
        "SI_TE_BO_Benutzer-Adress-System",
        "SI_TE_BO_Betrachtungsabstand (min/max)",
        "SI_TE_BO_Bilddiagonale",
        "SI_TE_BO_Busschnittstelle 1 / Protokoll 1",
        "SI_TE_BO_Busschnittstelle 2 / Protokoll 2",
        "SI_TE_BO_Eingang Schaltleistung",
        "SI_TE_BO_Eingang Spannung",
        "SI_TE_BO_Eingangssignal",
        "SI_TE_BO_Einstellbereich",
        "SI_TE_BO_Einstellwert",
        "SI_TE_BO_Funktionserhalt",
        "SI_TE_BO_Hyperlink",
        "SI_TE_BO_Lastaufnahme in Watt im Standby",
        "SI_TE_BO_Lichtfarbe",
        "SI_TE_BO_Lichstärke/Dimmung",
        "SI_TE_BO_Leistung in Watt",
        "SI_TE_BO_Materialien",
        "SI_TE_BO_Messbereich",
        "SI_TE_BO_max. Lastaufnahme in Kg",
        "SI_TE_BO_max. Lastaufnahme in Watt",
        "SI_TE_BO_Notstellfunktion",
        "SI_TE_BO_Pixelpitch",
        "SI_TE_BO_Prüfpflichtig",
        "SI_TE_BO_Regelungsart",
        "SI_TE_BO_Thermische Leistung",
        "SI_TE_BO_Versorgungs-Leistung",
        "SI_TE_BO_Versorgungs-Spannung",
        "SI_TE_BO_Wartungs-Prüfnachweise"
    ]
    
    type_parameters = ["Manufacturer", "Model"]
    
    # Get only Model category elements
    print("Processing only Model category elements, excluding Annotation and Analytical categories...")
    elements = get_model_category_elements()
    
    success_count = 0
    failure_count = 0
    results = []
    
    t = Transaction(doc, "Set BO Parameters")
    t.Start()
    
    try:
        for element in elements:
            element_results = {
                'ElementId': element.Id.IntegerValue,
                'Category': element.Category.Name if element.Category else "Unknown",
                'InstanceParams': 0,
                'TypeParams': 0,
                'BuildingCode': False,
                'Failures': []
            }
            
            # Set instance parameters to "_"
            for param_name in instance_parameters:
                param = get_parameter_by_name(element, param_name)
                if param and set_parameter_value(param, VAR_PAR_VAL_MANUFACTURER):
                    element_results['InstanceParams'] += 1
                elif param:
                    element_results['Failures'].append(param_name + " (read-only)")
            

            # Set BuildingCode to user input (once)
            building_code_param = get_parameter_by_name(element, "SI_TE_BO_BuildingCode")
            if building_code_param and building_code_value:
                if set_parameter_value(building_code_param, building_code_value):
                    element_results['BuildingCode'] = True
                else:
                    element_results['Failures'].append("SI_TE_BO_BuildingCode (read-only)")
            elif building_code_param:
                element_results['Failures'].append("SI_TE_BO_BuildingCode (no input)")


            # Set type parameters
            element_type = doc.GetElement(element.GetTypeId())
            if element_type:
                for param_name in type_parameters:
                    param = get_type_parameter_by_name(element_type, param_name)
                    if param and set_parameter_value(param, VAR_PAR_VAL_MANUFACTURER):
                        element_results['TypeParams'] += 1
                    elif param:
                        element_results['Failures'].append(param_name + " (type, read-only)")
            
            # Count success/failure
            if (element_results['InstanceParams'] > 0 or 
                element_results['TypeParams'] > 0 or 
                element_results['BuildingCode']):
                success_count += 1
            else:
                failure_count += 1
            
            results.append(element_results)
        
        t.Commit()
        
    except Exception as e:
        t.RollBack()
        print("Transaction failed: " + str(e))
        return 0, 0, []
    
    print("Option 2 completed: {} elements processed successfully, {} failed".format(success_count, failure_count))
    return success_count, failure_count, results

def export_results_to_excel(option1_results=None, option2_results=None, 
                          option1_success=0, option1_failure=0,
                          option2_success=0, option2_failure=0):
    """Export results to Excel file with separate columns for each option using prefixes and Report Type column"""
    try:
        # Use save_excel_file to get file path from user
        file_path = save_excel_file(title="Save Parameter Transfer Report")
        if not file_path:
            print("Export cancelled by user")
            return None
        
        # Prepare data for MiniExcel - create separate rows for each operation type
        report_data = []
        
        # Add summary header
        summary_data = OrderedDictionary()
        summary_data["Report_Type"] = "SUMMARY"
        summary_data["Element_ID"] = "Parameter Transfer Report"
        summary_data["Element_Category"] = "Generated: " + datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        summary_data["Workset_Original_Value"] = "Project: " + doc.Title
        summary_data["Workset_Transfer_Status"] = "Workset Success: " + (str(option1_success) if option1_results else "N/A")
        summary_data["Parameter_Instance_Count"] = "Workset Failed: " + (str(option1_failure) if option1_results else "N/A")
        summary_data["Parameter_Type_Count"] = "Parameter Success: " + (str(option2_success) if option2_results else "N/A")
        summary_data["Parameter_BuildingCode_Set"] = "Parameter Failed: " + (str(option2_failure) if option2_results else "N/A")
        summary_data["Parameter_Failures"] = ""
        report_data.append(summary_data)
        
        # Add separator row
        separator = OrderedDictionary()
        separator["Report_Type"] = "--- DETAILS ---"
        separator["Element_ID"] = "---"
        separator["Element_Category"] = "---"
        separator["Workset_Original_Value"] = "---"
        separator["Workset_Transfer_Status"] = "---"
        separator["Parameter_Instance_Count"] = "---"
        separator["Parameter_Type_Count"] = "---"
        separator["Parameter_BuildingCode_Set"] = "---"
        separator["Parameter_Failures"] = "---"
        report_data.append(separator)
        
        # Process Option 1 results (Workset Transfer)
        if option1_results:
            for result in option1_results:
                row_data = OrderedDictionary()
                row_data["Report_Type"] = "Workset Transfer"
                row_data["Element_ID"] = result['ElementId']
                row_data["Element_Category"] = result['Category']
                row_data["Workset_Original_Value"] = result['WorksetValue']
                row_data["Workset_Transfer_Status"] = result['Status']
                row_data["Parameter_Instance_Count"] = "N/A"
                row_data["Parameter_Type_Count"] = "N/A"
                row_data["Parameter_BuildingCode_Set"] = "N/A"
                row_data["Parameter_Failures"] = "N/A"
                report_data.append(row_data)
        
        # Process Option 2 results (BO Parameters)
        if option2_results:
            for result in option2_results:
                row_data = OrderedDictionary()
                row_data["Report_Type"] = "Set Parameter"
                row_data["Element_ID"] = result['ElementId']
                row_data["Element_Category"] = result['Category']
                row_data["Workset_Original_Value"] = "N/A"
                row_data["Workset_Transfer_Status"] = "N/A"
                row_data["Parameter_Instance_Count"] = result['InstanceParams']
                row_data["Parameter_Type_Count"] = result['TypeParams']
                row_data["Parameter_BuildingCode_Set"] = 'Yes' if result['BuildingCode'] else 'No'
                row_data["Parameter_Failures"] = '; '.join(result['Failures']) if result['Failures'] else 'None'
                report_data.append(row_data)
        
        # Clean file before saving
        if os.path.exists(file_path):
            os.remove(file_path)
        
        # Save using MiniExcel
        MiniExcel.SaveAs(file_path, report_data)
        
        # Calculate total rows for reporting
        workset_rows = len(option1_results) if option1_results else 0
        parameter_rows = len(option2_results) if option2_results else 0
        
        print("Report exported to: {}".format(file_path))
        print("Report structure: {} Workset Transfer rows, {} Set Parameter rows".format(workset_rows, parameter_rows))
        return file_path
        
    except Exception as e:
        print("Failed to export Excel file: {}".format(str(e)))
        return None

def main():
    """Main execution function"""
    print("Parameter Transfer Tool Starting...")
    
    # Show dialog to select options and Excel export preference
    selected_options, report_to_excel = show_parameter_transfer_dialog()
    
    if not selected_options:
        print("Operation cancelled by user.")
        return
    
    # Initialize results
    option1_results = None
    option2_results = None
    option1_success = option1_failure = 0
    option2_success = option2_failure = 0
    
    # Execute selected options
    if 2 in selected_options:
        option2_success, option2_failure, option2_results = option2_set_bo_parameters()

    if 1 in selected_options:
        option1_success, option1_failure, option1_results = option1_transfer_workset()

    # Conditionally export results to Excel based on checkbox
    excel_path = None
    if report_to_excel:
        print("Exporting results to Excel as requested...")
        excel_path = export_results_to_excel(
            option1_results, option2_results,
            option1_success, option1_failure,
            option2_success, option2_failure
        )
    else:
        print("Excel export skipped (not selected by user).")
    
    # Show completion message
    message = "Parameter transfer completed!\n\n"
    if 1 in selected_options:
        message += "Option 1 - Workset Transfer: {} successful, {} failed\n".format(
            option1_success, option1_failure)
    if 2 in selected_options:
        message += "Option 2 - BO Parameters: {} successful, {} failed\n".format(
            option2_success, option2_failure)
    
    if excel_path:
        message += "\nReport exported to:\n{}".format(excel_path)
        
        # Ask if user wants to open the folder
        open_folder = forms.alert(
            message + "\n\nOpen folder?", 
            title="Process Complete",
            options=["Yes", "No"]
        )
        if open_folder == "Yes":
            folder_path = os.path.dirname(excel_path)
            os.startfile(folder_path)
    else:
        if report_to_excel:
            message += "\nNote: Excel export was attempted but failed."
        else:
            message += "\nNote: Excel export was not requested."
        
        forms.alert(message, title="Process Complete")
    
    print("Process completed successfully!")

# Execute the script
if __name__ == "__main__":
    main() 
    output.close_others()