import clr
import System
from System import Type

from DCMvn.core import DB, UI, HOST_APP, get_output
from DCMvn.core.framework import List
from DCMvn.forms import wpfforms, alert
from DCMvn.revit.query import get_name

from pyrevit import script

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

output = get_output()
logger = script.get_logger()
doc = HOST_APP.doc


# parameter filter
def filterstring(value, parameter_id, evaluator):
    # type: (str, DB.ElementId, DB.FilterStringRuleEvaluator) -> DB.ElementParameterFilter
    parameter_value_provider = DB.ParameterValueProvider(parameter_id)
    string_rule = DB.FilterStringRule(valueProvider=parameter_value_provider,
                                      evaluator=evaluator, ruleString=value)
    return DB.ElementParameterFilter(string_rule)


# category filter
structuralframing = DB.ElementCategoryFilter(DB.BuiltInCategory.OST_StructuralFraming)
wall = DB.ElementCategoryFilter(DB.BuiltInCategory.OST_Walls)
column = DB.ElementCategoryFilter(DB.BuiltInCategory.OST_Columns)
ceiling = DB.ElementCategoryFilter(DB.BuiltInCategory.OST_Ceilings)
floor = DB.ElementCategoryFilter(DB.BuiltInCategory.OST_Floors)

# element type filter
type_only = DB.ElementIsElementTypeFilter()

# structural type filter
non_structural = DB.ElementStructuralTypeFilter(DB.Structure.StructuralType.NonStructural)
beam = DB.ElementStructuralTypeFilter(DB.Structure.StructuralType.Beam)

# action
all_link_instances = (DB.FilteredElementCollector(doc).OfClass(DB.RevitLinkInstance)
                      .Where(lambda x: DB.RevitLinkType.IsLoaded(doc, x.GetTypeId())))
all_link_docs = [x.GetLinkDocument() for x in all_link_instances]

selected_doc = wpfforms.SelectFromList.show(context=all_link_docs,
                                            title="Select the link",
                                            name_attr="Title",
                                            width=300,
                                            height=300,
                                            button_name="Select Link",
                                            show_min_button=False,
                                            show_maxrestore_button=False)


def GetElementInActiveView3DFromLinked(active_view_3d, link_doc, list_linked_elements):
    if active_view_3d.ViewType == DB.ViewType.ThreeD:
        active_sectionBox = active_view_3d.GetSectionBox()
        transform = active_sectionBox.Transform
        active_sectionBox_min = transform.OfPoint(active_sectionBox.Min)
        active_sectionBox_max = transform.OfPoint(active_sectionBox.Max)
        ol = DB.Outline(active_sectionBox_min, active_sectionBox_max)
        filter = DB.BoundingBoxIntersectsFilter(ol)
        elements = DB.FilteredElementCollector(link_doc, List[DB.ElementId]([a.Id for a in list_linked_elements])) \
            .WherePasses(filter).ToElements()
        return elements
    else:
        alert("Please select a 3D view", title="Error", warn_icon=True)
        return []


def is_contains_material(element, name="brick"):
    material_ids = element.GetMaterialIds(False)
    for material_id in material_ids:
        material = element.Document.GetElement(material_id)
        if name in material.Name.lower():
            return True
    return False


def is_contains_type(element, name="HistoricalSlab"):
    type_id = element.GetTypeId()
    if type_id != DB.ElementId.InvalidElementId:
        type_element = element.Document.GetElement(type_id)
        if name in get_name(type_element).lower():
            return True
    return False


def is_floor_thickness_equal(element, thickness=200):
    # type: (DB.Element, float) -> bool
    # thichness in milimeter
    thickness_param = element.LookupParameter("Thickness")
    if thickness_param:
        return thickness_param.AsDouble() == thickness / 304.8
    return False


brick_walls_inactive = (DB.FilteredElementCollector(selected_doc)
                        .OfCategory(DB.BuiltInCategory.OST_Walls)
                        .WhereElementIsNotElementType()
                        .Where(lambda x: not is_contains_material(x)))

beton_walls_inactive = (DB.FilteredElementCollector(selected_doc)
                        .OfCategory(DB.BuiltInCategory.OST_Walls)
                        .WhereElementIsNotElementType()
                        .Where(lambda x: not is_contains_material(x, name="beton")
                                         and not is_contains_material(x, name="concrete")))

historical_slab_floors = (DB.FilteredElementCollector(selected_doc)
                          .OfCategory(DB.BuiltInCategory.OST_Floors)
                          .WhereElementIsNotElementType()
                          .Where(lambda x: not is_contains_type(x) and not is_floor_thickness_equal(x)))

element_in_active_view = GetElementInActiveView3DFromLinked(doc.ActiveView, selected_doc, brick_walls_inactive)
# element_in_active_view = GetElementInActiveView3DFromLinked(doc.ActiveView, selected_doc, beton_walls_inactive)
# element_in_active_view = GetElementInActiveView3DFromLinked(doc.ActiveView, selected_doc, all_floors)

refs = element_in_active_view.Select(lambda x: DB.Reference(x).CreateLinkReference(
    [i for i in all_link_instances if selected_doc.Title in i.Name][0])).ToList()
uidoc = HOST_APP.uidoc
uidoc.Selection.SetReferences(refs)
uidoc.Application.PostCommand(UI.RevitCommandId.LookupPostableCommandId(UI.PostableCommand.HideElements))

