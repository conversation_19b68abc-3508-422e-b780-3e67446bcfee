# coding: utf-8
import os
from DCMvn.forms.wpfforms import WP<PERSON><PERSON><PERSON>ow
from DCMvn.core.framework import Debug
from ..services.preview_service import PreviewService
from ..viewmodel.main_viewmodel import MainViewModel  # noqa: F401

class MainView(WPFWindow):
    def __init__(self, view_model):
        # type: (MainViewModel) -> None
        xaml_path = os.path.join(os.path.dirname(__file__), "room_mapping_main_view.xaml")
        WPFWindow.__init__(self, xaml_path)
        self.DataContext = view_model
        self.view_model = view_model
        
        # Preview popup
        self.SpatialInfoButton.Click += self._on_spatial_info_click

        # Owner window
        self.view_model.owner_window = self
        self.Closing += self._on_window_closing
    
    def _on_spatial_info_click(self, sender, e):
        """Handle spatial info button click"""

        try:
            # Draw the preview in the popup
            preview_service = PreviewService()
            preview_service.draw_above_point_revit(self.AbovePointRevitCanvas)
            preview_service.draw_above_curve_revit(self.AboveCurveRevitCanvas)
            preview_service.draw_proximity_curve_revit(self.ProximityCurveRevitCanvas)
            preview_service.draw_proximity_point_revit(self.ProximityPointRevitCanvas)
            preview_service.draw_proximity_curve_generic(self.ProximityCurveGenericCanvas)
            preview_service.draw_proximity_point_generic(self.ProximityPointGenericCanvas)
            
            # Open popup
            self.DetectionPreviewPopup.IsOpen = True
        except Exception as ex:
            Debug.WriteLine("Preview error: {}".format(str(ex)))

    def _on_window_closing(self, sender, e):
        """Handle window closing event and cleanup resources"""
        try:
            # Clear the owner window reference
            if hasattr(self.view_model, 'owner_window'):
                self.view_model.owner_window = None
                
            # Cleanup view model resources
            if hasattr(self.view_model, 'cleanup'):
                self.view_model.cleanup()
                
            # Clear intersector cache when tool is closed
            from ..utils.geometry_utils import clear_intersector_cache
            clear_intersector_cache()
                
        except Exception as ex:
            Debug.WriteLine("Error during window cleanup: {}".format(str(ex))) 