# coding: utf-8
import clr
import System
from DCMvn.core import DB
from DCMvn.core.framework import Debug, List, Stopwatch  # noqa
from DCMvn.forms.mvvm import RelayCommand
from DCMvn.forms import alert
from ..ui.mapping_validation_dialog import MappingValidationDialog
from ..services import DetectionService, ReportService
from ..models import BaseMep, CurveBasedMep, PointBasedMep, RevitSpatial, GenericSpatial, DocumentWrapper, BaseSpatial  # noqa: F401

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)


class RunMappingCommand(RelayCommand):
    """Command to execute room number mapping operation with preview confirmation"""
    
    def __init__(self, main_viewmodel):
        self.__main_viewmodel = main_viewmodel
        RelayCommand.__init__(self, self.execute, self.can_execute)
        self.stopwatch = Stopwatch()
    
    def can_execute(self, parameter):
        """Check if the mapping command can be executed"""
        vm = self.__main_viewmodel
        
        has_selected_targets = vm.target_elements_by_category and any(
            tc.is_selected for tc in vm.target_elements_by_category
        )
        has_source_document = vm.selected_source_document is not None
        has_source_category = vm.selected_source_category is not None
        has_valid_spatial_elements = vm.collection_viewmodel.current_elements and vm.collection_viewmodel.current_elements.Count > 0
        
        return has_selected_targets and has_source_document and has_source_category and has_valid_spatial_elements
    
    def execute(self, parameter):
        """Execute the room number mapping operation with preview confirmation"""
        vm = self.__main_viewmodel
        
        Debug.WriteLine("=== Starting Room Number Mapping Process ===")
        
        # Step 1: Collect configuration data
        source_elements = vm.collection_viewmodel.current_elements
        source_count = source_elements.Count if source_elements else 0
        target_count = vm.target_element_count
        mapping_vm = vm.mapping_viewmodel
        
        # Get only valid mappings for preview
        valid_mappings = mapping_vm.get_valid_mappings()
        mapping_count = valid_mappings.Count if valid_mappings else 0
        mapping_details = []
        
        for i, mapping in enumerate(valid_mappings):
            source_param = mapping.source_param_name if hasattr(mapping, 'source_param_name') else 'Unknown'
            target_param = mapping.target_param_name if hasattr(mapping, 'target_param_name') else 'Unknown'
            mapping_detail = "  Mapping {}: {} -> {}".format(i + 1, source_param, target_param)
            mapping_details.append(mapping_detail)
        
        advanced_opts = vm.advanced_options
        
        # Validate we have valid mappings before proceeding
        if mapping_count == 0:
            Debug.WriteLine("No valid parameter mappings found - cannot proceed")
            return False
        
        # Step 2: Show preview dialog for user confirmation
        if not self._show_preview_confirmation(source_count, target_count, mapping_count, mapping_details, advanced_opts):
            Debug.WriteLine("User cancelled the mapping operation")
            return False
        
        # Step 3: User confirmed, proceed with actual mapping
        Debug.WriteLine("User confirmed - proceeding with mapping execution")
        return self._execute_mapping(advanced_opts)
    
    def _show_preview_confirmation(self, source_count, target_count, mapping_count, mapping_details, advanced_opts):
        """Show preview dialog and return True if user confirms, False if cancelled"""
        try:
            preview_message = build_preview_message(source_count, target_count, mapping_count, mapping_details, advanced_opts)
            vm = self.__main_viewmodel
            owner_window = vm.owner_window
            return MappingValidationDialog.show_preview_dialog(owner_window, preview_message)
            
        except Exception as ex:
            Debug.WriteLine("Error showing preview dialog: {}".format(str(ex)))
    
    def _execute_mapping(self, advanced_opts):
        """Execute the actual room number mapping operation using ActionEventHandler"""
        vm = self.__main_viewmodel
        self.stopwatch.Start()
        
        source_elements = vm.collection_viewmodel.current_elements
        target_elements = vm.target_elements_by_category.Where(lambda x: x.is_selected).SelectMany(lambda x: x.elements).ToList()
        
        if not source_elements or source_elements.Count == 0:
            return False
            
        if not target_elements or target_elements.Count == 0:
            return False
            
        valid_mappings = vm.mapping_viewmodel.get_valid_mappings()
        if not valid_mappings:
            return False

        mapping_data = prepare_mapping_data(
            source_elements, target_elements, valid_mappings, advanced_opts, vm.document, vm.selected_source_document
        )

        action_handler = vm.action_event_handler
        result_container = {'success': False, 'execution_time_ms': ""}

        def execute_transaction(ui_app):
            try:
                doc = ui_app.ActiveUIDocument.Document
                transaction_name = "Room Number Mapping - {} elements".format(
                    len(mapping_data['detection_results'])
                )

                with DB.Transaction(doc, transaction_name) as trans:
                    trans.Start()

                    successful_applications = 0
                    for result in mapping_data['detection_results']:
                        if apply_single_mapping_result(result, advanced_opts):
                            successful_applications += 1

                    result_container['success'] = successful_applications > 0
                    trans.Commit()

            except Exception as ex:
                Debug.WriteLine("Error in mapping transaction: {}".format(str(ex)))
                result_container['success'] = False
            finally:
                # Stop stopwatch and record execution time
                self.stopwatch.Stop()
                time_span = self.stopwatch.Elapsed
                elaspe_time = "{0:00}:{1:00}:{2:00}.{3:00}".format(time_span.Hours, time_span.Minutes,
                                                                   time_span.Seconds,
                                                                   time_span.Milliseconds)

                result_container['execution_time_ms'] = elaspe_time

            # Generate report with execution time
            if advanced_opts.log_unmapped_elements:
                mapping_data['report_service'].generate_mapping_report(
                    mapping_data['detection_results'],
                    advanced_opts,
                    result_container['execution_time_ms']
                )

            Debug.WriteLine("Mapping execution completed in {} ms".format(result_container['execution_time_ms']))

        action_handler.Raise(execute_transaction)

        success = result_container['success']
        show_completion_and_close(vm, success)
        return success


def create_mep_wrappers(elements):
    # type: (List[DB.Element]) -> List[object]
    """Create appropriate MEP wrapper based on element type."""
    mep_wrappers = []  # type: List[BaseMep]
    for element in elements:
        if hasattr(element, 'Location') and isinstance(element.Location, DB.LocationCurve):
            mep_wrappers.append(CurveBasedMep(element))
        elif hasattr(element, 'Location') and isinstance(element.Location, DB.LocationPoint):
            mep_wrappers.append(PointBasedMep(element))
        else:
            mep_wrappers.append(BaseMep(element))
    return mep_wrappers


def create_spatial_wrappers(elements, document_wrapper):
    # type: (List[DB.Element], DocumentWrapper) -> List[object]
    """Create appropriate spatial wrapper based on element type."""
    opts = DB.SpatialElementBoundaryOptions()
    opts.SpatialElementBoundaryLocation = DB.SpatialElementBoundaryLocation.Finish
    spatial_geometry_calculator = DB.SpatialElementGeometryCalculator(document_wrapper.document, opts)

    spatial_wrappers = []  # type: list[BaseSpatial]
    for element in elements:
        if isinstance(element, DB.SpatialElement):
            spatial_wrappers.append(RevitSpatial(element, document_wrapper, spatial_geometry_calculator))
        else:
            spatial_wrappers.append(GenericSpatial(element, document_wrapper))
    return [i for i in spatial_wrappers if i.is_valid]


def build_preview_message(source_count, target_count, mapping_count, mapping_details, advanced_opts):
    """Build the preview message string"""
    lines = [
        "📊 Element Summary:",
        "• Source Elements: {}".format(source_count),
        "• Target Elements: {}".format(target_count),
        "",
        "🔗 Valid Parameter Mappings: {}".format(mapping_count)
    ]
    
    if mapping_details:
        for detail in mapping_details[:3]:
            lines.append(detail.replace("  ", "• "))
        if len(mapping_details) > 3:
            lines.append("• ... and {} more mappings".format(len(mapping_details) - 3))
    elif mapping_count == 0:
        lines.append("• No valid mappings found!")
    lines.append("")
    
    lines.append("⚙️ Advanced Options:")
    active_options = []
    
    if advanced_opts.map_elements_above_spaces:
        active_options.append("• Map elements above spaces ({} mm)".format(advanced_opts.above_allowed_distance))
    if advanced_opts.use_proximity_mapping:
        active_options.append("• Proximity mapping ({} mm)".format(advanced_opts.nearest_allowed_distance))
    if advanced_opts.use_default_value_when_not_found:
        active_options.append("• Default value: '{}'".format(advanced_opts.default_value_when_not_found))
    if advanced_opts.allow_multiple_values:
        active_options.append("• Multiple values with separator: '{}'".format(advanced_opts.separator_value))
    if advanced_opts.override_existing_assignments:
        active_options.append("• Override existing assignments")
    if advanced_opts.log_unmapped_elements:
        active_options.append("• Generate detailed logs")
    
    if active_options:
        lines.extend(active_options)
    else:
        lines.append("• No advanced options enabled")
    
    lines.extend(["", "Ready to proceed with mapping?"])
    return "\n".join(lines)


def prepare_mapping_data(source_elements, target_elements, valid_mappings, advanced_opts, document, document_wrapper):
    """Prepare all mapping data before transaction execution"""
    detection_service = DetectionService(document, advanced_opts)
    report_service = ReportService()
    
    mep_element_wrappers = create_mep_wrappers(target_elements)
    spatial_element_wrappers = create_spatial_wrappers(source_elements, document_wrapper)

    detection_results = detection_service.detect_mep_spatial_relationships(
        mep_element_wrappers, spatial_element_wrappers, valid_mappings
    )
    return {
        'detection_results': detection_results,
        'report_service': report_service
    }


def apply_single_mapping_result(result, advanced_opts):
    """Apply a single mapping result within the current transaction"""
    try:
        if result.has_spatial_match:
            return result.apply_to_mep(
                advanced_opts.separator_value, 
                advanced_opts.allow_multiple_values,
                advanced_opts.override_existing_assignments
            )
                
        elif advanced_opts.use_default_value_when_not_found and result.parameter_pair:
            return result.parameter_pair.set_target_value(
                result.mep_element.element, 
                advanced_opts.default_value_when_not_found,
                advanced_opts.override_existing_assignments
            )
                
    except Exception as ex:
        Debug.WriteLine("Error applying mapping for element {}: {}".format(
            result.mep_element.id, str(ex)
        ))
        
    return False


def show_completion_and_close(vm, success):
    """Show completion notification and close the view"""
    action_handler = vm.action_event_handler

    def show_completion_and_close_action(ui_app):
        try:
            # Simple completion notification
            if success:
                alert("✅ Room Number Mapping Completed!\n\nCheck the detailed report for results.",
                      ok=True, warn_icon=False)
            else:
                alert("⚠️ Room Number Mapping Finished!\n\nCheck the detailed report for results.",
                      ok=True, warn_icon=True)

            # Always close the tool after notification
            if vm.owner_window:
                Debug.WriteLine("Closing Room Number Mapping tool")
                vm.owner_window.Close()

        except Exception as ex:
            Debug.WriteLine("Error in completion notification: {}".format(str(ex)))
            if vm.owner_window:
                vm.owner_window.Close()

    action_handler.Raise(show_completion_and_close_action)