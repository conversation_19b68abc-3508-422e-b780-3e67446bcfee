from DCMvn.core import DB
from DCMvn.core.framework import List


def is_physical_element(element):
    # type: (DB.Element) -> bool
    """
    Check if an element is a physical element.

    Args:
        element (DB.Element): The Revit element to check

    Returns:
        bool: True if the element is a physical element, False otherwise
    """
    if element.Category is None:
        return False

    if (DB.BuiltInCategory(element.Category.Id.IntegerValue) # noqa
            == DB.BuiltInCategory.OST_HVAC_Zones):
        return False

    return element.Category.CategoryType == DB.CategoryType.Model and element.Category.CanAddSubcategory


def get_element_physical_filter(document):
    # type: (DB.Document) -> DB.ElementMulticategoryFilter
    """
    Creates a filter that includes all model categories in the document.

    The filter is constructed by including all categories of type Model that allow
    subcategories, excluding HVAC Zones, Lines, and any categories containing
    "System" in their BuiltInCategory name.

    Args:
        document (DB.Document): The Revit document from which to retrieve the categories

    Returns:
        DB.ElementMulticategoryFilter: An ElementMulticategoryFilter that filters
                                      elements belonging to model categories
    """
    categories = document.Settings.Categories
    hvac_zone_id = DB.ElementId(DB.BuiltInCategory.OST_HVAC_Zones)
    line_id = DB.ElementId(DB.BuiltInCategory.OST_Lines)

    model_category_ids = List[DB.ElementId](
        [cat.Id for cat in categories
         if cat.CategoryType == DB.CategoryType.Model
         and cat.CanAddSubcategory
         and cat.Id != hvac_zone_id
         and cat.Id != line_id
         and not DB.BuiltInCategory(cat.Id.Value).ToString().Contains("System")]
    )

    return DB.ElementMulticategoryFilter(model_category_ids)
