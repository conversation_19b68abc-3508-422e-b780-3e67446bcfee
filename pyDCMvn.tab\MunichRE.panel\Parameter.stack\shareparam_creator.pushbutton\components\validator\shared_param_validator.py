# coding: utf-8
"""Validator for new register parameter"""
from DCMvn.core import DB, REVIT_VERSION, ApplicationServices, RevitExceptions  # noqa
from DCMvn.core.framework import Enum
from .. register_parameter import RegisterParameter # noqa
from .. parse_utils import SharedParamDef # noqa

def get_all_unit_types():
    all_unit_types = {}
    if REVIT_VERSION <= 2021:
        values = Enum.GetValues(DB.ParameterType)
        for value in values:
            try:
                label = DB.LabelUtils.GetLabelFor(value)
                all_unit_types[label] = value
            except Exception:
                pass
    else:
        attributes = dir(DB.SpecTypeId)
        for attribute in attributes:
            value = getattr(DB.SpecTypeId, attribute)
            if isinstance(value, DB.ForgeTypeId):
                try:
                    label = DB.LabelUtils.GetLabelForSpec(value)
                    all_unit_types[label] = value
                except RevitExceptions.ArgumentException:
                    pass
            elif isinstance(value, list):
                for sub_attr in value:
                    sub_type = getattr(DB.SpecTypeId, sub_attr)
                    sub_attrs = dir(sub_type)
                    for sub_attr in sub_attrs:
                        sub_value = getattr(sub_type, sub_attr)
                        if isinstance(sub_value, DB.ForgeTypeId):
                            label = DB.LabelUtils.GetLabelForSpec(sub_value)
                            all_unit_types[label] = sub_value
    return all_unit_types


class SharedParamValidator:
    """Validator for register parameter"""
    def __init__(self, register_param, shared_param_defs):
        # type: (RegisterParameter, list[SharedParamDef]) -> SharedParamValidator
        """Create RegisterParamValidator object

        Args:
            register_param (RegisterParameter): register parameter object
            allow_categories (List[DB.Category]): categories that be allowed to apply shared parameter
            loadable_families (List[DB.Family]): families that be allowed to apply shared parameter
            parameter_groups (list): Revit <= 2023 (DB.BuiltInParameterGroup) or Revit > 2023 (DB.ForgeTypeId)
        """
        self.__register_param = register_param  # type: RegisterParameter
        self.__shared_param_defs = shared_param_defs
        
        self.__shared_param_names = [i.name for i in self.__shared_param_defs]  # type: list[str]
        self.__shared_param_guids = [i.guid.ToString() for i in self.__shared_param_defs] # type: list[str]
        self.__shared_param_groups = [i.group for i in self.__shared_param_defs]  # type: list[str]
        
    @property
    def register_param(self):
        # type: () -> RegisterParameter
        """Get register parameter object

        Returns:
            RegisterParameter: register parameter object
        """
        return self.__register_param
                
    def check_guid(self):
        # type: () -> bool
        """Check GUID of register parameter is exist or not

        Returns:
            bool: True if GUID of register parameter is not exist, False otherwise
        """
        return not self.__register_param.guid \
                or self.__register_param.guid not in self.__shared_param_guids
    
    def check_name(self):
        # type: () -> bool
        """Check name of register parameter is exist or not

        Returns:
            bool: True if name of register parameter is not exist, False otherwise
        """
        return self.__register_param.name \
                and self.__register_param.name not in self.__shared_param_names
    
    def check_group(self):
        # type: () -> bool
        """Check group of register parameter is exist or not

        Returns:
            bool: True if group of register parameter is exist, False otherwise
        """
        return self.__register_param.group \
                and self.__register_param.group in self.__shared_param_groups
    
    def check_unit_type(self):
        # type: () -> bool
        """Check type of register parameter is valid or not

        Returns:
            bool: True if type of register parameter is exist, False otherwise
        """
        all_unit_types = get_all_unit_types()
        return self.__register_param.type_of_parameter \
                and self.__register_param.type_of_parameter in all_unit_types.keys()

    @property
    def can_be_create(self):
        # type: () -> bool
        """Check register parameter can be create or not

        Returns:
            bool: True if register parameter can be create, False otherwise
        """
        return (self.check_guid()
                and self.check_name()
                and self.check_unit_type())