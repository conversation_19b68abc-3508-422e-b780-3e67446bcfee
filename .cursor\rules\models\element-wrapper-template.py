# coding: utf-8
from DCMvn.core import DB
from DCMvn.core.framework import Debug

class ElementWrapper(object):
    """Base wrapper for Revit elements with enhanced functionality."""
    
    def __init__(self, element, document_wrapper=None):
        # type: (DB.Element, DocumentWrapper) -> None
        if not element or not element.IsValidObject:
            raise ValueError("Element must be valid")
            
        self.element = element
        self.document_wrapper = document_wrapper
        self._property_cache = {}
        
    @property
    def element_id(self):
        # type: () -> DB.ElementId
        """Safely get element ID."""
        return self.element.Id if self.is_valid else None
        
    @property
    def is_valid(self):
        # type: () -> bool
        """Check if wrapped element is still valid."""
        try:
            return self.element and self.element.IsValidObject
        except Exception:
            return False
            
    @property
    def category_name(self):
        # type: () -> str
        """Get element category name with fallback."""
        try:
            if self.element.Category:
                return self.element.Category.Name
        except Exception:
            pass
        return "Unknown"
        
    def get_parameter_value(self, parameter_name):
        # type: (str) -> object
        """Get parameter value with type-safe handling."""
        cache_key = "param_{}".format(parameter_name)
        if cache_key in self._property_cache:
            return self._property_cache[cache_key]
            
        try:
            if not self.is_valid:
                return None
                
            param = self.element.LookupParameter(parameter_name)
            if param and param.HasValue:
                if param.StorageType == DB.StorageType.String:
                    value = param.AsString()
                elif param.StorageType == DB.StorageType.Integer:
                    value = param.AsInteger()
                elif param.StorageType == DB.StorageType.Double:
                    value = param.AsDouble()
                elif param.StorageType == DB.StorageType.ElementId:
                    value = param.AsElementId()
                else:
                    value = None
                    
                self._property_cache[cache_key] = value
                return value
        except Exception as ex:
            Debug.WriteLine("Error getting parameter {}: {}".format(parameter_name, str(ex)))
            
        self._property_cache[cache_key] = None
        return None
        
    def set_parameter_value(self, parameter_name, value):
        # type: (str, object) -> bool
        """Set parameter value with type validation."""
        try:
            if not self.is_valid:
                return False
                
            param = self.element.LookupParameter(parameter_name)
            if not param or param.IsReadOnly:
                return False
                
            # Clear cache for this parameter
            cache_key = "param_{}".format(parameter_name)
            if cache_key in self._property_cache:
                del self._property_cache[cache_key]
                
            if param.StorageType == DB.StorageType.String:
                param.Set(str(value) if value is not None else "")
            elif param.StorageType == DB.StorageType.Integer:
                param.Set(int(value) if value is not None else 0)
            elif param.StorageType == DB.StorageType.Double:
                param.Set(float(value) if value is not None else 0.0)
            elif param.StorageType == DB.StorageType.ElementId:
                if isinstance(value, DB.ElementId):
                    param.Set(value)
                else:
                    return False
            else:
                return False
                
            return True
        except Exception as ex:
            Debug.WriteLine("Error setting parameter {}: {}".format(parameter_name, str(ex)))
            return False
            
    def get_transformed_geometry(self, geometry_func):
        # type: (callable) -> object
        """Get geometry with coordinate transformation applied."""
        try:
            raw_geometry = geometry_func()
            if not raw_geometry or not self.document_wrapper:
                return raw_geometry
                
            # Apply transformation for linked documents
            if self.document_wrapper.is_linked:
                if isinstance(raw_geometry, DB.XYZ):
                    return self.document_wrapper.transform_point(raw_geometry)
                elif isinstance(raw_geometry, DB.BoundingBoxXYZ):
                    return self.document_wrapper.transform_bounding_box(raw_geometry)
                    
            return raw_geometry
        except Exception as ex:
            Debug.WriteLine("Error transforming geometry: {}".format(str(ex)))
            return None
            
    def clear_cache(self):
        """Clear all cached properties."""
        self._property_cache.clear()