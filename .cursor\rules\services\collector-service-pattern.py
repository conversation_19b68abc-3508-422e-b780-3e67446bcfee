# coding: utf-8
from DCMvn.core import DB
from DCMvn.core.framework import List, Debug
from ..models import DocumentWrapper

class CollectorService(object):
    """Service for collecting and managing Revit elements efficiently."""
    
    def __init__(self, document):
        # type: (DB.Document) -> None
        self.document = document
        self.__cached_document_wrappers = None
        self.__cached_spatial_elements = {}
        
    def get_document_wrappers(self):
        # type: () -> List[DocumentWrapper]
        """Get all available documents (host + linked) as DocumentWrapper objects."""
        if self.__cached_document_wrappers is None:
            self.__cached_document_wrappers = self._collect_document_wrappers()
        return self.__cached_document_wrappers
        
    def get_spatial_elements(self, document_wrapper, use_active_view_only=False):
        # type: (DocumentWrapper, bool) -> List[DB.SpatialElement]
        """Collect spatial elements from specified document."""
        cache_key = "{}_{}_{}".format(
            document_wrapper.document.Title,
            document_wrapper.is_host,
            use_active_view_only
        )
        
        if cache_key not in self.__cached_spatial_elements:
            self.__cached_spatial_elements[cache_key] = self._collect_spatial_elements(
                document_wrapper, use_active_view_only
            )
            
        return self.__cached_spatial_elements[cache_key]
        
    def _collect_document_wrappers(self):
        # type: () -> List[DocumentWrapper]
        """Collect all available documents with proper wrapper."""
        wrappers = List[DocumentWrapper]()
        
        try:
            # Add host document
            host_wrapper = DocumentWrapper(self.document)
            wrappers.Add(host_wrapper)
            
            # Add linked documents
            link_collector = DB.FilteredElementCollector(self.document).OfClass(DB.RevitLinkInstance)
            for link_instance in link_collector:
                if link_instance.GetLinkDocument():
                    link_wrapper = DocumentWrapper(link_instance.GetLinkDocument(), link_instance)
                    wrappers.Add(link_wrapper)
                    
        except Exception as ex:
            Debug.WriteLine("Error collecting document wrappers: {}".format(str(ex)))
            
        return wrappers
        
    def _collect_spatial_elements(self, document_wrapper, use_active_view_only):
        # type: (DocumentWrapper, bool) -> List[DB.SpatialElement]
        """Collect spatial elements from document."""
        spatial_elements = List[DB.SpatialElement]()
        
        try:
            collector = DB.FilteredElementCollector(document_wrapper.document)
            if use_active_view_only and document_wrapper.is_host:
                collector = collector.OwnedByView(self.document.ActiveView.Id)
                
            collector = collector.OfClass(DB.SpatialElement).WhereElementIsNotElementType()
            
            for element in collector:
                if element and element.IsValidObject:
                    spatial_elements.Add(element)
                    
        except Exception as ex:
            Debug.WriteLine("Error collecting spatial elements: {}".format(str(ex)))
            
        return spatial_elements
        
    def clear_cache(self):
        """Clear all cached collections when document changes."""
        self.__cached_document_wrappers = None
        self.__cached_spatial_elements.clear()
        Debug.WriteLine("CollectorService cache cleared")