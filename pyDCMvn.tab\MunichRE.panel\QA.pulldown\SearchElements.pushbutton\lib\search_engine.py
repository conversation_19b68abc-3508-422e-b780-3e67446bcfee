from DCMvn.core import DB, REVIT_VERSION

def search_by_ifc_guid(raw_id, target_doc, search_type_guid=False):
    # type: (str, DB.Document, bool) -> list[DB.Element]  # noqa
    """
    Search for elements with the given IFC GUID in the target
    Args:
        raw_id (str): IFC GUID to search for
        target_doc (DB.Document): Document to search in
        search_type_guid (bool): Whether to search for type GUIDs

    Returns:
        list[DB.Element]: List of elements with the given IFC GUID

    """
    guid_provider = DB.ParameterValueProvider(DB.ElementId(DB.BuiltInParameter.IFC_GUID))
    element_filter = get_ifc_filter_rule(guid_provider, raw_id)
    guids_collector = DB.FilteredElementCollector(target_doc).WhereElementIsNotElementType().WherePasses(element_filter)

    if search_type_guid:
        type_guid_provider = DB.ParameterValueProvider(DB.ElementId(DB.BuiltInParameter.IFC_TYPE_GUID))
        type_element_filter = get_ifc_filter_rule(type_guid_provider, raw_id)
        type_guids_collector = DB.FilteredElementCollector(target_doc).WhereElementIsElementType().WherePasses(type_element_filter)

        return (guids_collector
                .UnionWith(type_guids_collector)
                .ToElements())

    return guids_collector.ToElements()


def get_ifc_filter_rule(rule_provider, raw_id):
    # type: (DB.ParameterValueProvider, str) -> DB.ElementParameterFilter
    if REVIT_VERSION >= 2022:
        filter_rule = DB.FilterStringRule(rule_provider, DB.FilterStringEquals(), raw_id)
    else:
        filter_rule = DB.FilterStringRule(rule_provider, DB.FilterStringEquals(), raw_id, True)  # noqa

    return DB.ElementParameterFilter(filter_rule)


def parse_raw_request(rows):
    # type: (list[str]) -> list[str]  # noqa
    """
    splitting lines by various delimiters and returning a flat list of items.
    Args:
        rows (list[str]): List of strings to split
    Returns:
        list[str]: List of items
    """
    items = []
    delimiters = ['\t', ';', ',', ' ']
    for row in rows:
        # For each line, try each delimiter in turn
        # and if it splits into multiple tokens, we use them
        used_split = False
        for i, delimiter in enumerate(delimiters):
            split = row.split(delimiter)
            # If more than 1 token, or last delimiter, or single token but changed
            if len(split) > 1 or i == len(delimiters) -1 or (len(split) == 1 and split[0] != row):
                # Add all non-empty tokens
                for s in split:
                    s = s.strip()
                    if s:
                        items.append(s)
                used_split = True
                break
        if not used_split:
            # If we somehow never split, just add the row
            row = row.strip()
            if row:
                items.append(row)
    return items


def get_element_by_id(raw_id, target_doc):
    # type: (str, DB.Document) -> DB.Element
    """
    If raw_id is a parsable integer, return that Element if it exists.
    Otherwise, return None.
    Args:
        raw_id (str): Element ID to search for
        target_doc (DB.Document): Document to search in
    Returns:
        DB.Element: Element if found, None otherwise
    """
    try:
        element_id = int(raw_id)
        element = target_doc.GetElement(DB.ElementId(element_id))
        return element
    except:  # noqa
        return None  # noqa