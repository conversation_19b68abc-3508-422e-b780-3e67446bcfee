# coding: utf-8
import clr
import System
from DCMvn.core import DB
from DCMvn.core.framework import List, Debug
from ..models import Parameter<PERSON>rapper, TargetCategory, DocumentWrapper  # noqa: F401
from ..utils import (
    get_element_physical_filter,
    get_filterable_parameters_items,
    get_applicable_parameters,
    group_elements_by_category,
    get_ifc_space_type_filter
)
from pyrevit.compat import get_elementid_value_func

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)  # noqa


class CollectorService(object):
    """
    Centralized service for collecting Revit elements and parameters with caching.
    Ensures each API call is made only once per session for optimal performance.
    """

    def __init__(self, document):
        # type: (DB.Document) -> None
        self.document = document

        # Cached collections
        self.__cached_document_wrappers = None
        self.__cached_spatial_categories = None
        self.__cached_all_physical_elements = None
        self.__cached_active_view_elements = None
        self.__cached_target_categories_all = None
        self.__cached_target_categories_active_view = None

        # Cached spatial elements by (doc_index, category_index)
        self.__cached_spatial_elements = {}

        # Cached parameters by category combinations
        self.__cached_source_parameters = {}  # Key: (doc_title, category_id)
        self.__cached_target_parameters = {}  # Key: frozenset of category_ids

    def get_document_wrappers(self):
        # type: () -> List[DocumentWrapper]
        """Get all document wrappers including host and linked documents (cached)"""
        if self.__cached_document_wrappers is None:
            self.__cached_document_wrappers = self._collect_document_wrappers()
        return self.__cached_document_wrappers

    def get_spatial_categories(self):
        # type: () -> List[DB.Category]
        """Get spatial categories (cached)"""
        if self.__cached_spatial_categories is None:
            self.__cached_spatial_categories = self._collect_spatial_categories()
        return self.__cached_spatial_categories

    def get_all_physical_elements(self):
        # type: () -> List[DB.Element]
        """Get all physical elements in document (cached)"""
        if self.__cached_all_physical_elements is None:
            self.__cached_all_physical_elements = self._collect_physical_elements(
                active_view_only=False
            )
        return self.__cached_all_physical_elements

    def get_active_view_physical_elements(self):
        # type: () -> List[DB.Element]
        """Get physical elements in active view only (cached)"""
        if self.__cached_active_view_elements is None:
            self.__cached_active_view_elements = self._collect_physical_elements(
                active_view_only=True
            )
        return self.__cached_active_view_elements

    def get_target_categories(self, active_view_only=False):
        # type: (bool) -> List[TargetCategory]
        """Get target categories grouped by category (cached)"""
        if active_view_only:
            if self.__cached_target_categories_active_view is None:
                elements = self.get_active_view_physical_elements()
                self.__cached_target_categories_active_view = (
                    group_elements_by_category(elements)
                )
            return self.__cached_target_categories_active_view
        else:
            if self.__cached_target_categories_all is None:
                elements = self.get_all_physical_elements()
                self.__cached_target_categories_all = group_elements_by_category(
                    elements
                )
            return self.__cached_target_categories_all

    def get_spatial_elements(self, doc_index, category_index):
        # type: (int, int) -> List[DB.Element]
        """Get spatial elements for specific document and category combination (cached)"""
        cache_key = (doc_index, category_index)

        if cache_key not in self.__cached_spatial_elements:
            self.__cached_spatial_elements[cache_key] = self._collect_spatial_elements(
                doc_index, category_index
            )

        return self.__cached_spatial_elements[cache_key]

    def get_document_wrapper_by_index(self, doc_index):
        # type: (int) -> DocumentWrapper | None
        """Get document wrapper by index"""
        document_wrappers = self.get_document_wrappers()
        if 0 <= doc_index < len(document_wrappers):
            return document_wrappers[doc_index]
        return None

    def get_source_parameters(self, document, category_id):
        # type: (DB.Document, DB.ElementId) -> List[ParameterWrapper]
        """Get source parameters for spatial category from specific document (cached)"""
        # Include document in cache key to differentiate between different link documents
        doc_title = document.Title if document else "unknown"
        category_value = get_elementid_value_func()(category_id)
        cache_key = (doc_title, category_value)

        if cache_key not in self.__cached_source_parameters:
            category_ids = List[DB.ElementId]([category_id])
            self.__cached_source_parameters[cache_key] = (
                get_filterable_parameters_items(document, category_ids)
            )

        return self.__cached_source_parameters[cache_key]

    def get_target_parameters(self, target_elements):
        # type: (List[DB.Element]) -> List[ParameterWrapper]
        """Get target parameters for target elements (cached)"""
        if not target_elements:
            return List[ParameterWrapper]()

        # Create cache key from unique category IDs
        category_ids = (
            target_elements.Where(lambda e: e.Category is not None)
            .Select(lambda e: get_elementid_value_func()(e.Category.Id))
            .Distinct()
            .OrderBy(lambda x: x)
            .ToList()
        )

        cache_key = frozenset(category_ids)

        if cache_key not in self.__cached_target_parameters:
            self.__cached_target_parameters[cache_key] = get_applicable_parameters(
                self.document, target_elements
            )

        return self.__cached_target_parameters[cache_key]

    def clear_cache(self):
        """Clear all cached data"""
        self.__cached_document_wrappers = None
        self.__cached_spatial_categories = None
        self.__cached_all_physical_elements = None
        self.__cached_active_view_elements = None
        self.__cached_target_categories_all = None
        self.__cached_target_categories_active_view = None
        self.__cached_spatial_elements.clear()
        self.__cached_source_parameters.clear()
        self.__cached_target_parameters.clear()

    def get_cache_info(self):
        """Get cache information for debugging"""
        return {
            "document_wrappers": self.__cached_document_wrappers is not None,
            "spatial_categories": self.__cached_spatial_categories is not None,
            "all_physical_elements": self.__cached_all_physical_elements is not None,
            "active_view_elements": self.__cached_active_view_elements is not None,
            "target_categories_all": self.__cached_target_categories_all is not None,
            "target_categories_active": self.__cached_target_categories_active_view
            is not None,
            "spatial_elements_combinations": len(self.__cached_spatial_elements),
            "source_parameter_categories": len(self.__cached_source_parameters),
            "target_parameter_combinations": len(self.__cached_target_parameters),
        }

    def _collect_document_wrappers(self):
        # type: () -> List[DocumentWrapper]
        """Collect all document wrappers including host and linked documents"""
        wrappers = List[DocumentWrapper]()

        # Add host document first
        host_wrapper = DocumentWrapper(self.document)
        wrappers.Add(host_wrapper)

        # Add linked documents
        link_instances = (
            DB.FilteredElementCollector(self.document)  # noqa
            .OfClass(DB.RevitLinkInstance)
            .WhereElementIsNotElementType()
            .Where(lambda x: DB.RevitLinkType.IsLoaded(self.document, x.GetTypeId()))
            .ToList()
        )

        for link_instance in link_instances:
            link_document = link_instance.GetLinkDocument()
            if link_document:
                link_wrapper = DocumentWrapper(link_document, link_instance)
                wrappers.Add(link_wrapper)

        return wrappers

    def _collect_spatial_categories(self):
        # type: () -> List[DB.Category]
        """Collect spatial categories"""
        categories = self.document.Settings.Categories
        return [
            categories.get_Item(DB.BuiltInCategory.OST_MEPSpaces),
            categories.get_Item(DB.BuiltInCategory.OST_Rooms),
            categories.get_Item(DB.BuiltInCategory.OST_GenericModel),
        ]

    def _collect_physical_elements(self, active_view_only=False):
        # type: (bool) -> List[DB.Element]
        """Collect physical elements"""
        if active_view_only and self.document.ActiveView:
            return (
                DB.FilteredElementCollector(self.document, self.document.ActiveView.Id)
                .WhereElementIsNotElementType()
                .WherePasses(get_element_physical_filter(self.document))
                .ToElements()
            )

        return (
            DB.FilteredElementCollector(self.document)
            .WhereElementIsNotElementType()
            .WherePasses(get_element_physical_filter(self.document))
            .ToElements()
        )

    def _collect_spatial_elements(self, doc_index, category_index):
        # type: (int, int) -> List[DB.Element]
        """Collect spatial elements for specific document and category"""
        document_wrappers = self.get_document_wrappers()
        spatial_cats = self.get_spatial_categories()

        if (
            doc_index < 0
            or doc_index >= len(document_wrappers)
            or category_index < 0
            or category_index >= len(spatial_cats)
        ):
            return List[DB.Element]()

        try:
            selected_wrapper = document_wrappers[doc_index]
            selected_category = spatial_cats[category_index]

            if (
                not selected_wrapper
                or not selected_wrapper.document
                or not selected_category
            ):
                return List[DB.Element]()

            if selected_category:
                return self._collect_elements(
                    selected_wrapper.document, selected_category.Id
                )
            else:
                return List[DB.Element]()

        except Exception as e:
            Debug.WriteLine("Error collecting spatial elements: {}".format(str(e)))
            return List[DB.Element]()

    def _collect_elements(self, document, category_id):
        # type: (DB.Document, DB.ElementId) -> List[DB.Element]
        """Collect spatial elements for specific category"""
        collector = DB.FilteredElementCollector(document)

        if category_id == DB.ElementId(DB.BuiltInCategory.OST_GenericModel):
            return (
                collector.OfCategoryId(category_id)
                .WhereElementIsNotElementType()
                .WherePasses(get_ifc_space_type_filter())
                .ToElements()
            )
        else:
            return (
                collector.OfCategoryId(category_id)
                .WhereElementIsNotElementType()
                .ToElements()
            )
