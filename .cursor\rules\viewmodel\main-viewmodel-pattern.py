# coding: utf-8
import clr
import System
from DCMvn.core import DB
from DCMvn.forms.mvvm import ViewModelBase, RelayCommand
from DCMvn.core.framework import ObservableCollection, Debug, List

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

class MainViewModel(ViewModelBase):
    """Main ViewModel pattern for DCMvn applications."""
    
    def __init__(self, document):
        # type: (DB.Document) -> None
        ViewModelBase.__init__(self)
        self.document = document
        
        # Initialize services
        self.__service = None  # Inject your services here
        
        # Initialize collections
        self.__items = ObservableCollection[object]()
        self.__selected_items = []
        
        # Initialize properties
        self.__selected_index = 0
        self.__is_loading = False
        
        # Initialize sub-ViewModels
        self.__sub_viewmodel = None  # Initialize sub-ViewModels
        
        # Initialize commands
        self.load_command = RelayCommand(self._execute_load, self._can_execute_load)
        self.process_command = RelayCommand(self._execute_process, self._can_execute_process)
        
    # Collection Properties
    @property
    def items(self):
        # type: () -> ObservableCollection[object]
        return self.__items
        
    @property 
    def selected_items(self):
        # type: () -> List[object]
        return self.__selected_items
        
    @selected_items.setter
    def selected_items(self, value):
        # type: (List[object]) -> None
        if self.__selected_items != value:
            self.__selected_items = value
            self.RaisePropertyChanged("selected_items")
            
    # Index Properties
    @property
    def selected_index(self):
        # type: () -> int
        return self.__selected_index
        
    @selected_index.setter
    def selected_index(self, value):
        # type: (int) -> None
        if self.__selected_index != value:
            self.__selected_index = value
            self.RaisePropertyChanged("selected_index")
            
    # State Properties
    @property
    def is_loading(self):
        # type: () -> bool
        return self.__is_loading
        
    @is_loading.setter
    def is_loading(self, value):
        # type: (bool) -> None
        if self.__is_loading != value:
            self.__is_loading = value
            self.RaisePropertyChanged("is_loading")
            
    # Command Methods
    def _execute_load(self, parameter):
        # type: (object) -> None
        """Execute load command."""
        try:
            self.is_loading = True
            Debug.WriteLine("Loading data...")
            
            # Load data using services
            # Update collections and properties
            
            self.RaisePropertyChanged("items")
            
        except Exception as ex:
            Debug.WriteLine("Error loading data: {}".format(str(ex)))
        finally:
            self.is_loading = False
            
    def _can_execute_load(self, parameter):
        # type: (object) -> bool
        """Check if load command can execute."""
        return not self.is_loading and self.document is not None
        
    def _execute_process(self, parameter):
        # type: (object) -> None
        """Execute process command."""
        try:
            Debug.WriteLine("Processing selected items...")
            
            # Process using services
            # Update UI state
            
        except Exception as ex:
            Debug.WriteLine("Error processing: {}".format(str(ex)))
            
    def _can_execute_process(self, parameter):
        # type: (object) -> bool
        """Check if process command can execute."""
        return not self.is_loading and len(self.selected_items) > 0