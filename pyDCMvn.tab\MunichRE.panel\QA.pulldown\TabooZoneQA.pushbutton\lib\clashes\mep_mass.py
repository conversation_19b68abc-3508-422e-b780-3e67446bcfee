import clr
from DCMvn.core import DB
from DCMvn.core.framework import System
from DCMvn.revit.geometry import GetElementMergedSolid

from .arc_curve import ArcCurve
from ..utils import get_element_from_ref

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

DOWN_RAY_DIRECTION = DB.XYZ(0, 0, -1)
UP_RAY_DIRECTION = DB.XYZ(0, 0, 1)

class MepMassObject:
    """ Provide a wrapper for Revit MEP Mass Element """
    def __init__(self, revit_mep_mass_element):
        # type: (DB.Element) -> None
        self._revit_mep_mass_element = revit_mep_mass_element
        self.__document = revit_mep_mass_element.Document

    @property
    def id(self):
        # type: () -> DB.ElementId
        return self._revit_mep_mass_element.Id
    
    @property
    def comment(self):
        # type: () -> str
        return self._revit_mep_mass_element.get_Parameter(DB.BuiltInParameter.ALL_MODEL_INSTANCE_COMMENTS).AsString()

    @property
    def workset(self):
        return self._revit_mep_mass_element.get_Parameter(DB.BuiltInParameter.ELEM_PARTITION_PARAM).AsValueString()

    def get_solid(self):
        return GetElementMergedSolid(self._revit_mep_mass_element)
    
    def get_raycast_element(self, intersector, up_direction=True):
        # type: (DB.ReferenceIntersector, bool) -> DB.Element
        """Get the nearest element that the arc element cast to

        Args:
            intersector (DB.ReferenceIntersector): the reference intersector
            up_direction (bool): the direction of the cast

        Returns:
            DB.Element: the nearest element that the arc element cast to
        """
        mass_solid = self.get_solid()
        if mass_solid:
            start = mass_solid.ComputeCentroid()
            if start:
                reference_context = intersector.FindNearest(start,
                                                            UP_RAY_DIRECTION if up_direction else DOWN_RAY_DIRECTION)
                if reference_context:
                    return get_element_from_ref(reference_context.GetReference(), self.__document)
                else:
                    reference_context = intersector.FindNearest(start,
                                                                DOWN_RAY_DIRECTION if not up_direction else UP_RAY_DIRECTION)
                    if reference_context:
                        return get_element_from_ref(reference_context.GetReference(), self.__document)
        return None  # noqa

    def to_element(self):
        # type: () -> DB.Element
        return self._revit_mep_mass_element

    def set_ifc_guid(self, guid):
        # type: (str) -> None
        dm_guid = self._revit_mep_mass_element.LookupParameter("SI_TE_DM_IfcGUID")
        if dm_guid:
            dm_guid.Set(guid)
        else:
            print("Parameter 'SI_TE_DM_IfGUID' not found")


class FloorMassObject(MepMassObject):
    """ Provide a wrapper for Revit MEP Mass Element in Floor """
    def __init__(self, revit_mep_mass_element):
        # type: (DB.Element) -> None
        MepMassObject.__init__(self, revit_mep_mass_element)


    def get_raycast_elements(self,
                             intersector,
                             arcs,
                             extend_center_distance=100 / 304.8,
                             extend_center_elevation=100 / 304.8,
                             limit_cast_range=600 / 304.8):
        # type: (DB.ReferenceIntersector, list[ArcCurve], float, float, float) -> list[DB.Element]  # noqa
        """
        Get the nearest element that the arc element cast to
        Args:
            intersector (DB.ReferenceIntersector): reference intersector
            arcs (list[ArcCurve]): list of arcs from the mass solid
            extend_center_distance (float): the distance to extend the center of the arc
            extend_center_elevation (float): the elevation to extend the center of the arc
            limit_cast_range (float): the limit of the cast range

        Returns:
            list[DB.Element]: the nearest element that the arc element cast to
        """

        if not arcs:
            return []
        arc = arcs.OrderByDescending(lambda x: x.arc.GetEndPoint(0).Z).First()  # type: ArcCurve # noqa

        center_extend = arc.extend_center(extend_center_distance, extend_center_elevation)
        start_extend_dir = arc.start_tangent_direction
        end_extend_dir = arc.end_tangent_direction

        start_cast_contexts = intersector.Find(center_extend, start_extend_dir)
        end_cast_contexts = intersector.Find(center_extend, end_extend_dir)

        start_cast_contexts = [i for i in start_cast_contexts if i.Proximity < limit_cast_range].OrderBy(lambda x: x.Proximity)  # type: list[DB.ReferenceWithContext]  # noqa
        end_cast_contexts = [i for i in end_cast_contexts if i.Proximity < limit_cast_range].OrderBy(lambda x: x.Proximity) # type: list[DB.ReferenceWithContext] # noqa

        if not start_cast_contexts and not end_cast_contexts:
            return []

        document = self._revit_mep_mass_element.Document
        ref_elements = (list(start_cast_contexts) + list(end_cast_contexts)).Select(lambda x: get_element_from_ref(x.GetReference(), document))  # noqa
        ref_uniques = ref_elements.GroupBy(lambda x: x.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()).Select(lambda x: x.First())

        if ref_uniques.Count() >= 2:
            return list(ref_uniques)[:2]
        elif ref_uniques.Count() == 1:
            return [ref_uniques.First()]
        return []

