# coding: utf-8
import clr
import System
from DCMvn.core import DB
from DCMvn.forms.mvvm import ViewModelBase, RelayCommand
from DCMvn.core.framework import ObservableCollection, Debug, List
from ..models import ParameterPair, ParameterWrapper
from ..services.collector_service import CollectorService  # noqa
from ..events.external_event_handler import ActionEventHandler

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)  # noqa


class MappingViewModel(ViewModelBase):
    def __init__(self, collector_service):
        # type: (CollectorService) -> None
        ViewModelBase.__init__(self)
        self.__collector_service = collector_service

        # External Event
        self.__external_event_handler = ActionEventHandler()

        # Collections for parameter mapping
        self.__parameter_mappings = ObservableCollection[ParameterPair]()
        self.__source_parameters = ObservableCollection[ParameterWrapper]()
        self.__target_parameters = ObservableCollection[ParameterWrapper]()
        
        # Commands
        self.__add_parameter_mapping_command = RelayCommand(
            self.execute_add_parameter_mapping, 
            self.can_execute_add_parameter_mapping
        )
        self.__remove_parameter_mapping_command = RelayCommand(
            self.execute_remove_parameter_mapping,
            self.can_execute_remove_parameter_mapping
        )
        
        # Initialize with a default mapping
        self._add_default_mapping()

    @property
    def external_event_handler(self):
        return self.__external_event_handler
    
    @property
    def parameter_mappings(self):
        return self.__parameter_mappings
    
    @property
    def source_parameters(self):
        return self.__source_parameters
    
    @property
    def target_parameters(self):
        return self.__target_parameters
    
    @property
    def add_parameter_mapping_command(self):
        return self.__add_parameter_mapping_command
    
    @property
    def remove_parameter_mapping_command(self):
        return self.__remove_parameter_mapping_command
    
    def update_source_parameters(self, source_elements):
        # type: (List[DB.Element]) -> None
        """Update available source parameters based on source elements"""
        self.__source_parameters.Clear()
        
        if not source_elements:
            return
        
        # Get category and document from first element
        first_element = source_elements.FirstOrDefault()
        if first_element and first_element.Category:
            # Use the document from the source element (handles link documents correctly)
            source_document = first_element.Document
            applicable_params = self.__collector_service.get_source_parameters(source_document, first_element.Category.Id)
            
            for param_wrapper in applicable_params:
                self.__source_parameters.Add(param_wrapper)
    
    def update_target_parameters(self, target_elements):
        # type: (List[DB.Element]) -> None
        """Update available target parameters based on target elements"""
        self.__target_parameters.Clear()
        
        if not target_elements:
            return
            
        applicable_params = self.__collector_service.get_target_parameters(target_elements)
        
        for param_wrapper in applicable_params:
            self.__target_parameters.Add(param_wrapper)
    
    def _add_default_mapping(self):
        """Add a default parameter mapping"""
        # Use host document for default mapping
        host_document = self.__collector_service.document
        applicable_params = self.__collector_service.get_source_parameters(host_document, DB.ElementId(DB.BuiltInCategory.OST_MEPSpaces))

        for param_wrapper in applicable_params:
            self.__source_parameters.Add(param_wrapper)
        default_pair = ParameterPair(self.__source_parameters, self.__target_parameters)
        self.__parameter_mappings.Add(default_pair)
    
    def can_execute_add_parameter_mapping(self, parameter):  # noqa
        """Check if add parameter mapping command can be executed"""
        return True
    
    def execute_add_parameter_mapping(self, parameter):
        """Execute add parameter mapping command"""
        new_pair = ParameterPair(self.__source_parameters, self.__target_parameters)
        self.__parameter_mappings.Add(new_pair)
    
    def can_execute_remove_parameter_mapping(self, parameter):
        """Check if remove parameter mapping command can be executed"""
        selected_pair = parameter  # type: ParameterPair
        return (selected_pair is not None and 
                selected_pair in self.__parameter_mappings and
                len(self.__parameter_mappings) > 1)  # Keep at least one mapping
    
    def execute_remove_parameter_mapping(self, parameter):
        """Execute remove parameter mapping command"""
        selected_pair = parameter  # type: ParameterPair
        if selected_pair and selected_pair in self.__parameter_mappings:
            self.__parameter_mappings.Remove(selected_pair)
    
    def get_valid_mappings(self):
        # type: () -> list[ParameterPair]
        """Get list of valid parameter mappings (both source and target set) without duplicates"""
        valid_mappings = List[ParameterPair]()
        seen_pairs = {}  # type: dict
        
        for mapping in self.__parameter_mappings:
            if mapping.is_valid():
                # Create unique key based on source and target parameter names
                mapping_key = "{}->{}".format(
                    mapping.source_param_name or "",
                    mapping.target_param_name or ""
                )
                
                # Only add if we haven't seen this mapping combination before
                if mapping_key not in seen_pairs:
                    seen_pairs[mapping_key] = True
                    valid_mappings.Add(mapping)
                    Debug.WriteLine("Added valid mapping: {}".format(mapping_key))
                else:
                    Debug.WriteLine("Skipped duplicate mapping: {}".format(mapping_key))
                
        return valid_mappings
    
    def execute_mapping(self, source_elements, target_elements):
        # type: (list[DB.Element], list[DB.Element]) -> int
        """Execute parameter mapping from source to target elements"""
        valid_mappings = self.get_valid_mappings()
        success_count = 0
        
        # For now, we'll do a simple 1:1 mapping demonstration
        # In a real implementation, you'd implement spatial relationship logic
        for i, target_element in enumerate(target_elements):
            if i < len(source_elements):
                source_element = source_elements[i]
                
                for mapping in valid_mappings:
                    if mapping.transfer_parameter(source_element, target_element):
                        success_count += 1
                        Debug.WriteLine("Successfully transferred {} -> {}".format(
                            mapping.source_param_name, mapping.target_param_name
                        ))
                    else:
                        Debug.WriteLine("Failed to transfer {} -> {}".format(
                            mapping.source_param_name, mapping.target_param_name
                        ))
        return success_count
    
    def clear_mappings(self):
        """Clear all parameter mappings and reset to default"""
        self.__parameter_mappings.Clear()
        self._add_default_mapping() 