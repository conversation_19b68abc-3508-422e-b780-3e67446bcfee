# coding: utf-8
import clr
from DCMvn.core import HOST_APP, get_output
from DCMvn.core.framework import System
from DCMvn.forms import alert

from components import constant
from components.ui.sharedParameterPath import get_shared_parameter_path
from components.parse_utils import shared_parameters_parser, register_parameters_parser
from components.creator.shared_creator import SharedCreator
from components.validator.shared_param_validator import SharedParamValidator

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

app = HOST_APP.app
output = get_output()

shared_parameter_path, register_list_path = get_shared_parameter_path(app)
shared_parameters_parse = shared_parameters_parser(app, shared_parameter_path)
register_parameters_parse = register_parameters_parser(excel_file_path=register_list_path, 
                                                       sheet_name=constant.DEFAULT_REGISTERLIST_SHEETNAME,
                                                       start_cell=constant.DEFAULT_REGISTERLIST_STARTCELL)
shared_validators_valid = []
shared_validators_invalid = []

for register_parameter in register_parameters_parse:
    validator = SharedParamValidator(register_parameter, shared_parameters_parse)
    if validator.can_be_create:
        shared_validators_valid.append(validator)
    else:
        shared_validators_invalid.append(validator)

confirm = alert("📝 Confirm\n\n👉 {} parameters will be create\n👉 {} parameters exist or not valid".
            format(len(shared_validators_valid), len(shared_validators_invalid)),
            ok=True, cancel=True, warn_icon=False)
if confirm:
    SharedCreator(app).create_shared(shared_validators_valid, output)
    open_file = alert("✅ Create shared parameter successful\n❓ Open shared parameter file directory?", 
                      yes=True, no=True, warn_icon=False)
    if open_file:
        System.Diagnostics.Process.Start(System.IO.Path.GetDirectoryName(shared_parameter_path))

        


