---
description: Comprehensive DCMvn Excel integration (MiniExcel setup, read/write patterns, validation, ViewModel integration)
globs: *.py
---

## Overview
- Single entry-point rule for Excel integration in DCMvn tools
- Covers: MiniExcel setup, safe read/write, unit conversion, data validation, persistent configuration, and ViewModel integration
- Leverages existing detailed rules. Start here, then dive deeper via links

Related rules:
- Core patterns: [dcmvn-excel-patterns.mdc](mdc:dcmvn-excel-patterns.mdc)
- MiniExcel assembly & utilities: [dcmvn-miniexcel-integration.mdc](mdc:dcmvn-miniexcel-integration.mdc)
- Data validation: [dcmvn-data-validation.mdc](mdc:dcmvn-data-validation.mdc)
- ViewModel patterns: [viewmodels/dcmvn-excel-viewmodel-patterns.mdc](mdc:../viewmodels/dcmvn-excel-viewmodel-patterns.mdc)

## Quick Start (imports and setup)
```python
# coding: utf-8
import clr
import os
from DCMvn.io import save_excel_file
from DCMvn.coreutils.assemblyhandler import load_miniexcel
from DCMvn.core.framework import IDictionary, Debug
from DCMvn.forms import alert

# Load MiniExcel assemblies once per module
load_miniexcel()
from MiniExcelLibs import MiniExcel
from MiniExcelLibs.OpenXml import OpenXmlConfiguration

# .NET collections
clr.AddReference("System.Collections.Specialized")
from System.Collections.Specialized import OrderedDictionary
from System.Collections.Generic import Dictionary
```

## Save pattern (safe, idempotent)
```python
def save_excel(file_path, data):
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
        MiniExcel.SaveAs(file_path, data)
        return True
    except Exception as ex:
        alert("Failed to save Excel file: {}".format(str(ex)))
        return False
```

## Read pattern (basic and advanced)
```python
def read_excel(path, sheet="Sheet1", use_header=True):
    try:
        if not os.path.exists(path):
            return None
        return MiniExcel.Query(path, useHeaderRow=use_header, sheetName=sheet) \
                         .Cast[IDictionary[str, object]]()
    except Exception as ex:
        Debug.WriteLine("Read Excel failed: {}".format(str(ex)))
        return None


def read_excel_advanced(path, sheet="Sheet1", start_cell="A1"):
    try:
        cfg = OpenXmlConfiguration()
        cfg.FillMergedCells = True
        return MiniExcel.Query(path, useHeaderRow=True, startCell=start_cell,
                               sheetName=sheet, configuration=cfg) \
                         .Cast[IDictionary[str, object]]()
    except Exception as ex:
        Debug.WriteLine("Read Excel (advanced) failed: {}".format(str(ex)))
        return None
```

## Data structures
- Single sheet: `List[OrderedDictionary]` to guarantee column ordering
- Multi-sheet: `Dictionary[str, object]` where keys are sheet names

```python
def ordered_row(pairs):
    row = OrderedDictionary()
    for k, v in pairs:
        row[k] = v
    return row

# Multi-sheet container
sheets = Dictionary[str, object]()
sheets["Summary"] = summary_rows  # List[OrderedDictionary]
sheets["Details"] = detail_rows
```

## Unit conversion (Revit internal → human-readable)
```python
from DCMvn.core import DB

area_m2 = DB.UnitUtils.ConvertFromInternalUnits(area_internal, DB.UnitTypeId.SquareMeters)
length_mm = DB.UnitUtils.ConvertFromInternalUnits(length_internal, DB.UnitTypeId.Millimeters)
volume_m3 = DB.UnitUtils.ConvertFromInternalUnits(volume_internal, DB.UnitTypeId.CubicMeters)
```

## Validation hooks
- Validate data before save and after read (shape, required columns, types)
- Use the dedicated validation helpers and patterns:
  - [dcmvn-data-validation.mdc](mdc:dcmvn-data-validation.mdc)

## ViewModel integration (persistent paths, UX)
- Persist user-selected folders/paths with `get_config_property`/`set_config_property`
- Provide success/failure alerts; offer to open output folder on success
- Full ViewModel workflow: [viewmodels/dcmvn-excel-viewmodel-patterns.mdc](mdc:../viewmodels/dcmvn-excel-viewmodel-patterns.mdc)

## End-to-end export example
```python
def export_report(items):
    # build data
    rows = []
    for it in items:
        rows.append(ordered_row([
            ("Element ID", it.Id.IntegerValue),
            ("Name", it.Name),
            ("Category", it.Category.Name)
        ]))

    # ask user for destination
    path = save_excel_file(title="Save Report")
    if not path:
        return False

    ok = save_excel(path, rows)
    if ok:
        open_folder = alert("Exported:\n{}\n\nOpen folder?".format(path), yes=True, no=True)
        if open_folder:
            os.startfile(os.path.dirname(path))
    return ok
```

## Best practices
- Wrap MiniExcel operations in try/except
- Log development details with `Debug.WriteLine` and show user-friendly alerts
- Pre-build data structures, batch operations, and minimize I/O
- Use `OrderedDictionary` only when column order is important

See details: [dcmvn-excel-patterns.mdc](mdc:dcmvn-excel-patterns.mdc)
