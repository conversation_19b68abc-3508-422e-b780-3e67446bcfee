# coding: utf-8
import clr
import System
import os
from collections import defaultdict

from pyrevit.output import PyRevitOutputWindow # noqa

from DCMvn.core import DB
from DCMvn.core.framework import ObservableCollection, List
from DCMvn.forms.revit import alert
from DCMvn.forms.mvvm import RelayCommand, ViewModelBase
from DCMvn.revit.ui import get_mainwindow
from DCMvn.io import pick_folder, get_config_property, set_config_property

from MunichRE.excel_reader import IfcExcelReader
from MunichRE.ifc_entity import IfcEntity
from MunichRE.constants import IGNORE_MISSING_EXCEL, EXCEL_EXTENSION, \
                                IGNORE_MISSING_EXCEL_EXTENSION, GLOBAL_ID, NOT_APPLY
from models.revit_entity import RevitEntity

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

separator = "##########################################################################################\n"

class GuidAlignmentViewModel(ViewModelBase):
    def __init__(self, pyrevit_output, doc, ifc_sheet_name="AllData"):
        # type: (PyRevitOutputWindow, DB.Document, str) -> None
        ViewModelBase.__init__(self)

        # Revit Document
        self._doc = doc
        self._output = pyrevit_output

        # Excel Config
        self._config_section = "pyDCMvn.MunichRE.GeometryAlignment"
        self._excel_folder_path_property = "EXCEL_FOLDER_PATH"
        self._selected_excel_index_property = "SELECTED_EXCEL_INDEX"
        self._excel_folder_path = get_config_property(
            self._config_section, self._excel_folder_path_property
        )
        self._selected_excel_index = get_config_property(
            self._config_section, self._selected_excel_index_property
        )
        if self._selected_excel_index is None:
            self._selected_excel_index = 0

        self._excelsource = []
        self._ifc_sheet_name = ifc_sheet_name

        # Data collections
        self._ifc_entity = ObservableCollection[System.Object]()
        self._no_ignore_ifc_entity = 0
        self._rvt_model_elements = {}
        self._filtered_ifc_entity = ObservableCollection[System.Object]()
        self._filtered_rvt_model_elements = {}

        # Full unfiltered IFC data
        self._all_ifc_entities = []  # List to store all IFC data including duplicates

        # Dictionaries for fast lookups (IFC)
        self._ifc_entity_dict = {}  # GUID -> IfcEntity
        self._ifc_by_class = {}  # class -> set(GUIDs)
        self._ifc_by_level = {}  # level -> set(GUIDs)
        self._ifc_by_category = {}  # category -> set(GUIDs)
        self._ifc_by_system = {}  # system -> set(GUIDs)

        # Dictionary for fast lookups (Revit)
        self._rvt_by_category = {}  # category -> set(Element)
        self._rvt_by_level = {}  # level -> set(Element)

        # Filter options
        self._ifc_classes = []
        self._ifc_level = []
        self._rvt_categories = []
        self._rvt_systems = []

        self._selected_ifc_class_index = 0
        self._selected_ifc_level_index = 0
        self._selected_rvt_category_index = 0
        self._selected_rvt_system_index = 0

        # Input options
        self._guids_input = ""
        self._is_guids_input_selected = False

        # Counters
        self._no_ifc_elements = 0
        self._no_rvt_elements = 0
        self._no_filtered_ifc_elements = 0
        self._no_filtered_rvt_elements = 0

        # Conditions
        self._active_view_only = False
        self._allowable_offset = 10.0
        self._include_not_found = True
        self._matched_elements_only = False

        # Command
        self._run_command = RelayCommand(
            self.execute_run_command, self.can_execute_run_command
        )
        self._pick_excel_folder_command = RelayCommand(
            self.execute_pick_excel_folder_command,
            self.can_execute_pick_excel_folder_command,
        )

        # Load data only once
        if self._excel_folder_path and os.path.exists(self._excel_folder_path):
            self.load_excel_folder()

    # region ExcelSource

    @property
    def ExcelSource(self):
        return self._excelsource

    @ExcelSource.setter
    def ExcelSource(self, value):
        self._excelsource = value
        self.RaisePropertyChanged("ExcelSource")

    @property
    def ExcelFolderPath(self):
        return self._excel_folder_path

    @ExcelFolderPath.setter
    def ExcelFolderPath(self, value):
        self._excel_folder_path = value
        self.RaisePropertyChanged("ExcelFolderPath")

    @property
    def SelectedExcelIndex(self):
        return self._selected_excel_index

    @SelectedExcelIndex.setter
    def SelectedExcelIndex(self, value):
        if value < 0 or (self._excelsource and value >= len(self._excelsource)):
            # ignore invalid index
            return
        self._selected_excel_index = value
        self.RaisePropertyChanged("SelectedExcelIndex")
        set_config_property(
            self._config_section, self._selected_excel_index_property, value
        )
        self.load_ifc_data()

    # endregion

    # region Total Counts

    @property
    def NoIfcElements(self):
        return len(self._all_ifc_entities)

    @property
    def NoIgnoreElements(self):
        return self._no_ignore_ifc_entity

    @property
    def NoRvtElements(self):
        return len(self._rvt_model_elements.keys())

    @property
    def IsIFCExcelValid(self):
        return self.NoIfcElements > 0

    # endregion

    # region Filtered Options
    # IFCClass
    @property
    def IFCClass(self):
        return self._ifc_classes

    @IFCClass.setter
    def IFCClass(self, value):
        self._ifc_classes = value
        self.RaisePropertyChanged("IFCClass")

    @property
    def SelectedIFCClassIndex(self):
        return self._selected_ifc_class_index

    @SelectedIFCClassIndex.setter
    def SelectedIFCClassIndex(self, value):
        self._selected_ifc_class_index = value
        self.RaisePropertyChanged("SelectedIFCClassIndex")
        self.update_filtered_counts()

    # IFCLevel
    @property
    def IFCLevel(self):
        return self._ifc_level

    @IFCLevel.setter
    def IFCLevel(self, value):
        self._ifc_level = value
        self.RaisePropertyChanged("IFCLevel")

    @property
    def SelectedIFCLevelIndex(self):
        return self._selected_ifc_level_index

    @SelectedIFCLevelIndex.setter
    def SelectedIFCLevelIndex(self, value):
        self._selected_ifc_level_index = value
        self.RaisePropertyChanged("SelectedIFCLevelIndex")
        self.update_filtered_counts()

    # RvtCategory
    @property
    def RvtCategory(self):
        return self._rvt_categories

    @RvtCategory.setter
    def RvtCategory(self, value):
        self._rvt_categories = value
        self.RaisePropertyChanged("RvtCategory")

    @property
    def SelectedRvtCategoryIndex(self):
        return self._selected_rvt_category_index

    @SelectedRvtCategoryIndex.setter
    def SelectedRvtCategoryIndex(self, value):
        self._selected_rvt_category_index = value
        self.RaisePropertyChanged("SelectedRvtCategoryIndex")
        self.update_filtered_counts()

    # RvtSystem
    @property
    def RvtSystem(self):
        return self._rvt_systems

    @RvtSystem.setter
    def RvtSystem(self, value):
        self._rvt_systems = value
        self.RaisePropertyChanged("RvtSystem")

    @property
    def SelectedRvtSystemIndex(self):
        return self._selected_rvt_system_index

    @SelectedRvtSystemIndex.setter
    def SelectedRvtSystemIndex(self, value):
        self._selected_rvt_system_index = value
        self.RaisePropertyChanged("SelectedRvtSystemIndex")
        self.update_filtered_counts()

    # endregion

    # region GuidsInput Options

    @property
    def IsGuidInputSelected(self):
        return self._is_guids_input_selected

    @IsGuidInputSelected.setter
    def IsGuidInputSelected(self, value):
        self._is_guids_input_selected = value
        self.RaisePropertyChanged("IsGuidInputSelected")
        self.update_filtered_counts()

    @property
    def GuidsInput(self):
        return self._guids_input

    @GuidsInput.setter
    def GuidsInput(self, value):
        self._guids_input = value
        self.RaisePropertyChanged("GuidsInput")
        self.update_filtered_counts()

    # endregion

    # region Conditions

    @property
    def IsCheckActiveView(self):
        return self._active_view_only

    @IsCheckActiveView.setter
    def IsCheckActiveView(self, value):
        self._active_view_only = value
        self.RaisePropertyChanged("IsCheckActiveView")
        self.load_rvt_data()
        self.update_filtered_counts()

    @property
    def IsCheckMatchedElements(self):
        return self._matched_elements_only

    @IsCheckMatchedElements.setter
    def IsCheckMatchedElements(self, value):
        self._matched_elements_only = value
        self.RaisePropertyChanged("IsCheckMatchedElements")
        self.load_rvt_data()
        self.update_filtered_counts()

    # endregion

    # region Filtered Counts
    @property
    def NoFilteredIfcElements(self):
        return len(self._filtered_ifc_entity)

    @NoFilteredIfcElements.setter
    def NoFilteredIfcElements(self, value):
        self._no_filtered_ifc_elements = value
        self.RaisePropertyChanged("NoFilteredIfcElements")

    @property
    def NoFilteredRvtElements(self):
        return len(self._filtered_rvt_model_elements.keys())

    @NoFilteredRvtElements.setter
    def NoFilteredRvtElements(self, value):
        self._no_filtered_rvt_elements = value
        self.RaisePropertyChanged("NoFilteredRvtElements")

    # endregion

    # region Commands

    @property
    def RunCommand(self):
        return self._run_command

    def can_execute_run_command(self, param):  # noqa
        return len(self._filtered_rvt_model_elements) > 0

    def execute_run_command(self, param):  # noqa
        try:
            filter_applied = any(index > 0 for index in [
                    self.SelectedIFCClassIndex,
                    self.SelectedIFCLevelIndex,
                    self.SelectedRvtCategoryIndex,
                    self.SelectedRvtSystemIndex
                ])
            excel_guids = [entity.id for entity in (self._filtered_ifc_entity if filter_applied else self._all_ifc_entities)]  # type: List[str]
            excel_guids_original = excel_guids 
            revit_entities = list(self._filtered_rvt_model_elements.values())  # type: List[RevitEntity]
            revit_entities_original = revit_entities

            # 1. Check for duplicated GUIDs in Excel
            excel_duplicated_guid = set([x for x in excel_guids if excel_guids.count(x) > 1])
            # excel_duplicated_guid_count = {guid: excel_guids.count(guid) for guid in excel_duplicated_guid}
            excel_guids = [guid for guid in excel_guids if guid not in excel_duplicated_guid]

            # 2. Check for empty GUIDs in Revit
            rvt_empty_guid = [e for e in revit_entities if not e.revit_guid]
            revit_entities = [e for e in revit_entities if e.revit_guid]

            # 3. Check for duplicated GUIDs in Revit
            rvt_guids = [e.revit_guid for e in revit_entities]
            rvt_duplicated_guid = set([x for x in rvt_guids if rvt_guids.count(x) > 1])
            rvt_duplicated_guid_count = {guid: rvt_guids.count(guid) for guid in rvt_duplicated_guid}
            rvt_duplicated_guid_dict = {guid: [e for e in revit_entities if e.revit_guid == guid] for guid in rvt_duplicated_guid}
            revit_entities = [e for e in revit_entities if e.revit_guid not in rvt_duplicated_guid]

            # 4. Compare GUIDs between Excel and Revit elements
            rvt_matched_elements = {}
            ifc_entities = [e for e in self._all_ifc_entities if e.id in excel_guids]
            for revit_entity in revit_entities:
                ifc_entity = next((e for e in ifc_entities if e.id == revit_entity.revit_guid), None)
                if ifc_entity:
                    rvt_matched_elements[revit_entity.revit_guid] = (ifc_entity, revit_entity)

            rvt_matched_guid = rvt_matched_elements.keys()

            # 5. Identify unmatched elements in Revit
            rvt_unmatched_elements = [e for e in revit_entities if e.revit_guid not in rvt_matched_guid]

            # 6. Find GUIDs present in Excel but missing in Revit
            excel_missing_in_revit = [guid for guid in excel_guids if guid not in rvt_matched_guid]

            # Output results
            output = self._output

            if excel_duplicated_guid:
                output.print_html(separator + '<strong style="color: red">[Excel] Duplicated GUIDs:</strong>')
                for guid in excel_duplicated_guid:
                    print(" {0}".format(guid))

            level_class_to_missing_guids = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
            level_class_to_missing_guids_count = defaultdict(lambda: defaultdict(int))
            level_to_total_count = defaultdict(int)
            if excel_missing_in_revit:
                for ifc_entity in self._ifc_entity:
                    if ifc_entity.id in excel_missing_in_revit:
                        level_class_to_missing_guids[ifc_entity.ifc_level][ifc_entity.ifc_class][ifc_entity.id] = ifc_entity.id
                        level_class_to_missing_guids_count[ifc_entity.ifc_level][ifc_entity.ifc_class] += 1
                        level_to_total_count[ifc_entity.ifc_level] += 1

                output.print_html(separator + '<strong style="color: red">[Excel] GUIDs missing in Revit:</strong>')
                for ifc_level in sorted(level_class_to_missing_guids.keys()):
                    output.print_html('<strong style="color: blue"> IfcStorey: {0}</strong> {1}'.format(ifc_level,separator))
                    for ifc_class in sorted(level_class_to_missing_guids[ifc_level].keys()):
                        output.print_html('<strong> IfcElement: {0}</strong> {1}'.format(ifc_class,separator))
                        for guid in level_class_to_missing_guids[ifc_level][ifc_class]:
                            print(" {0}".format(guid))

            if rvt_empty_guid:
                category_to_empty_guid = defaultdict(list)
                for e in rvt_unmatched_elements:
                    category_to_empty_guid[e.revit_category].append(e)

                output.print_html(separator + '<strong style="color: red">[Revit] Elements with empty GUIDs:</strong>')
                for category, elements in category_to_empty_guid.items():
                    output.print_html('<strong>Category: {0}</strong> {1}'.format(category,separator))
                    for e in elements:
                        print(" {0}".format(
                                self._output.linkify(e.id)
                            ))

            if rvt_duplicated_guid:
                output.print_html(separator + '<strong style="color: red">[Revit] Duplicated GUIDs:</strong>')
                for guid, elements in rvt_duplicated_guid_dict.items():
                    output.print_html('<strong>- GUID: {0}</strong>'.format(guid))
                    for e in elements:
                        print("{0}, Category: {1}".format(
                                self._output.linkify(e.id),
                                e.revit_category
                            ))

            if rvt_unmatched_elements:
                category_to_unmatched = defaultdict(list)
                for e in rvt_unmatched_elements:
                    category_to_unmatched[e.revit_category].append(e)

                output.print_html(separator + '<strong style="color: red">[Revit] Unmatched GUIDs:</strong>')
                for category, elements in category_to_unmatched.items():
                    output.print_html('<strong>Category: {0}</strong>'.format(category) + " " + separator)
                    for e in elements:
                        print(" {0}".format(
                                self._output.linkify(e.id, e.revit_guid)
                            ))

            # Output for matched elements with a condition to display based on checkbox
            if self.IsCheckMatchedElements:
                output.print_html(separator + '<strong style="color: green">[Matched Elements]</strong>')
                for ifc_entity, revit_entity in rvt_matched_elements.values():
                    print("  - IFC: GUID={0}, RVT_Category={1}, IfcElement={2}".format(
                            ifc_entity.id,
                            ifc_entity.rvt_category,
                            ifc_entity.ifc_class
                        ))
                    print("    Matched with Revit: GUID={0}, Category={1}".format(
                            self._output.linkify(revit_entity.id, revit_entity.revit_guid),
                            revit_entity.revit_category
                        ))
            else:
                pass
            # Summary report
            total_excel_guids = len(excel_guids_original)
            total_revit_elements = len(revit_entities_original)
            rvt_matched_count = len(rvt_matched_elements)
            rvt_matched_percentage = (rvt_matched_count * 100) / total_revit_elements if total_revit_elements > 0 else 0
            excel_matched_count = len(rvt_matched_guid)
            excel_matched_percentage = (excel_matched_count * 100) / total_excel_guids if total_excel_guids > 0 else 0

            output.print_html(separator + '<strong style="color: blue">Summary Report:</strong>')
            output.print_html('<strong style="color: blue">[Excel] Total GUIDs: {0}</strong>'.format(total_excel_guids))
            output.print_html("- Total duplicated GUIDs: <strong style=\"color: red\">{0}</strong>".format(len(excel_duplicated_guid)))
            output.print_html("- Total missing GUIDs in Revit: <strong style=\"color: red\">{0}</strong>".format(len(excel_missing_in_revit)))
            print("- Missing GUIDs per Storey:")
            for ifc_level in sorted(level_to_total_count.keys()):
                output.print_html(" " + " · Storey {}: ".format(ifc_level) + '<strong style="color: red">{}</strong>'.format(level_to_total_count[ifc_level]))
            output.print_html('<strong style="color: green">- Total matched GUIDs: {0} out of {1} ({2:.2f}%)</strong>'.format(excel_matched_count, total_excel_guids, float(excel_matched_percentage)))
            output.print_html('<strong style="color: blue">[Revit] Total Elements: {0}</strong>'.format(total_revit_elements))
            output.print_html("- Total elements with empty GUIDs: <strong style=\"color: red\">{0}</strong>".format(len(rvt_empty_guid)))
            output.print_html("- Total elements with duplicated GUIDs: <strong style=\"color: red\">{0}</strong> in (<strong style=\"color: red\">{1}</strong>) duplicated GUIDs".format(sum(rvt_duplicated_guid_count[guid] for guid in rvt_duplicated_guid), len(rvt_duplicated_guid)))
            output.print_html("- Total elements with unmatched GUIDs: <strong style=\"color: red\">{0}</strong>".format(len(rvt_unmatched_elements)))
            output.print_html('<strong style="color: green">- Total elements with matched GUIDs: {0} out of {1} ({2:.2f}%)</strong>'.format(rvt_matched_count, total_revit_elements, float(rvt_matched_percentage)))
            self._output.close_others()
        except Exception:
            import traceback
            alert("Error: {}".format(traceback.format_exc()))
            return

    @property
    def PickExcelFolderCommand(self):
        return self._pick_excel_folder_command

    def can_execute_pick_excel_folder_command(self, param):  # noqa
        return True

    def execute_pick_excel_folder_command(self, param):  # noqa
        revit_main_window = get_mainwindow()
        folder = pick_folder("Select IFC Excel Folder", owner=revit_main_window)
        if folder and os.path.exists(folder):
            set_config_property(
                self._config_section, self._excel_folder_path_property, folder
            )
            self._excel_folder_path = folder
            self.load_excel_folder()
        else:
            alert("No folder selected.")
            self.clear_data()
            self.clear_filtered_data()
        self.RaisePropertyChanged("ExcelFolderPath")

    # endregion

    # region Data Loading

    def clear_data(self):
        self._ifc_entity.Clear()
        self._rvt_model_elements.clear()


    def clear_filtered_data(self):
        self._filtered_ifc_entity.Clear()
        self._filtered_rvt_model_elements.clear()

    def load_excel_folder(self):
        self._excelsource = [
            f
            for f in os.listdir(self._excel_folder_path)
            if f.endswith(EXCEL_EXTENSION)
            and not (f.startswith("~") or IGNORE_MISSING_EXCEL in f)
        ]
        self.RaisePropertyChanged("ExcelSource")
        # Only load if there's a valid index
        if self._excelsource and 0 <= self._selected_excel_index < len(
            self._excelsource
        ):
            self.load_ifc_data()

    def load_rvt_data(self):
        self._rvt_model_elements.clear()
        self._filtered_rvt_model_elements.clear()

        # Load Revit elements once
        categories = self._doc.Settings.Categories
        excluded_ids = [
            DB.ElementId(DB.BuiltInCategory.OST_Cameras),
            DB.ElementId(DB.BuiltInCategory.OST_Views),
            DB.ElementId(DB.BuiltInCategory.OST_RvtLinks),
            DB.ElementId(DB.BuiltInCategory.OST_DuctSystem),
            DB.ElementId(DB.BuiltInCategory.OST_PipingSystem),
            DB.ElementId(DB.BuiltInCategory.OST_ProjectInformation),
            DB.ElementId(DB.BuiltInCategory.OST_Materials),
            DB.ElementId(DB.BuiltInCategory.OST_HVAC_Zones),
            DB.ElementId(DB.BuiltInCategory.OST_Sheets),
            DB.ElementId(DB.BuiltInCategory.OST_PipeSegments)
        ]
        model_categories = [
            c.Id for c in categories if c.CategoryType == DB.CategoryType.Model and c.Id not in excluded_ids
        ]
        categories_filter = DB.ElementMulticategoryFilter(
            List[DB.ElementId](model_categories)
        )

        if self.IsCheckActiveView:
            elements = (
                DB.FilteredElementCollector(self._doc, self._doc.ActiveView.Id)
                .WherePasses(categories_filter)
                .WhereElementIsNotElementType()
            )
        else:
            elements = (
                DB.FilteredElementCollector(self._doc)
                .WherePasses(categories_filter)
                .WhereElementIsNotElementType()
            )

        for e in elements:
            self._rvt_model_elements[e.Id] = RevitEntity(e)

        self.RaisePropertyChanged("NoRvtElements")

    def load_ifc_data(self):
        self.clear_data()

        if not self._excelsource or self._selected_excel_index < 0 \
            or self._selected_excel_index >= len(self._excelsource):
            return

        excel_dir = self._excelsource[self._selected_excel_index]
        excel_path = os.path.join(self._excel_folder_path, excel_dir)
        ignore_excel_dir = excel_dir.replace(EXCEL_EXTENSION, IGNORE_MISSING_EXCEL_EXTENSION)
        ignore_excel_path = os.path.join(self._excel_folder_path, ignore_excel_dir)
        reader = IfcExcelReader(excel_path, self._ifc_sheet_name)
        data = reader.data

        ignore_id = []
        if os.path.exists(ignore_excel_path):
            ignore_reader = IfcExcelReader(ignore_excel_path, self._ifc_sheet_name)
            ignore_data = ignore_reader.data
            ignore_id = [getattr(d, GLOBAL_ID) for d in ignore_data]
            self._no_ignore_ifc_entity = len(ignore_id)
        try:
            for d in data:
                if ignore_id and getattr(d, GLOBAL_ID) in ignore_id:
                    continue
                entity = IfcEntity(d)
                self._ifc_entity.Add(entity)
                self._all_ifc_entities.append(entity) #new
        except Exception:
            import traceback
            alert("Error: {0}".format(traceback.format_exc()), warn_icon=True)
            return

        # Build dictionaries for fast lookups
        self.build_ifc_indexes()
        self.update_filtered_counts() #new
        # Add "Not Apply" option at the start
        self.IFCClass = [NOT_APPLY] + sorted(self._ifc_by_class.keys())
        self.IFCLevel = [NOT_APPLY] + sorted(self._ifc_by_level.keys())
        self.RvtCategory = [NOT_APPLY] + sorted(self._ifc_by_category.keys())
        self.RvtSystem = [NOT_APPLY] + sorted(self._ifc_by_system.keys())

        # Reset selections
        self.SelectedIFCClassIndex = 0
        self.SelectedIFCLevelIndex = 0
        self.SelectedRvtCategoryIndex = 0
        self.SelectedRvtSystemIndex = 0

        # load rvt data once
        self.load_rvt_data()

        # Build indexes (Revit)
        self.build_revit_indexes()

        self.RaisePropertyChanged("NoIfcElements")
        self.RaisePropertyChanged("IsIFCExcelValid")
        self.update_filtered_counts()

    @staticmethod
    def add_to_index(index_dict, key, express_id_):
        if key not in index_dict:
            index_dict[key] = set()
        index_dict[key].add(express_id_)

    def build_revit_indexes(self):
        self._rvt_by_category.clear()
        self._rvt_by_level.clear()

        # Build indexes (Revit)
        for revit_id, revit_entity in self._rvt_model_elements.items():
            revit_entity = revit_entity # type: RevitEntity
            if revit_entity.revit_category:
                self.add_to_index(self._rvt_by_category, revit_entity.revit_category, revit_id)
            if revit_entity.revit_level:
                self.add_to_index(self._rvt_by_level, revit_entity.revit_level, revit_id)

    def build_ifc_indexes(self):
        # Clear old indexes
        self._ifc_entity_dict.clear()
        self._ifc_by_class.clear()
        self._ifc_by_level.clear()
        self._ifc_by_category.clear()
        self._ifc_by_system.clear()

        # Build indexes (IFC)
        for d in self._ifc_entity:
            express_id = d.express_id
            self._ifc_entity_dict[express_id] = d
            if d.ifc_class:
                self.add_to_index(self._ifc_by_class, d.ifc_class, express_id)
            if d.ifc_level:
                self.add_to_index(self._ifc_by_level, d.ifc_level, express_id)
            if d.rvt_category:
                self.add_to_index(self._ifc_by_category, d.rvt_category, express_id)
            if d.rvt_system:
                self.add_to_index(self._ifc_by_system, d.rvt_system, express_id)

    def update_filtered_counts(self):
        self.clear_filtered_data()

        # Use set intersection for filtering
        all_ids = set(self._ifc_entity_dict.keys())
        filtered_revit_ids = set(self._rvt_model_elements.keys())

        # Filter by GUID if selected
        if self.IsGuidInputSelected and self.GuidsInput.strip():
            guids = {g.strip() for g in self.GuidsInput.split("\n") if g.strip()}
            filtered_ids = all_ids.intersection(guids)
        else:
            filtered_ids = all_ids

        # Apply IFC filters (# `Not Apply` is index 0)
        if self.IFCClass and self.SelectedIFCClassIndex > 0:  # noqa
            selected_class = self.IFCClass[self.SelectedIFCClassIndex]
            filtered_ids = filtered_ids.intersection(
                self._ifc_by_class.get(selected_class, set())
            )

        if self.IFCLevel and self.SelectedIFCLevelIndex > 0:
            selected_level = self.IFCLevel[self.SelectedIFCLevelIndex]
            filtered_ids = filtered_ids.intersection(
                self._ifc_by_level.get(selected_level, set())
            )
            filtered_revit_ids = filtered_revit_ids.intersection(
                self._rvt_by_level.get(selected_level, set())
            )

        if self.RvtCategory and self.SelectedRvtCategoryIndex > 0:  # noqa
            selected_cat = self.RvtCategory[self.SelectedRvtCategoryIndex]
            filtered_ids = filtered_ids.intersection(
                self._ifc_by_category.get(selected_cat, set())
            )
            filtered_revit_ids = filtered_revit_ids.intersection(
                self._rvt_by_category.get(selected_cat, set()))

        if self.RvtSystem and self.SelectedRvtSystemIndex > 0:
            selected_sys = self.RvtSystem[self.SelectedRvtSystemIndex]
            filtered_ids = filtered_ids.intersection(
                self._ifc_by_system.get(selected_sys, set())
            )

        # Now we have a final set of IFC GUIDs after all filters
        for express_id in filtered_ids:
            self._filtered_ifc_entity.Add(self._ifc_entity_dict[express_id])

        # Now we have a final set of Revit elements after all filters
        for revit_id, revit_entity in self._rvt_model_elements.items():
            if revit_id in filtered_revit_ids:
                self._filtered_rvt_model_elements[revit_id] = revit_entity

        # Update counts at once
        self.RaisePropertyChanged("NoFilteredIfcElements")
        self.RaisePropertyChanged("NoFilteredRvtElements")
        self._run_command.RaiseCanExecuteChanged()

    # endregion
