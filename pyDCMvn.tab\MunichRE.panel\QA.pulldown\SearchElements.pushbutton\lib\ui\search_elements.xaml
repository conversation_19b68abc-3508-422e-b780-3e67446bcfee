<mah:MetroWindow
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Search Elements"
    Width="400"
    Height="450"
    FontSize="15"
    ShowMaxRestoreButton="False"
    ShowMinButton="False"
    mc:Ignorable="d">

    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <ComboBox
            Margin="0 0 0 5"
            DisplayMemberPath="Title"
            IsTextSearchEnabled="True"
            ItemsSource="{Binding models}"
            SelectedIndex="{Binding selected_model_index}" />

        <TextBox
            Grid.Row="1"
            x:Name="GuidInputTextBox"
            mah:TextBoxHelper.Watermark="Accept Guid(s)/Id(s)/UniqueId(s)"
            Text="{Binding guids_input, UpdateSourceTrigger=PropertyChanged}"/>

        <DockPanel Grid.Row="2" Margin="0,5,0,0">
            <CheckBox
                Content="Section box selected"
                DockPanel.Dock="Left"
                IsChecked="{Binding is_section_box}" />
            <Button
                Width="100"
                HorizontalAlignment="Right"
                Command="{Binding select_command}"
                Content="Select"
                DockPanel.Dock="Right" />
        </DockPanel>

    </Grid>
</mah:MetroWindow>
