# coding: utf-8
import System
import clr
from DCMvn.core import DB
from MunichRE.ifc_entity import IfcEntity

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)


class MEPCurveGeometry(IfcEntity):
    def __init__(self, expando_object):
        """
        Mep curve geometry base on OrientedBoundingBox
        Args:
            expando_object: ExpandedObject
        """
        IfcEntity.__init__(self, expando_object)
        self.__obbox = self.calculate_oriented_bbox()

    def __get_obbox_edges(self):
        min_pt = self.__obbox.Min
        max_pt = self.__obbox.Max

        # 8 corners of the oriented bounding box
        _pt0 = min_pt  # noqa
        _pt1 = DB.XYZ(max_pt.X, min_pt.Y, min_pt.Z)
        _pt2 = DB.XYZ(max_pt.X, max_pt.Y, min_pt.Z)
        _pt3 = DB.XYZ(min_pt.X, max_pt.Y, min_pt.Z)
        _pt4 = DB.XYZ(min_pt.X, min_pt.Y, max_pt.Z)
        _pt5 = DB.XYZ(max_pt.X, min_pt.Y, max_pt.Z)
        _pt6 = max_pt
        _pt7 = DB.XYZ(min_pt.X, max_pt.Y, max_pt.Z)

        edge1 = DB.Line.CreateBound(_pt0, _pt1).CreateTransformed(self.transform)
        edge2 = DB.Line.CreateBound(_pt0, _pt3).CreateTransformed(self.transform)
        edge3 = DB.Line.CreateBound(_pt0, _pt4).CreateTransformed(self.transform)

        return {
            _pt1: edge1,
            _pt3: edge2,
            _pt4: edge3
        }

    def _get_side_edges(self, length):
        group_lenth = self.__get_obbox_edges().items().OrderByDescending(  # noqa
            lambda x: abs(x[1].Length - length)).ToList()
        cross_points = group_lenth.Take(2).Select(lambda x: x[0]).ToList()
        side_edges = group_lenth.Take(2).Select(lambda x: x[1]).ToList()
        direction_edge = group_lenth.Last()[1]
        return cross_points, direction_edge, side_edges

    def _get_points(self, length, cross_points, direction_edge):
        # type: (float, list, DB.Line) -> list
        edge = DB.Line.CreateBound(cross_points[0], cross_points[1]).CreateTransformed(self.transform)
        start_point = edge.Evaluate(0.5, True)
        end_point = start_point + length * direction_edge.Direction

        return [start_point, end_point].OrderBy(lambda x: x.Z).ToList()  # noqa
