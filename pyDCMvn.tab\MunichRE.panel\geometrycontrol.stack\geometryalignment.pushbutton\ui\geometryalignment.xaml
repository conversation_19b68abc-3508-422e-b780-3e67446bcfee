<Window
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Geometry Alignment Check"
    Width="450"
    Height="450"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">

    <Grid Margin="5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Select source IFC Excel  -->
        <GroupBox
            Grid.Row="0"
            Header="Select IFC Excel">
            <StackPanel Orientation="Vertical">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <TextBox
                        x:Name="ExcelFolderPathTextBox"
                        Margin="0,5,5,5"
                        IsReadOnly="True"
                        Text="{Binding ExcelFolderPath, UpdateSourceTrigger=PropertyChanged}"
                        ToolTip="{Binding ExcelFolderPath, UpdateSourceTrigger=PropertyChanged}" />
                    <Button
                        Grid.Column="1"
                        Width="30"
                        Margin="0,5"
                        Command="{Binding PickExcelFolderCommand}"
                        Content="..."
                        ToolTip="Select IFC Excel Folder" />
                </Grid>
                <ComboBox
                        x:Name="SelectExcelCbBox"
                        IsTextSearchEnabled="True"
                        ItemsSource="{Binding ExcelSource}"
                        SelectedIndex="{Binding SelectedExcelIndex}" />

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock
                        x:Name="NoIfcElementsTextBlock"
                        Margin="0 10 10 10"
                        FontWeight="Bold"
                        Foreground="#F37326"
                        Text="{Binding NoIfcElements, StringFormat='IFC Total: {0}'}" />
                    <TextBlock Grid.Column="1"
                        x:Name="NoRvtElementsTextBlock"
                        Margin="0, 10"
                        FontWeight="Bold"
                        Foreground="Green"
                        Text="{Binding NoRvtElements, StringFormat='RVT Total: {0}'}" />
                    <TextBlock Grid.Column="2"
                        x:Name="NoIgnoreIfcElementsTextBlock"
                        Margin="10"
                        FontWeight="Bold"
                        Foreground="#FDCD19"
                        Text="{Binding NoIgnoreElements, StringFormat='GUID Ignore: {0}'}" />

                </Grid>
            </StackPanel>
        </GroupBox>

        <TabControl
            Grid.Row="1"
            IsEnabled="{Binding IsIFCExcelValid}"
            SelectedIndex="0">

            <!--  IFC Filter Tab  -->
            <TabItem Header="IFC Filter">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="3*" />
                    </Grid.ColumnDefinitions>

                    <!--  IFC Class  -->
                    <Label
                        Margin="0,5,0,0"
                        Content="IFC Class" />
                    <ComboBox
                        x:Name="IfClassCbBox"
                        Grid.Row="0"
                        Grid.Column="1"
                        Margin="0,5,0,0"
                        IsTextSearchEnabled="True"
                        ItemsSource="{Binding IFCClass}"
                        SelectedIndex="{Binding SelectedIFCClassIndex}" />

                    <!--  IFC Level  -->
                    <Label
                        Grid.Row="1"
                        Grid.Column="0"
                        Margin="0,5,0,0"
                        Content="IFC Level" />
                    <ComboBox
                        x:Name="IfcLevelCbBox"
                        Grid.Row="1"
                        Grid.Column="1"
                        Margin="0,5,0,0"
                        IsTextSearchEnabled="True"
                        ItemsSource="{Binding IFCLevel}"
                        SelectedIndex="{Binding SelectedIFCLevelIndex}" />

                    <!--  Rvt Category  -->
                    <Label
                        Grid.Row="2"
                        Grid.Column="0"
                        Margin="0,5"
                        Content="Rvt Category" />
                    <ComboBox
                        x:Name="RvtCategoryCbBox"
                        Grid.Row="2"
                        Grid.Column="1"
                        Margin="0,5"
                        IsTextSearchEnabled="True"
                        ItemsSource="{Binding RvtCategory}"
                        SelectedIndex="{Binding SelectedRvtCategoryIndex}" />

                    <!--  Rvt System  -->
                    <Label
                        Grid.Row="3"
                        Grid.Column="0"
                        Content="Rvt System" />
                    <ComboBox
                        x:Name="RvtSystemCbBox"
                        Grid.Row="3"
                        Grid.Column="1"
                        IsTextSearchEnabled="True"
                        ItemsSource="{Binding RvtSystem}"
                        SelectedIndex="{Binding SelectedRvtSystemIndex}" />
                </Grid>
            </TabItem>

            <!--  IFC Guid Tab  -->
            <TabItem
                Header="IFC Guid"
                IsSelected="{Binding IsGuidInputSelected}">
                <TextBox
                    x:Name="GuidsInputTextBox"
                    Text="{Binding GuidsInput, UpdateSourceTrigger=PropertyChanged}"
                    ToolTip="Paste Guid(s) here" />
            </TabItem>
        </TabControl>

        <!--  Total Filter + Match Guid  -->
        <Grid
            Grid.Row="2"
            Margin="0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock
                x:Name="NoFilteredIfcElementsTextBlock"
                FontWeight="Bold"
                Foreground="#F37326"
                Text="{Binding NoFilteredIfcElements, StringFormat='Filtered IFC: {0}'}" />
            <TextBlock
                x:Name="NoFilteredRvtElementsTextBlock"
                Grid.Column="1"
                Margin="10,0"
                FontWeight="Bold"
                Foreground="Green"
                Text="{Binding NoFilteredRvtElements, StringFormat='Filtered RVT: {0}'}" />
            <CheckBox
                x:Name="CheckActiveViewCheckBox"
                Grid.Column="2"
                HorizontalAlignment="Right"
                VerticalContentAlignment="Center"
                Content="Active view only"
                IsChecked="{Binding IsCheckActiveView}" />
        </Grid>


        <Grid
            Grid.Row="3"
            Margin="0,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <Label Content="Allowable Offset:" />
            <TextBox
                x:Name="AllowableOffsetTextBox"
                Grid.Row="0"
                Grid.Column="1"
                Margin="5,0,0,0"
                Text="{Binding AllowableOffset}"
                ToolTip="Enter allowable offset in mm" />
            <Label
                Grid.Column="2"
                Content="mm" />
            <Button
                x:Name="RunButton"
                Grid.Column="3"
                Width="75"
                Margin="10,0,0,0"
                Command="{Binding RunCommand}"
                Content="Run" />
        </Grid>
    </Grid>
</Window>