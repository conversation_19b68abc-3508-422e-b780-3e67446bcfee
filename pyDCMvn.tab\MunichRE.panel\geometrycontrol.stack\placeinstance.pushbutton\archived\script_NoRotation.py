# -*- coding: utf-8 -*-

# Name of the button in Revit
__title__ = "02_PlacingFamily_ByExcel"
__doc__ = """Version = 1.6
----------------------------------------------------------------
Description:
This is a tool for placing Family Instance based on Excel  file have location
----------------------------------------------------------------
How-to:
Step 1: Click on the button (Shift + Click on the button to Change Parameter to input data from Ifc.Mark column [Default: Mark])
Step 2: Select Excel have location data
Step 3: Ensure all Family Name and Type Name are in RVT.FamilyName and RVT.TypeName have been loaded into the project.

----------------------------------------------------------------
Last update:
- [08.11.2024] - 1.6 Release - Added Select Sheet from List
----------------------------------------------------------------
Author: Long Dang"""

# Imports
import sys
import os
import datetime
import re
from math import pi
import clr

clr.AddReference("System")
from System.Collections.Generic import List
from System import GC

from Autodesk.Revit.DB import *
from Autodesk.Revit.UI import *
from Autodesk.Revit.DB.Structure import StructuralType
from Autodesk.Revit.DB import XYZ
from pyrevit import forms, script

# Use xlrd for reading Excel files (compatible with IronPython 2.7)
import xlrd
import xlsxwriter

# Custom Category Mapping Module
from BuiltinCategory_mapping import category_mapping

# # Absolute path to the folder containing BuiltinCategory_mapping.py
category_mapping_folder = r"F:\Digital Team\LongDang\C_R&D\DCMvnTools\pyDCMvn.extension\pyDCMvn.tab\lkdang.panel\lkdang.lib\Revit"


if category_mapping_folder not in sys.path:
    sys.path.append(category_mapping_folder)


# RPW Imports
from rpw.ui.forms import FlexForm, Label, ComboBox, TextBox, Separator, Button, CheckBox, SelectFromList


# Variables
logger = script.get_logger()
doc = __revit__.ActiveUIDocument.Document  # type: Document
uidoc = __revit__.ActiveUIDocument  # type: UIDocument
app = __revit__.Application  # Application class
selection = uidoc.Selection  # type: Selection
active_view = doc.ActiveView  # Get Currently open View
output = script.get_output()
active_level = active_view.GenLevel  # Only View plan views have associated Level.
rvt_year = int(app.VersionNumber)  # Get Version number of Revit file using script
PATH_SCRIPT = os.path.dirname(__file__)  # Absolute path to the folder where script is located

# Constants for easy configuration
LOCATION_HEADER = "ElementCentroid (ft)"
CODE_NUMBER_HEADER = "GlobalId"
FAMILY_NAME_HEADER = "RVT.FamilyName"  # Column name for family name
TYPE_NAME_HEADER = "RVT.TypeName"      # Column name for type name
CATEGORY_NAME_HEADER = "RVT.Category"  # Column name for category name
MISSING_FAMILY_REPORT = set()         # Using a set to capture unique missing family/type details


# Functions
def read_excel_file(file_path, sheet_name, header_row=0):
    """Read data from an Excel file using xlrd."""
    try:
        workbook = xlrd.open_workbook(file_path)
        worksheet = workbook.sheet_by_name(sheet_name)
        parameter_names = worksheet.row_values(header_row)
        data = []

        for row_idx in range(header_row + 1, worksheet.nrows):
            row_values = worksheet.row_values(row_idx)
            row_data = dict((parameter_names[i], row_values[i]) for i in range(len(parameter_names)))
            data.append(row_data)

        return data, parameter_names
    except Exception as e:
        forms.alert("Error reading Excel file: {}".format(e), exitscript=True)
        return [], []


def write_failed_instances_report(file_path, headers, failed_instances):
    """Write failed rotations to an Excel file using xlsxwriter."""
    try:
        # Create a workbook and add a worksheet
        workbook = xlsxwriter.Workbook(file_path)
        worksheet = workbook.add_worksheet("Failed Rotations")

        # Write headers to the first row
        for col, header in enumerate(headers + ["Reason"]):  # Adding "Reason" column
            worksheet.write(0, col, header)

        # Write each failed instance data row
        for row_idx, instance_data in enumerate(failed_instances, start=1):
            for col_idx, header in enumerate(headers + ["Reason"]):
                worksheet.write(row_idx, col_idx, instance_data.get(header, ""))

        # Close the workbook
        workbook.close()
        logger.info("Failed rotations report saved to: {}".format(file_path))
    except Exception as e:
        logger.error("Error writing failed instances report: {}".format(e))


def parse_xyz(xyz_string):
    """Parse an XYZ string in the format '(x, y, z)' without assuming any prefix."""
    try:
        match = re.match(r"\(?\s*([-+]?[0-9]*\.?[0-9]+),\s*([-+]?[0-9]*\.?[0-9]+),\s*([-+]?[0-9]*\.?[0-9]+)\s*\)?", xyz_string)
        if not match:
            logger.error("Invalid XYZ format: {}".format(xyz_string))
            return None

        x, y, z = map(float, match.groups())
        return XYZ(x, y, z)
    except Exception as e:
        logger.error("Error parsing XYZ: {}".format(e))
        return None


def activate_family_symbol(doc, category_name, family_name, type_name):
    """
    Activate a FamilySymbol in the specified category by matching Family Name and Type Name.

    Parameters:
    - doc: The current Revit document.
    - category_name: The name of the category (mapped to a BuiltInCategory).
    - family_name: The name of the family to search for.
    - type_name: The name of the type (symbol) to search for.
    - category_mapping: A dictionary mapping category names to BuiltInCategory values.

    Returns:
    - A tuple (FamilySymbol, None) if successful, or (None, error_message) if not.
    """
    try:
        # Get the BuiltInCategory from the category name
        built_in_category = category_mapping.get(category_name)
        if not built_in_category:
            return None, "Category '{}' not recognized".format(category_name)

        # Collect FamilySymbols in the specified category
        symbols = FilteredElementCollector(doc).OfClass(FamilySymbol).OfCategory(built_in_category)

        # Initialize a flag to track if the family name is found
        family_found = False

        # Iterate through the symbols to find a match
        for symbol in symbols:
            if symbol.Family.Name == family_name:
                family_found = True  # Mark that the family name exists in the category
                if Element.Name.__get__(symbol) == type_name:
                    # Activate the symbol if it is not already active
                    if not symbol.IsActive:
                        symbol.Activate()
                        doc.Regenerate()
                    return symbol, None

        # Return specific messages based on what was found
        if family_found:
            return None, "Family '{}' found in Category '{}', but no Type Name '{}' exists.".format(family_name, category_name, type_name)
        else:
            return None, "Family '{}' not found in Category '{}'.".format(family_name, category_name)
    except Exception as e:
        # Handle any exceptions and return an error message
        return None, str(e)


def get_level_dict():
    """
    Retrieve a dictionary mapping level names to Level IDs in the Revit document.
    """
    levels = FilteredElementCollector(doc).OfClass(Level).ToElements()
    level_dict = {level.Name: level.Id for level in levels}  # Map level name to ID
    return level_dict

def place_family_instance_with_rotation(doc, family_symbol, location, level_name, z_coordinate):
    """
    Place a family instance with rotation at a specific level, converting Z-coordinate to level-relative offset.

    Args:
        doc (Document): The active Revit document.
        family_symbol (FamilySymbol): The family symbol to place.
        location (XYZ): The base location (X and Y) of the instance.
        transform (Transform): The rotation and scaling transform.
        level_name (str): The name of the level where the instance should be placed.
        z_coordinate (float): Z-coordinate from the Excel file (project elevation).

    Returns:
        FamilyInstance: The placed family instance, or None if placement fails.
        str: An error message if placement fails, or None if successful.
    """
    try:
        # Get the level dictionary
        levels = FilteredElementCollector(doc).OfClass(Level).ToElements()
        level_dict = {level.Name: level for level in levels}  # Map level name to Level object

        # Find the Level object based on the level name
        level = level_dict.get(level_name)
        if not level:
            return None, "Level '{}' not found in the project.".format(level_name)

        # Calculate the elevation offset relative to the level
        level_elevation = level.Elevation  # Level elevation in project units
        relative_offset = z_coordinate - level_elevation  # Convert Z to level reference offset

        # Set the location Z to the relative offset
        adjusted_location = XYZ(location.X, location.Y, relative_offset)

        # Ensure the family symbol is activated
        if not family_symbol.IsActive:
            family_symbol.Activate()
            doc.Regenerate()

        # Place the family instance at the level with offset
        instance = doc.Create.NewFamilyInstance(adjusted_location, family_symbol, level, StructuralType.NonStructural)

        return instance, None

    except Exception as e:
        logger.error("Error placing family instance: {}".format(e))
        return None, str(e)


# Main code
if __shiftclick__:  # Shift + Click # noqa
    PARAMETER_NAME_FOR_CODE = forms.ask_for_string(
        default='IfcGUID',
        prompt='Enter Parameter name:',
        title='Get Code Number From Parameter Name'
    )
else:
    PARAMETER_NAME_FOR_CODE = "IfcGUID"


excel_file_path = forms.pick_excel_file()
if not excel_file_path:
    forms.alert("Excel | Can't find Excel path for Get Data. Please Try Again", exitscript=True)

# Get all sheet names from the selected Excel file
workbook = xlrd.open_workbook(excel_file_path)
sheet_names = workbook.sheet_names()

sheet_name = SelectFromList(
    title="Select Sheet",
    options=sheet_names,
    description="Choose the sheet with element location data.",
    sort=True,
    exit_on_close=True
)

if not sheet_name:
    forms.alert("Excel | No sheet selected. Please try again.", exitscript=True)

datas, headers = read_excel_file(excel_file_path, sheet_name)
failed_instances = []
placed_elements_count = 0
start = datetime.datetime.now()

# Start transaction
transaction_group = TransactionGroup(doc, "Place Families")
transaction_group.Start()

try:
    transaction = Transaction(doc, "Place Families")
    transaction.Start()
    for item in datas:
        try:
            # Parse location and direction vectors
            location_raw = item.get(LOCATION_HEADER)

            # Extract family name and category name
            family_name = item.get(FAMILY_NAME_HEADER)
            type_name = item.get(TYPE_NAME_HEADER)
            category_name = item.get(CATEGORY_NAME_HEADER)
            storey_name = item.get("StoreyName")  # New column for level name
            z_coordinate = parse_xyz(location_raw).Z if location_raw else None  # Extract Z-coordinate

            # Validate required fields
            if not family_name or not category_name:
                logger.warning("Missing required fields: Family Name or Category Name. Skipping row.")
                item["Reason"] = "Missing Family Name or Category Name"
                failed_instances.append(item)
                continue

            # Validate required fields
            if not family_name or not category_name or not type_name:
                logger.warning("Missing required fields: Family Name or Category Name or Type Name. Skipping row.")
                item["Reason"] = "Missing Family Name or Category Name or Type Name"
                failed_instances.append(item)
                continue

            # Log raw input for debugging
            logger.info("Raw Data - Location: {0},Category: {1}, Family: {2},Type: {3} , Storey: {4}, Z: {5}".format(
                location_raw, category_name, family_name,type_name, storey_name, z_coordinate))

            # Convert parsed data to XYZ objects
            location = parse_xyz(location_raw)

            # Skip invalid rows
            if not location:
                item["Reason"] = "Invalid or missing XYZ directions"
                failed_instances.append(item)
                continue

            # Place the family instance
            family_symbol, error = activate_family_symbol(doc,category_name, family_name, type_name)
            if not family_symbol:
                item["Reason"] = error
                failed_instances.append(item)
                continue

            instance, error = place_family_instance_with_rotation(doc, family_symbol, location, storey_name,z_coordinate)
            if not instance:
                item["Reason"] = error
                failed_instances.append(item)
                continue

            if instance:
                # Set parameter value
                code_param = instance.LookupParameter(PARAMETER_NAME_FOR_CODE)
                if code_param:
                    code_param.Set(item.get(CODE_NUMBER_HEADER, ""))
            placed_elements_count += 1

        except Exception as e:
            item["Reason"] = str(e)
            failed_instances.append(item)

    transaction.Commit()
except Exception as e:
    logger.error("Error in transaction: {0}".format(e))
    transaction.RollBack()

transaction_group.Assimilate()


if failed_instances:
    report_dir = os.path.dirname(excel_file_path)
    report_name = os.path.splitext(os.path.basename(excel_file_path))[0] + "_NoRotationReport.xlsx"
    report_file_path = os.path.join(report_dir, report_name)
    write_failed_instances_report(report_file_path, headers, failed_instances)
    print("Report for failed rotation elements saved to: {report_file_path}".format(report_file_path=report_file_path))
else:
    print("No failed instances. No report generated.")

print("Total elements placed: {} out of {}".format(placed_elements_count, len(datas)))
print("DONE: {}".format(__title__))
end = datetime.datetime.now()
print("Time elapsed: {}".format(end - start))
