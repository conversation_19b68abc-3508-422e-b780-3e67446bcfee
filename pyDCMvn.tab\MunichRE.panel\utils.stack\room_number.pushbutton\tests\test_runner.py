# coding: utf-8
"""
Main Test Runner for Room Number Mapping Tests
Organizes and executes all test suites with clear navigation and options.
"""
import clr
from DCMvn.core.framework import Debug
from DCMvn.forms import alert

clr.AddReference("System.Core")


class TestRunner(object):
    """Main test runner with organized test execution and interactive selection."""
    
    def __init__(self):
        self.test_groups = {
            'spatial': {
                'name': 'Spatial Geometry Tests (1-4)',
                'description': 'Test spatial element geometry, bounding boxes, and center points',
                'module': 'test_spatial_geometry',
                'tests': [
                    ('Test 1', 'Trace Linked Spatial Geometry', 'run_test_01'),
                    ('Test 2', 'Trace Linked Spatial Bounding Box', 'run_test_02'),
                    ('Test 3', 'Trace Expanded Bounding Box (300mm)', 'run_test_03'),
                    ('Test 4', 'Trace Linked Spatial Center Point', 'run_test_04')
                ]
            },
            'intersection': {
                'name': 'MEP Intersection Tests (5-9)',
                'description': 'Test MEP-spatial intersections with and without proximity',
                'module': 'test_mep_intersection',
                'tests': [
                    ('Test 5', 'MEP Intersection with Linked Spatial', 'run_test_05'),
                    ('Test 6', 'MEP Intersection with Proximity (300mm)', 'run_test_06'),
                    ('Test 7', 'Show All MEP Intersections by Category', 'run_test_07'),
                    ('Test 8', 'Show MEP Proximity by Category (300mm)', 'run_test_08'),
                    ('Test 9', 'Interactive MEP-Spatial Intersection', 'run_test_09')
                ]
            }
        }
    
    def show_main_menu(self):
        """Display main test menu and handle selection."""
        Debug.WriteLine("=" * 60)
        Debug.WriteLine("ROOM NUMBER MAPPING TEST SUITE")
        Debug.WriteLine("=" * 60)
        
        menu_text = "Room Number Mapping Test Suite\n\n"
        menu_text += "Available Test Groups:\n\n"
        
        menu_text += "1. Spatial Geometry Tests (1-4)\n"
        menu_text += "   - Test spatial element geometry and bounding boxes\n"
        menu_text += "   - Visualize center points and expanded boundaries\n\n"
        
        menu_text += "2. MEP Intersection Tests (5-9)\n"
        menu_text += "   - Test MEP-spatial intersections\n"
        menu_text += "   - Proximity detection and bulk analysis\n\n"
        
        menu_text += "3. Run All Tests (1-9)\n"
        menu_text += "   - Execute complete test suite\n\n"
        
        menu_text += "4. Individual Test Selection\n"
        menu_text += "   - Choose specific tests to run\n\n"
        
        menu_text += "Choose an option (1-4):"
        
        # Since we can't get direct input, we'll provide separate functions for each option
        alert(menu_text + "\n\nUse the individual run functions:\n- run_spatial_tests()\n- run_intersection_tests()\n- run_all_tests()\n- show_individual_tests()")
    
    def run_spatial_tests(self):
        """Run all spatial geometry tests."""
        try:
            from tests.test_spatial_geometry import run_spatial_geometry_tests
            run_spatial_geometry_tests()
        except ImportError as e:
            alert("Error importing spatial tests: {0}".format(str(e)))
        except Exception as e:
            alert("Error running spatial tests: {0}".format(str(e)))
    
    def run_intersection_tests(self):
        """Run all MEP intersection tests."""
        try:
            from tests.test_mep_intersection import run_mep_intersection_tests
            run_mep_intersection_tests()
        except ImportError as e:
            alert("Error importing intersection tests: {0}".format(str(e)))
        except Exception as e:
            alert("Error running intersection tests: {0}".format(str(e)))
    
    def run_all_tests(self):
        """Run complete test suite."""
        Debug.WriteLine("\n" + "=" * 60)
        Debug.WriteLine("RUNNING COMPLETE TEST SUITE (1-9)")
        Debug.WriteLine("=" * 60)
        
        alert("Starting Complete Test Suite\n\nAll tests (1-9) will run sequentially.\nYou will be prompted for element selection.")
        
        try:
            # Run spatial tests first
            Debug.WriteLine("\nStarting Spatial Geometry Tests...")
            self.run_spatial_tests()
            
            # Run intersection tests
            Debug.WriteLine("\nStarting MEP Intersection Tests...")
            self.run_intersection_tests()
            
            Debug.WriteLine("\n" + "=" * 60)
            Debug.WriteLine("COMPLETE TEST SUITE FINISHED")
            Debug.WriteLine("=" * 60)
            
            alert("Complete Test Suite Finished!\n\nAll tests (1-9) have been executed.\nCheck Output window for detailed results.")
            
        except Exception as e:
            Debug.WriteLine("Error in complete test suite: {0}".format(str(e)))
            alert("Error running complete test suite: {0}".format(str(e)))
    
    def show_individual_tests(self):
        """Show individual test options."""
        individual_text = "Individual Test Functions:\n\n"
        
        individual_text += "SPATIAL GEOMETRY TESTS:\n"
        individual_text += "- run_test_01()  # Trace Geometry\n"
        individual_text += "- run_test_02()  # Trace Bounding Box\n"
        individual_text += "- run_test_03()  # Trace Expanded BBox (300mm)\n"
        individual_text += "- run_test_04()  # Trace Center Point\n\n"
        
        individual_text += "MEP INTERSECTION TESTS:\n"
        individual_text += "- run_test_05()  # MEP-Spatial Intersection\n"
        individual_text += "- run_test_06()  # MEP-Spatial with Proximity\n"
        individual_text += "- run_test_07()  # Show All MEP Intersections\n"
        individual_text += "- run_test_08()  # Show MEP Proximity\n"
        individual_text += "- run_test_09()  # Interactive Intersection\n\n"
        
        individual_text += "Import the test modules and call these functions directly:\n"
        individual_text += "from tests.test_spatial_geometry import run_test_01\n"
        individual_text += "from tests.test_mep_intersection import run_test_05"
        
        alert(individual_text)
    
    def show_interactive_test_selection(self):
        """Show interactive test selection dialog using SelectFromList."""
        try:
            from DCMvn.forms.wpfforms import SelectFromList
            
            # Define available tests
            test_options = [
                ("Complete Test Suite (1-9)", "run_all_tests", "Run all spatial and MEP intersection tests"),
                ("Spatial Geometry Tests (1-4)", "run_spatial_tests", "Test spatial element geometry and bounding boxes"),
                ("MEP Intersection Tests (5-9)", "run_intersection_tests", "Test MEP-spatial intersections and proximity"),
                ("Test 1: Trace Spatial Geometry", "run_test_01", "Trace linked spatial element geometry"),
                ("Test 2: Trace Bounding Box", "run_test_02", "Trace linked spatial bounding box"),
                ("Test 3: Trace Expanded BBox (300mm)", "run_test_03", "Trace expanded bounding box with 300mm offset"),
                ("Test 4: Trace Center Point", "run_test_04", "Trace linked spatial center point"),
                ("Test 5: MEP-Spatial Intersection", "run_test_05", "Test direct MEP-spatial intersection"),
                ("Test 6: MEP-Spatial with Proximity", "run_test_06", "Test MEP-spatial intersection with 300mm proximity"),
                ("Test 7: Show All MEP Intersections", "run_test_07", "Find all intersecting MEPs by category"),
                ("Test 8: Show MEP Proximity", "run_test_08", "Find all MEPs within 300mm by category"),
                ("Test 9: Interactive Intersection", "run_test_09", "Interactive MEP-spatial intersection test"),
                ("Test 10: Performance Benchmark", "run_test_10", "Performance timing for detection strategies"),
                ("Test 11: Edge Case Testing", "run_test_11", "Test edge cases and unusual elements"),
                ("Test 12: Validation & Reporting", "run_test_12", "Result validation and comprehensive reporting")
            ]
            
            # Create selection items - use simple objects instead of dict
            class TestOption(object):
                def __init__(self, name, function_name, description):
                    self.Name = name
                    self.Description = description
                    self.FunctionName = function_name
            
            selection_items = []
            for name, func_name, description in test_options:
                selection_items.append(TestOption(name, func_name, description))
            
            # Show selection dialog
            selected = SelectFromList.show(
                selection_items,
                title="Room Number Mapping Test Suite",
                width=400,
                height=500,
                name_attr='Name',
                description_attr='Description'
            )
            
            if selected:
                function_name = selected.FunctionName
                test_name = selected.Name
                
                Debug.WriteLine("=" * 60)
                Debug.WriteLine("EXECUTING TEST: {0}".format(test_name))
                Debug.WriteLine("=" * 60)
                
                # Execute the selected test
                self._execute_test_by_name(function_name, test_name)
                
                Debug.WriteLine("Test execution completed: {0}".format(test_name))
                
            else:
                Debug.WriteLine("Test selection cancelled by user")
                
        except ImportError:
            alert("SelectFromList not available. Please check DCMvn.forms.wpfforms module.", "Import Error")
        except Exception as e:
            alert("Error showing test selection: {0}".format(str(e)), "Test Selection Error")
    
    def _execute_test_by_name(self, function_name, test_name):
        """Execute a test by its function name."""
        try:
            if function_name in ['run_all_tests', 'run_spatial_tests', 'run_intersection_tests']:
                # Execute test suite functions
                if function_name == 'run_all_tests':
                    self.run_all_tests()
                elif function_name == 'run_spatial_tests':
                    self.run_spatial_tests()
                elif function_name == 'run_intersection_tests':
                    self.run_intersection_tests()
                    
            elif function_name.startswith('run_test_'):
                # Import and execute specific test functions
                test_num = function_name.split('_')[-1]
                
                if test_num in ['01', '02', '03', '04']:
                    from tests.test_spatial_geometry import run_test_01, run_test_02, run_test_03, run_test_04
                    test_functions = {
                        '01': run_test_01, '02': run_test_02, 
                        '03': run_test_03, '04': run_test_04
                    }
                    test_functions[test_num]()
                    
                elif test_num in ['05', '06', '07', '08', '09']:
                    from tests.test_mep_intersection import run_test_05, run_test_06, run_test_07, run_test_08, run_test_09
                    test_functions = {
                        '05': run_test_05, '06': run_test_06, '07': run_test_07,
                        '08': run_test_08, '09': run_test_09
                    }
                    test_functions[test_num]()
                    
                elif test_num in ['10', '11', '12']:
                    # These are in the same module (test_runner)
                    test_functions = {
                        '10': test_10_performance_benchmark,
                        '11': test_11_edge_cases,
                        '12': test_12_validation_and_reporting
                    }
                    test_functions[test_num]()
                    
        except ImportError as e:
            alert("Error importing test module: {0}".format(str(e)), "Test Import Error")
        except Exception as e:
            alert("Error executing test: {0}".format(str(e)), "Test Execution Error")
            Debug.WriteLine("Error executing test {0}: {1}".format(test_name, str(e)))


# Global test runner instance
test_runner = TestRunner()


# Convenient global functions for easy access
def show_test_menu():
    """Show the main test menu."""
    test_runner.show_main_menu()

def run_spatial_tests():
    """Run all spatial geometry tests (1-4)."""
    test_runner.run_spatial_tests()

def run_intersection_tests():
    """Run all MEP intersection tests (5-9)."""
    test_runner.run_intersection_tests()

def run_all_tests():
    """Run complete test suite (1-9)."""
    test_runner.run_all_tests()

def show_individual_tests():
    """Show individual test function names."""
    test_runner.show_individual_tests()

def show_test_selection():
    """Show interactive test selection dialog."""
    test_runner.show_interactive_test_selection()


# Additional suggested tests (Test 10+)
def test_10_performance_benchmark():
    """
    Test 10: Performance benchmark for different detection strategies.
    Measures execution time for various intersection methods.
    """
    try:
        import time
        from tests.test_utils import SpatialElementSelector, MepElementSelector, IntersectionTester
        from DCMvn.core.framework import Debug
        
        Debug.WriteLine("=" * 50)
        Debug.WriteLine("Test 10 - Performance Benchmark")
        Debug.WriteLine("=" * 50)
        
        alert("Test 10: Performance benchmark\nSelect a MEP element and linked spatial for timing tests")
        
        # Select elements
        mep_element = MepElementSelector.pick_mep_element()
        if not mep_element:
            return
            
        spatial_element, link_instance, doc_wrapper = SpatialElementSelector.pick_linked_spatial()
        if not spatial_element:
            return
        
        from tests.test_utils import SpatialWrapperFactory
        spatial_wrapper = SpatialWrapperFactory.create_spatial_wrapper(spatial_element, doc_wrapper)
        if not spatial_wrapper:
            return
        
        tester = IntersectionTester()
        iterations = 10
        
        Debug.WriteLine("Running {0} iterations for each test...".format(iterations))
        
        # Test 1: Direct intersection timing
        start_time = time.time()
        for i in range(iterations):
            is_intersecting, distance, details = tester.test_mep_spatial_intersection(
                mep_element, spatial_wrapper, use_proximity=False
            )
        direct_time = (time.time() - start_time) / iterations
        
        # Test 2: Proximity intersection timing  
        start_time = time.time()
        for i in range(iterations):
            is_intersecting, distance, details = tester.test_mep_spatial_intersection(
                mep_element, spatial_wrapper, use_proximity=True
            )
        proximity_time = (time.time() - start_time) / iterations
        
        Debug.WriteLine("Performance Results:")
        Debug.WriteLine("  Direct intersection: {0:.4f}s per test".format(direct_time))
        Debug.WriteLine("  Proximity intersection: {0:.4f}s per test".format(proximity_time))
        Debug.WriteLine("  Overhead for proximity: {0:.1f}%".format((proximity_time - direct_time) / direct_time * 100))
        
        alert("Performance Test Completed!\n\nDirect: {0:.4f}s\nProximity: {1:.4f}s\n\nCheck Output for details.".format(direct_time, proximity_time))
        
    except Exception as e:
        Debug.WriteLine("Error in performance test: {0}".format(str(e)))
        alert("Performance test failed: {0}".format(str(e)))


def test_11_edge_cases():
    """
    Test 11: Edge case testing for various element types and conditions.
    Tests robustness with unusual elements and configurations.
    """
    try:
        from DCMvn.core.framework import Debug
        
        Debug.WriteLine("=" * 50)
        Debug.WriteLine("Test 11 - Edge Case Testing")
        Debug.WriteLine("=" * 50)
        
        alert("Test 11: Edge case testing\nThis will test various element types and edge conditions")
        
        # Test with different element types
        edge_cases = [
            "Elements with no geometry",
            "Very small elements",
            "Elements at document boundaries", 
            "Rotated/transformed elements",
            "Elements with invalid parameters"
        ]
        
        Debug.WriteLine("Edge cases to be tested:")
        for i, case in enumerate(edge_cases, 1):
            Debug.WriteLine("  {0}. {1}".format(i, case))
        
        # Implementation would depend on specific edge cases found in practice
        Debug.WriteLine("Edge case testing framework ready - implement specific cases as needed")
        
        alert("Edge case testing framework ready.\nImplement specific test cases based on production issues.")
        
    except Exception as e:
        Debug.WriteLine("Error in edge case test: {0}".format(str(e)))
        alert("Edge case test failed: {0}".format(str(e)))


def test_12_validation_and_reporting():
    """
    Test 12: Validation and reporting test.
    Tests the accuracy of results and generates comprehensive reports.
    """
    try:
        from DCMvn.core.framework import Debug
        
        Debug.WriteLine("=" * 50)
        Debug.WriteLine("Test 12 - Validation and Reporting")
        Debug.WriteLine("=" * 50)
        
        alert("Test 12: Validation and reporting\nTests result accuracy and generates reports")
        
        # This would implement comprehensive validation
        validation_checks = [
            "Coordinate system consistency",
            "Distance calculation accuracy",
            "Geometry transformation validation",
            "Result reproducibility",
            "Cross-reference with manual verification"
        ]
        
        Debug.WriteLine("Validation checks:")
        for i, check in enumerate(validation_checks, 1):
            Debug.WriteLine("  {0}. {1}".format(i, check))
        
        Debug.WriteLine("Validation framework ready - implement specific validation logic")
        
        alert("Validation and reporting framework ready.\nImplement specific validation checks as needed.")
        
    except Exception as e:
        Debug.WriteLine("Error in validation test: {0}".format(str(e)))
        alert("Validation test failed: {0}".format(str(e)))


# Additional test functions for suggested tests
def run_test_10():
    """Run performance benchmark test."""
    test_10_performance_benchmark()

def run_test_11():
    """Run edge case testing."""
    test_11_edge_cases()

def run_test_12():
    """Run validation and reporting test."""
    test_12_validation_and_reporting()