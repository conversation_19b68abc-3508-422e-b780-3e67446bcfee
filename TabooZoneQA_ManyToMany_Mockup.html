<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabooZone QA Report - Many-to-Many Mockup</title>
    <style>
        body {
            font-family: Segoe UI, Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            margin: 4px 0 8px 0;
            color: #2c3e50;
        }
        h3 {
            margin: 10px 0 6px 0;
            color: #34495e;
        }
        .header-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 20px;
        }
        .summary-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 8px 0 16px 0;
        }
        .card {
            padding: 8px 12px;
            border-radius: 6px;
            min-width: 140px;
            border: 1px solid rgba(0,0,0,0.1);
        }
        .card-success { background: #d5f5e3; color: #111; }
        .card-error { background: #fadbd8; color: #111; }
        .card-warning { background: #fcf3cf; color: #111; }
        .card-label {
            font-size: 12px;
            opacity: 0.8;
        }
        .card-value {
            font-size: 18px;
            font-weight: 600;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        th {
            text-align: left;
            padding: 8px;
            background: #f1f2f6;
            border: 1px solid #ddd;
            font-weight: 600;
        }
        td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        .row-matched-pass { background: #eafaf1; }
        .row-matched-fail { background: #fdecea; }
        .row-missing { background: #fdecea; }
        .row-extra { background: #fdecea; }
        .row-duplicate { background: #fff4d6; }
        .info-block {
            margin: 4px 0 10px 0;
            padding: 8px;
            border-radius: 6px;
            background: #f9f9f9;
            border: 1px solid rgba(0,0,0,0.08);
            font-size: 12px;
            color: #444;
        }
        .status-banner {
            margin: 12px 0;
            padding: 10px;
            border-radius: 6px;
            background: #f4f6f6;
            font-weight: bold;
        }
        .linkified {
            color: #0066cc;
            text-decoration: underline;
            cursor: pointer;
        }
        .composite-guid {
            font-family: monospace;
            font-size: 11px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="font-family:Segoe UI, Arial, sans-serif;margin:8px 0;">
            <h2>TabooZone QA Report - Complex Many-to-Many Validation</h2>
            <div class="header-info">
                ARC: Architectural Elements | Mass: Mass Elements | Tol: 50.0 mm
            </div>
        </div>

        <h3>Missing in Mass (ARC present, Mass missing)</h3>
        <table>
            <tr>
                <th>Composite GUID</th>
                <th>ARC Elements</th>
                <th>ARC Info</th>
            </tr>
            <tr class="row-missing">
                <td class="composite-guid">a1b2c3d4-e5f6-7890-abcd-ef1234567890;b2c3d4e5-f6g7-8901-bcde-f23456789012</td>
                <td>DB.Element(156789), DB.Element(234567)</td>
                <td>ID:156789 | Walls | Basic Wall: Generic - 200mm, ID:234567 | Structural Columns | Concrete-Rectangular-Column: 400 x 600mm</td>
            </tr>
            <tr class="row-missing">
                <td class="composite-guid">c3d4e5f6-g7h8-9012-cdef-************;d4e5f6g7-h8i9-0123-defg-************;e5f6g7h8-i9j0-1234-efgh-************</td>
                <td>DB.Element(345678), DB.Element(456789), DB.Element(567890)</td>
                <td>ID:345678 | Floors | Floor: Generic - 150mm, ID:456789 | Walls | Curtain Wall: Storefront, ID:567890 | Structural Columns | Steel-W-Column: W14X90</td>
            </tr>
        </table>

        <h3>Extra in Mass (Mass present, ARC missing)</h3>
        <table>
            <tr>
                <th>Composite GUID</th>
                <th>Mass Element</th>
                <th>Mass Info</th>
            </tr>
            <tr class="row-extra">
                <td class="composite-guid">f6g7h8i9-j0k1-2345-fghi-************;g7h8i9j0-k1l2-3456-ghij-************</td>
                <td><span class="linkified">DB.Element(789012)</span></td>
                <td>ID:789012 | Mass | Mass: Conceptual Mass</td>
            </tr>
        </table>

        <h3>Duplicate GUIDs in ARC</h3>
        <table>
            <tr>
                <th>Composite GUID</th>
                <th>ARC Element</th>
                <th>ARC Info</th>
            </tr>
            <tr class="row-duplicate">
                <td class="composite-guid">h8i9j0k1-l2m3-4567-hijk-************;i9j0k1l2-m3n4-5678-ijkl-************</td>
                <td>DB.Element(890123)</td>
                <td>ID:890123 | Walls | Basic Wall: Exterior - Brick on CMU</td>
            </tr>
            <tr class="row-duplicate">
                <td class="composite-guid">h8i9j0k1-l2m3-4567-hijk-************;i9j0k1l2-m3n4-5678-ijkl-************</td>
                <td>DB.Element(901234)</td>
                <td>ID:901234 | Walls | Basic Wall: Exterior - Brick on CMU</td>
            </tr>
        </table>

        <h3>Duplicate GUIDs in Mass</h3>
        <i>No duplicate Mass GUIDs.</i>

        <h3>Matched GUID Pairs</h3>
        <table>
            <tr>
                <th>Composite GUID</th>
                <th>ARC Info</th>
                <th>Mass Element</th>
                <th>Mass Info</th>
                <th>Distance (mm)</th>
                <th>Result</th>
            </tr>
            <!-- Many-to-Many Example 1: Mass relates to 2 ARC elements -->
            <tr class="row-matched-pass">
                <td class="composite-guid">12345678-90ab-cdef-1234-567890abcdef;23456789-01bc-def1-2345-6789012bcdef</td>
                <td>ID:123001 | Walls | Basic Wall: Generic - 200mm, ID:123002 | Structural Columns | Concrete-Rectangular-Column: 300 x 300mm</td>
                <td><span class="linkified">DB.Element(456001)</span></td>
                <td>ID:456001 | Mass | Mass: Conceptual Mass | Centroid: (12.5, 8.3, 3.0) | Thickness: 200mm</td>
                <td>15.2</td>
                <td>Passed</td>
            </tr>
            <!-- Many-to-Many Example 2: Mass relates to 3 ARC elements -->
            <tr class="row-matched-pass">
                <td class="composite-guid">34567890-12cd-ef12-3456-789012cdef12;45678901-23de-f123-4567-890123def123;56789012-34ef-1234-5678-901234ef1234</td>
                <td>ID:123003 | Floors | Floor: Generic - 150mm, ID:123004 | Walls | Curtain Wall: Storefront, ID:123005 | Structural Columns | Steel-W-Column: W12X65</td>
                <td><span class="linkified">DB.Element(456002)</span></td>
                <td>ID:456002 | Mass | Mass: Conceptual Mass | Centroid: (25.1, 15.7, 6.0) | Thickness: 150mm</td>
                <td>8.7</td>
                <td>Passed</td>
            </tr>
            <!-- Shared ARC elements example: Mass 3 shares some ARC elements with Mass 1 -->
            <tr class="row-matched-fail">
                <td class="composite-guid">12345678-90ab-cdef-1234-567890abcdef;67890123-45ef-2345-6789-012345ef2345</td>
                <td>ID:123001 | Walls | Basic Wall: Generic - 200mm, ID:123006 | Structural Columns | Steel-HSS-Column: HSS8X8X1/2</td>
                <td><span class="linkified">DB.Element(456003)</span></td>
                <td>ID:456003 | Mass | Mass: Conceptual Mass | Centroid: (18.9, 12.4, 4.5) | Thickness: 250mm</td>
                <td>75.3</td>
                <td>Failed</td>
            </tr>
            <!-- More complex relationships -->
            <tr class="row-matched-pass">
                <td class="composite-guid">78901234-56ef-3456-7890-123456ef3456;89012345-67f0-4567-8901-234567f04567</td>
                <td>ID:123007 | Walls | Basic Wall: Interior - 135mm Partition, ID:123008 | Floors | Floor: Composite Deck - Metal Deck</td>
                <td><span class="linkified">DB.Element(456004)</span></td>
                <td>ID:456004 | Mass | Mass: Conceptual Mass | Centroid: (35.2, 22.8, 9.0) | Thickness: 135mm</td>
                <td>22.1</td>
                <td>Passed</td>
            </tr>
            <tr class="row-matched-pass">
                <td class="composite-guid">90123456-78f1-5678-9012-345678f15678;01234567-89f2-6789-0123-456789f26789;12345678-90f3-7890-1234-567890f37890</td>
                <td>ID:123009 | Structural Columns | Concrete-Round-Column: 400mm dia, ID:123010 | Walls | Basic Wall: Exterior - EIFS on Metal Stud, ID:123011 | Floors | Floor: Generic - 200mm</td>
                <td><span class="linkified">DB.Element(456005)</span></td>
                <td>ID:456005 | Mass | Mass: Conceptual Mass | Centroid: (42.7, 31.5, 12.0) | Thickness: 200mm</td>
                <td>31.8</td>
                <td>Passed</td>
            </tr>
        </table>

        <h3 style="margin:12px 0 6px 0;">Summary</h3>
        <div class="summary-cards">
            <div class="card card-success">
                <div class="card-label">✅ Matched</div>
                <div class="card-value">5</div>
            </div>
            <div class="card card-error">
                <div class="card-label">❌ Missing</div>
                <div class="card-value">2</div>
            </div>
            <div class="card card-error">
                <div class="card-label">❌ Extra</div>
                <div class="card-value">1</div>
            </div>
            <div class="card card-warning">
                <div class="card-label">⚠️ Duplicate ARC</div>
                <div class="card-value">2</div>
            </div>
            <div class="card card-warning">
                <div class="card-label">⚠️ Duplicate Mass</div>
                <div class="card-value">0</div>
            </div>
            <div class="card card-success">
                <div class="card-label">✅ Centroid Passed</div>
                <div class="card-value">4</div>
            </div>
            <div class="card card-error">
                <div class="card-label">❌ Centroid Failed</div>
                <div class="card-value">1</div>
            </div>
        </div>

        <div class="info-block">
            <div><b>Rule:</b> TabooZone Many-to-Many Validation</div>
            <div><b>ARC Document:</b> Munich_RE_Architectural_Model_v2.3.rvt</div>
            <div><b>Mass Document:</b> Munich_RE_Mass_Study_v1.8.rvt</div>
            <div><b>Total ARC elements:</b> 324 &nbsp;&nbsp; <b>Total Mass elements:</b> 257</div>
        </div>

        <div class="status-banner">
            <b>Overall Status:</b> ❌ FAILED
        </div>

        <!-- Additional mockup data section to demonstrate scale -->
        <h3>Sample Data Overview (Demonstrating Scale)</h3>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 10px 0;">
            <h4 style="margin: 0 0 10px 0; color: #495057;">Many-to-Many Relationship Examples:</h4>
            <ul style="margin: 5px 0; color: #6c757d; font-size: 13px;">
                <li><strong>Mass Element 456001:</strong> Relates to Wall(123001) + Column(123002) → Composite GUID formed by combining both</li>
                <li><strong>Mass Element 456002:</strong> Relates to Floor(123003) + Curtain Wall(123004) + Steel Column(123005) → 3-element composite</li>
                <li><strong>Mass Element 456003:</strong> Shares Wall(123001) with Mass 456001, plus additional Column(123006)</li>
                <li><strong>Typical Scenarios:</strong> Structural masses representing building zones that span multiple architectural elements</li>
            </ul>

            <h4 style="margin: 15px 0 10px 0; color: #495057;">Scale Demonstration:</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 10px 0;">
                <div style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #dee2e6;">
                    <div style="font-weight: 600; color: #495057;">ARC Elements (324 total)</div>
                    <div style="font-size: 12px; color: #6c757d;">
                        • Walls: 156 elements<br>
                        • Columns: 89 elements<br>
                        • Floors/Slabs: 79 elements
                    </div>
                </div>
                <div style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #dee2e6;">
                    <div style="font-weight: 600; color: #495057;">Mass Elements (257 total)</div>
                    <div style="font-size: 12px; color: #6c757d;">
                        • Each relates to 2-3 ARC elements<br>
                        • Composite GUIDs: semicolon-separated<br>
                        • Shared relationships common
                    </div>
                </div>
                <div style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #dee2e6;">
                    <div style="font-weight: 600; color: #495057;">Validation Results</div>
                    <div style="font-size: 12px; color: #6c757d;">
                        • 5 matched pairs shown<br>
                        • 2 missing, 1 extra<br>
                        • 2 duplicate ARC GUIDs<br>
                        • 1 centroid failure (>50mm)
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top: 20px; padding: 10px; background: #e3f2fd; border-radius: 6px; border-left: 4px solid #2196f3;">
            <strong>Note:</strong> This is a mockup demonstration of how the TabooZone QA report would appear with complex many-to-many relationships in a production environment. The composite GUID format (semicolon-separated) allows tracking relationships where each Mass element corresponds to multiple ARC elements, enabling more sophisticated architectural validation workflows.
        </div>
    </div>
</body>
</html>
