<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabooZone QA Report - HG NEW CONSTRUCTION SLAB</title>
    <style>
        body {
            font-family: Segoe UI, Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            margin: 4px 0 8px 0;
            color: #2c3e50;
        }
        h3 {
            margin: 10px 0 6px 0;
            color: #34495e;
        }
        .header-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 20px;
        }
        .summary-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 8px 0 16px 0;
        }
        .card {
            padding: 8px 12px;
            border-radius: 6px;
            min-width: 140px;
            border: 1px solid rgba(0,0,0,0.1);
        }
        .card-success { background: #d5f5e3; color: #111; }
        .card-error { background: #fadbd8; color: #111; }
        .card-warning { background: #fcf3cf; color: #111; }
        .card-label {
            font-size: 12px;
            opacity: 0.8;
        }
        .card-value {
            font-size: 18px;
            font-weight: 600;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        th {
            text-align: left;
            padding: 8px;
            background: #f1f2f6;
            border: 1px solid #ddd;
            font-weight: 600;
        }
        td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        .row-matched-pass { background: #eafaf1; }
        .row-matched-fail { background: #fdecea; }
        .row-missing { background: #fdecea; }
        .row-extra { background: #fdecea; }
        .row-duplicate { background: #fff4d6; }
        .info-block {
            margin: 4px 0 10px 0;
            padding: 8px;
            border-radius: 6px;
            background: #f9f9f9;
            border: 1px solid rgba(0,0,0,0.08);
            font-size: 12px;
            color: #444;
        }
        .status-banner {
            margin: 12px 0;
            padding: 10px;
            border-radius: 6px;
            background: #f4f6f6;
            font-weight: bold;
        }
        .linkified {
            color: #0066cc;
            text-decoration: underline;
            cursor: pointer;
        }
        .composite-guid {
            font-family: monospace;
            font-size: 11px;
            color: #666;
            line-height: 1.4;
            max-width: 300px;
            word-break: break-all;
        }
        .guid-part {
            display: inline-block;
            margin-right: 8px;
            padding: 2px 4px;
            background: #f8f9fa;
            border-radius: 3px;
            margin-bottom: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="font-family:Segoe UI, Arial, sans-serif;margin:8px 0;">
            <h2>TabooZone QA Report - Complex Many-to-Many Validation</h2>
            <div class="header-info">
                ARC: Architectural Elements | Mass: Mass Elements | Tol: 50.0 mm
            </div>
        </div>

        <h3>Missing in Mass (ARC present, Mass missing)</h3>
        <table>
            <tr>
                <th>Composite GUID</th>
                <th>ARC Elements</th>
                <th>ARC Info</th>
            </tr>
            <tr class="row-missing">
                <td class="composite-guid">
                    <span class="guid-part">a1b2c3d4-e5f6-7890-abcd-ef1234567890</span><br>
                    <span class="guid-part">b2c3d4e5-f6g7-8901-bcde-f23456789012</span>
                </td>
                <td>DB.Element(156789), DB.Element(234567)</td>
                <td>ID:156789 | Walls | Basic Wall: Generic - 200mm<br>ID:234567 | Structural Columns | Concrete-Rectangular-Column: 400 x 600mm</td>
            </tr>
        </table>

        <h3>Extra in Mass (Mass present, ARC missing)</h3>
        <table>
            <tr>
                <th>Composite GUID</th>
                <th>Mass Element</th>
                <th>Mass Info</th>
            </tr>
            <tr class="row-extra">
                <td class="composite-guid">
                    <span class="guid-part">f6g7h8i9-j0k1-2345-fghi-************</span><br>
                    <span class="guid-part">g7h8i9j0-k1l2-3456-ghij-************</span>
                </td>
                <td><span class="linkified">DB.Element(789012)</span></td>
                <td>ID:789012 | Mass | Mass: Conceptual Mass</td>
            </tr>
        </table>

        <h3>Duplicate GUIDs in ARC</h3>
        <table>
            <tr>
                <th>Composite GUID</th>
                <th>ARC Element</th>
                <th>ARC Info</th>
            </tr>
            <tr class="row-duplicate">
                <td class="composite-guid">
                    <span class="guid-part">h8i9j0k1-l2m3-4567-hijk-************</span><br>
                    <span class="guid-part">i9j0k1l2-m3n4-5678-ijkl-************</span>
                </td>
                <td>DB.Element(890123)</td>
                <td>ID:890123 | Walls | Basic Wall: Exterior - Brick on CMU</td>
            </tr>
        </table>

        <h3>Duplicate GUIDs in Mass</h3>
        <i>No duplicate Mass GUIDs.</i>

        <h3>Matched GUID Pairs</h3>
        <table>
            <tr>
                <th>Composite GUID</th>
                <th>ARC Info</th>
                <th>Mass Element</th>
                <th>Mass Info</th>
                <th>Distance (mm)</th>
                <th>Result</th>
            </tr>
            <!-- Many-to-Many Example 1: Mass relates to 2 ARC elements -->
            <tr class="row-matched-pass">
                <td class="composite-guid">
                    <span class="guid-part">12345678-90ab-cdef-1234-567890abcdef</span><br>
                    <span class="guid-part">23456789-01bc-def1-2345-6789012bcdef</span>
                </td>
                <td>ID:123001 | Walls | Basic Wall: Generic - 200mm<br>ID:123002 | Structural Columns | Concrete-Rectangular-Column: 300 x 300mm</td>
                <td><span class="linkified">DB.Element(456001)</span></td>
                <td>ID:456001 | Mass | Mass: Conceptual Mass | Centroid: (12.5, 8.3, 3.0) | Thickness: 200mm</td>
                <td>15.2</td>
                <td>Passed</td>
            </tr>
            <!-- Many-to-Many Example 2: Mass relates to 3 ARC elements -->
            <tr class="row-matched-pass">
                <td class="composite-guid">
                    <span class="guid-part">34567890-12cd-ef12-3456-789012cdef12</span><br>
                    <span class="guid-part">45678901-23de-f123-4567-890123def123</span><br>
                    <span class="guid-part">56789012-34ef-1234-5678-901234ef1234</span>
                </td>
                <td>ID:123003 | Floors | Floor: Generic - 150mm<br>ID:123004 | Walls | Curtain Wall: Storefront<br>ID:123005 | Structural Columns | Steel-W-Column: W12X65</td>
                <td><span class="linkified">DB.Element(456002)</span></td>
                <td>ID:456002 | Mass | Mass: Conceptual Mass | Centroid: (25.1, 15.7, 6.0) | Thickness: 150mm</td>
                <td>8.7</td>
                <td>Passed</td>
            </tr>
            <!-- Shared ARC elements example: Mass 3 shares some ARC elements with Mass 1 -->
            <tr class="row-matched-fail">
                <td class="composite-guid">
                    <span class="guid-part">12345678-90ab-cdef-1234-567890abcdef</span><br>
                    <span class="guid-part">67890123-45ef-2345-6789-012345ef2345</span>
                </td>
                <td>ID:123001 | Walls | Basic Wall: Generic - 200mm<br>ID:123006 | Structural Columns | Steel-HSS-Column: HSS8X8X1/2</td>
                <td><span class="linkified">DB.Element(456003)</span></td>
                <td>ID:456003 | Mass | Mass: Conceptual Mass | Centroid: (18.9, 12.4, 4.5) | Thickness: 250mm</td>
                <td>75.3</td>
                <td>Failed</td>
            </tr>
        </table>

        <h3 style="margin:12px 0 6px 0;">Summary</h3>
        <div class="summary-cards">
            <div class="card card-success">
                <div class="card-label">✅ Matched</div>
                <div class="card-value">3</div>
            </div>
            <div class="card card-error">
                <div class="card-label">❌ Missing</div>
                <div class="card-value">1</div>
            </div>
            <div class="card card-error">
                <div class="card-label">❌ Extra</div>
                <div class="card-value">1</div>
            </div>
            <div class="card card-warning">
                <div class="card-label">⚠️ Duplicate ARC</div>
                <div class="card-value">1</div>
            </div>
            <div class="card card-warning">
                <div class="card-label">⚠️ Duplicate Mass</div>
                <div class="card-value">0</div>
            </div>
            <div class="card card-success">
                <div class="card-label">✅ Centroid Passed</div>
                <div class="card-value">2</div>
            </div>
            <div class="card card-error">
                <div class="card-label">❌ Centroid Failed</div>
                <div class="card-value">1</div>
            </div>
        </div>

        <div class="info-block">
            <div><b>Rule:</b> TabooZone Many-to-Many Validation</div>
            <div><b>ARC Document:</b> Munich_RE_Architectural_Model_v2.3.rvt</div>
            <div><b>Mass Document:</b> Munich_RE_Mass_Study_v1.8.rvt</div>
            <div><b>Total ARC elements:</b> 324 &nbsp;&nbsp; <b>Total Mass elements:</b> 257</div>
        </div>

        <div class="status-banner">
            <b>Overall Status:</b> ❌ FAILED
        </div>
    </div>
</body>
</html>
