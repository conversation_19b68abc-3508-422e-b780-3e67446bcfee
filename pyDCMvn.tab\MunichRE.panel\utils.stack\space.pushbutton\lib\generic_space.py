from DCMvn.core import DB
from DCMvn.core.framework import Trace
from DCMvn.forms import alert
from DCMvn.revit.geometry import GetElementMergedSolid, GetElementMeshes

from space_props import GenericSpaceProperties
from factory.curve_factory import CurveFactory
from cached_collector import CachedCollector

class GenericSpace:
    def __init__(self, generic_space, host_collector):
        # type: (DB.Element, CachedCollector) -> None
        """Initialize a GenericSpace instance.
        This class is used to represent a generic space in the system.
        Args:
            generic_space (DB.Element): The generic space element from the linked model.
        """
        self.__host_collector = host_collector  # type: CachedCollector
        self.__space = generic_space
        self.__geometry = self.__get_geometry()
        self.__space_location = self.__compute_space_location()
        
    @property
    def height(self):
        return self.__space.LookupParameter(GenericSpaceProperties.Height).AsDouble()
        
    @property
    def geometry(self):
        return self.__geometry

    @property
    def number(self):
        return self.__space.LookupParameter(GenericSpaceProperties.Number).AsString()

    @property
    def name(self):
        return self.__space.LookupParameter(GenericSpaceProperties.Name).AsString()

    @property
    def guid(self):
        return self.__space.get_Parameter(GenericSpaceProperties.Guid).AsString()

    @property
    def level(self):
        # type: () -> DB.Level
        """Get the level of the space."""
        level_name = self.__space.LookupParameter(GenericSpaceProperties.Level).AsString()
        return self.__host_collector.level_dicts[level_name]
    
    @property
    def attached_plan_view(self):
        # type: () -> DB.ViewPlan | None
        """Get the attached plan view of the space.
        
        Returns:
            DB.ViewPlan | None: The attached plan view of the space, or None if not found.
        """
        try:
            return self.__host_collector.views_dicts[self.level.Name]
        except KeyError:
            alert("No ORI plan view found for the space's level.")
            return None
        
    @property
    def curve_arrays(self):
        # type: () -> list[DB.CurveArray]
        """Get the curve arrays of the space."""
        return self.__space_location[0]

    @property
    def base_offset(self):
        # type: () -> float
        """Get the base offset of the space.

        Returns:
            float: The base offset of the space in feet.
        """
        return self.__space_location[1]

    @property
    def limit_offset(self):
        # type: () -> float
        """Get the limit offset of the space.

        Returns:
            float: The limit offset of the space in feet.
        """
        return self.__space_location[2]

    @property
    def upper_limit(self):
        # type: () -> DB.ElementId
        """Get the upper limit of the space.

        Returns:
            DB.ElementId: The upper level id of the space.
        """
        return self.__upper_limit
    
    def __get_geometry(self):
        # type: () -> DB.Solid | list[DB.Mesh]
        """Get the geometry of the space.
        This method retrieves the geometry of the space, either as a solid or as meshes.
        Returns:
            DB.Solid | list[DB.Mesh]: The geometry of the space, either as a solid or a list of meshes.
        """
        opt = DB.Options()
        opt.ComputeReferences = False
        opt.IncludeNonVisibleObjects = False
        opt.DetailLevel = DB.ViewDetailLevel.Coarse
        meshes = GetElementMeshes(self.__space, transform=self.__host_collector.link_transform, option=opt)
        if not meshes or len(meshes) == 0:
            solid = GetElementMergedSolid(self.__space, transform=self.__host_collector.link_transform, option=opt)
            return solid
        return meshes[0]
    
    def __compute_space_location(self):
        # type: () -> (list[DB.CurveArray], float, float)
        """Get the curve arrays of the space.
        This method retrieves the curve arrays of the space, which are used for boundary representation.
        Returns:
            list[DB.CurveArray]: The curve arrays of the space.
        """
        if isinstance(self.__geometry, DB.Solid):
            space_curves_groups = CurveFactory.get_boundary_curves_from_solid(self.__geometry, self.__host_collector.link_transform)
            # top_space_curves_groups = CurveFactory.get_boundary_curves_from_solid(self.__geometry, self.__host_collector.link_transform, bottom=False)
        else:
            space_curves_groups = CurveFactory.get_boundary_curves_from_mesh(self.__geometry, self.__host_collector.link_transform)
            # top_space_curves_groups = CurveFactory.get_boundary_curves_from_mesh(self.__geometry, self.__host_collector.link_transform, bottom=False)
        
        # Base offset
        level = self.level
        level_elevation = level.Elevation
        bottom_generic_space = space_curves_groups[0][0].GetEndPoint(0).Z if space_curves_groups else 0.0
        # Trace.Write(space_curves_groups[0][0].GetEndPoint(0))
        base_offset = bottom_generic_space - level_elevation if bottom_generic_space < level_elevation else 0.0
        
        # Limit offset
        # top_generic_space = top_space_curves_groups[0][0].GetEndPoint(0).Z if top_space_curves_groups else 0.0
        limit_offset = abs(self.height + base_offset)
        
        # CurveArray
        curve_arrays = []
        for curves in space_curves_groups:
            curve_array = DB.CurveArray()
            for curve in curves:
                # Move curve Z coordinate to level elevation
                start_point = curve.GetEndPoint(0)
                end_point = curve.GetEndPoint(1)
                
                # Create new points with level elevation Z coordinate
                new_start = DB.XYZ(start_point.X, start_point.Y, level_elevation)
                new_end = DB.XYZ(end_point.X, end_point.Y, level_elevation)
                
                # Create new curve at level elevation
                adjusted_curve = DB.Line.CreateBound(new_start, new_end)
                curve_array.Append(adjusted_curve)
            curve_arrays.append(curve_array)
        
        return curve_arrays, base_offset, limit_offset