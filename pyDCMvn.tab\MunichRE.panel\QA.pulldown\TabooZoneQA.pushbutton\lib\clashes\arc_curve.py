from DCMvn.core import DB

class ArcCurve:
    """ Provide a wrapper for DB.Arc curve """
    def __init__(self, arc):
        self.__arc = arc

    @property
    def radius(self):
        # type: () -> float
        """ Get the radius of the arc """
        return self.__arc.Radius

    @property
    def mid_point(self):
        # type: () -> DB.XYZ
        """ Get the center of the arc """
        return self.__arc.Evaluate(0.5, True)

    @property
    def center(self):
        # type: () -> DB.XYZ
        """ Get the center of the arc """
        return self.__arc.Center

    @property
    def direction(self):
        # type: () -> DB.XYZ
        """ Get the direction of the arc mid-point to the center """
        return (self.center - self.mid_point).Normalize()

    @property
    def arc(self):
        # type: () -> DB.Arc
        """ Get the DB.Arc object """
        return self.__arc

    def extend_center(self, distance, elevation):
        # type: (float, float) -> DB.XYZ
        """
        Extend the center of the arc by the direction from the mid-point of the arc
        Args:
            distance (float): the distance to extend (ft)
            elevation (float): the elevation to extend (ft)
        Returns:
            DB.XYZ: the extended point
        """
        extend_direction = self.center + self.direction * distance
        return DB.XYZ(extend_direction.X, extend_direction.Y, extend_direction.Z + elevation)

    @property
    def start_tangent_direction(self):
        # type: () -> DB.XYZ
        """ Get the start extend direction of the arc """
        start_arc = self.__arc.GetEndPoint(0)
        derivatives = self.__arc.ComputeDerivatives(0.0, True)
        direction = derivatives.BasisX.Normalize()

        end_to_start = (self.__arc.GetEndPoint(1) - start_arc).Normalize()
        if direction.DotProduct(end_to_start) > 0:
            direction = - direction
        return direction

    @property
    def end_tangent_direction(self):
        # type: () -> DB.XYZ
        """ Get the end extend direction of the arc """
        end_arc = self.__arc.GetEndPoint(1)
        derivatives = self.__arc.ComputeDerivatives(1.0, True)
        direction = derivatives.BasisX.Normalize()

        start_to_end = (end_arc - self.__arc.GetEndPoint(0)).Normalize()
        if direction.DotProduct(start_to_end) < 0:
            direction = - direction

        return direction