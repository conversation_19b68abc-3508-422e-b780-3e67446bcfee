﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MahApps.Metro" Version="2.4.10" />
  </ItemGroup>

  <ItemGroup>
    <Page Include="..\..\..\MunichRE.panel\geometrycontrol.stack\geometryalignment.pushbutton\ui\geometryalignment.xaml">
      <Link>geometryalignment.xaml</Link>
    </Page>
    <None Include="..\..\..\MunichRE.panel\geometrycontrol.stack\geometryalignment.pushbutton\ui\geometryalignment.xaml" />

    <Page Include="..\..\..\MunichRE.panel\QA.pulldown\guidalignment.pushbutton\ui\guidalignment.xaml">
      <Link>guidalignment.xaml</Link>
    </Page>
    <None Include="..\..\..\MunichRE.panel\QA.pulldown\guidalignment.pushbutton\ui\guidalignment.xaml" />

    <Page Include="..\..\..\MunichRE.panel\Parameter.stack\shareparam_creator.pushbutton\components\ui\SharedParameterPath.xaml">
      <Link>SharedParameterPath.xaml</Link>
    </Page>
    <None Include="..\..\..\MunichRE.panel\Parameter.stack\shareparam_creator.pushbutton\components\ui\SharedParameterPath.xaml" />

    <Page Include="..\..\..\MunichRE.panel\QA.pulldown\SearchElements.pushbutton\lib\ui\search_elements.xaml">
      <Link>search_elements.xaml</Link>
    </Page>
    <None Include="..\..\..\MunichRE.panel\QA.pulldown\SearchElements.pushbutton\lib\ui\search_elements.xaml" />

    <Page Include="..\..\..\MunichRE.panel\utils.stack\room_number.pushbutton\lib\ui\room_mapping_main_view.xaml">
      <Link>room_mapping_main_view.xaml</Link>
    </Page>
    <None Include="..\..\..\MunichRE.panel\utils.stack\room_number.pushbutton\lib\ui\room_mapping_main_view.xaml" />
  </ItemGroup>

</Project>
