# coding: utf-8
"""
MEP-Spatial Intersection Tests (Tests 5-9)
Tests for MEP element intersection with spatial elements, with and without proximity.
"""
import clr
from DCMvn.core import DB, HOST_APP
from DCMvn.core.framework import Debug, System
from DCMvn.forms import alert
from tests.test_utils import (TestOptions, SpatialElementSelector, MepElementSelector, 
                             SpatialWrapperFactory, GeometryHelper, TestLogger, IntersectionTester)

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)


def test_05_mep_intersection_with_linked_spatial():
    """
    Test 5: Select MEP element then check intersection with linked spatial.
    Tests intersection with proximity detection and visual debugging.
    """
    TestLogger.log_test_start("05 - MEP Intersection with Linked Spatial")
    
    try:
        # Select MEP element
        alert("Test 5: Select a MEP element from current document")
        mep_element = MepElementSelector.pick_mep_element()
        if not mep_element:
            TestLogger.log_test_end("05 - MEP Intersection", success=False)
            return
        
        TestLogger.log_element_info(mep_element, "MEP Element")
        
        # Select linked spatial element
        alert("Now select a linked spatial element to test intersection")
        spatial_element, link_instance, doc_wrapper = SpatialElementSelector.pick_linked_spatial()
        if not spatial_element:
            TestLogger.log_test_end("05 - MEP Intersection", success=False)
            return
        
        TestLogger.log_element_info(spatial_element, "Linked Spatial Element")
        
        # Create spatial wrapper
        spatial_wrapper = SpatialWrapperFactory.create_spatial_wrapper(spatial_element, doc_wrapper)
        if not spatial_wrapper:
            Debug.WriteLine("Failed to create spatial wrapper")
            TestLogger.log_test_end("05 - MEP Intersection", success=False)
            return
        
        # Test intersection with proximity and visual debugging
        tester = IntersectionTester()
        is_intersecting, distance, details = tester.test_mep_spatial_intersection(
            mep_element, spatial_wrapper, use_proximity=True, show_visual=True
        )
        
        # Log results
        Debug.WriteLine("Intersection Test Results:")
        Debug.WriteLine("  Is Intersecting: {0}".format(is_intersecting))
        Debug.WriteLine("  Distance: {0}".format(distance))
        Debug.WriteLine("  Distance (mm): {0:.2f}".format(distance * 304.8))
        Debug.WriteLine("  Strategy Used: {0}".format(details.get('strategy_used', 'Unknown')))
        Debug.WriteLine("  Proximity Enabled: {0}".format(details.get('proximity_enabled', False)))
        
        # Log MEP classification details
        mep_info = details.get('mep_type_info', {})
        Debug.WriteLine("MEP Classification:")
        Debug.WriteLine("  Is Family Instance: {0}".format(mep_info.get('is_family_instance', 'Unknown')))
        Debug.WriteLine("  Has Location Curve: {0}".format(mep_info.get('has_location_curve', 'Unknown')))
        Debug.WriteLine("  Has Location Point: {0}".format(mep_info.get('has_location_point', 'Unknown')))
        Debug.WriteLine("  Room Calc Points: {0}".format(len(mep_info.get('room_calc_points', []))))
        
        # Result message
        result_status = "INTERSECTING" if is_intersecting else "NOT INTERSECTING"
        alert("Test 5 Completed!\n\nMEP-Spatial Intersection: {0}\nDistance: {1:.2f}mm\n\nVisual debugging enabled - check Revit view for geometry traces!\nCheck Output window for details.".format(
            result_status, distance * 304.8))
        
        TestLogger.log_test_end("05 - MEP Intersection", success=True)
        
    except Exception as e:
        Debug.WriteLine("Error in test_05: {0}".format(str(e)))
        alert("Test 5 Failed: {0}".format(str(e)))
        TestLogger.log_test_end("05 - MEP Intersection", success=False)


def test_06_mep_intersection_with_proximity():
    """
    Test 6: Select MEP element then check intersection with linked spatial with 300mm offset.
    Tests intersection with proximity detection enabled.
    """
    TestLogger.log_test_start("06 - MEP Intersection with Proximity (300mm)")
    
    try:
        # Select MEP element
        alert("Test 6: Select a MEP element from current document")
        mep_element = MepElementSelector.pick_mep_element()
        if not mep_element:
            TestLogger.log_test_end("06 - MEP Intersection with Proximity", success=False)
            return
        
        TestLogger.log_element_info(mep_element, "MEP Element")
        
        # Select linked spatial element
        alert("Now select a linked spatial element for proximity intersection test (300mm)")
        spatial_element, link_instance, doc_wrapper = SpatialElementSelector.pick_linked_spatial()
        if not spatial_element:
            TestLogger.log_test_end("06 - MEP Intersection with Proximity", success=False)
            return
        
        TestLogger.log_element_info(spatial_element, "Linked Spatial Element")
        
        # Create spatial wrapper
        spatial_wrapper = SpatialWrapperFactory.create_spatial_wrapper(spatial_element, doc_wrapper)
        if not spatial_wrapper:
            Debug.WriteLine("Failed to create spatial wrapper")
            TestLogger.log_test_end("06 - MEP Intersection with Proximity", success=False)
            return
        
        # Test intersection with proximity and visual debugging
        tester = IntersectionTester()
        is_intersecting, distance, details = tester.test_mep_spatial_intersection(
            mep_element, spatial_wrapper, use_proximity=True, show_visual=True
        )
        
        # Log results
        Debug.WriteLine("Proximity Intersection Test Results:")
        Debug.WriteLine("  Is Intersecting: {0}".format(is_intersecting))
        Debug.WriteLine("  Distance: {0}".format(distance))
        Debug.WriteLine("  Distance (mm): {0:.2f}".format(distance * 304.8))
        Debug.WriteLine("  Proximity Distance: 300mm")
        Debug.WriteLine("  Strategy Used: {0}".format(details.get('strategy_used', 'Unknown')))
        Debug.WriteLine("  Proximity Enabled: {0}".format(details.get('proximity_enabled', False)))
        
        # Determine if intersection is due to proximity
        if is_intersecting and distance > 0:
            if distance * 304.8 <= 300:  # Within proximity range
                Debug.WriteLine("  Intersection Type: PROXIMITY (within 300mm)")
            else:
                Debug.WriteLine("  Intersection Type: DIRECT")
        elif is_intersecting and distance == 0:
            Debug.WriteLine("  Intersection Type: DIRECT (inside spatial)")
        
        # Result message
        result_status = "INTERSECTING" if is_intersecting else "NOT INTERSECTING"
        proximity_note = " (within 300mm proximity)" if is_intersecting and distance > 0 else ""
        alert("Test 6 Completed!\n\nMEP-Spatial Intersection: {0}{1}\nDistance: {2:.2f}mm\n\nVisual debugging enabled - check Revit view for geometry traces!\nCheck Output window for details.".format(
            result_status, proximity_note, distance * 304.8))
        
        TestLogger.log_test_end("06 - MEP Intersection with Proximity", success=True)
        
    except Exception as e:
        Debug.WriteLine("Error in test_06: {0}".format(str(e)))
        alert("Test 6 Failed: {0}".format(str(e)))
        TestLogger.log_test_end("06 - MEP Intersection with Proximity", success=False)


def test_07_linked_spatial_show_intersection_meps():
    """
    Test 7: Select linked spatial then show all intersection MEPs by category.
    Finds all MEP elements that intersect with a selected spatial element.
    """
    TestLogger.log_test_start("07 - Show All MEP Intersections by Category")
    
    try:
        # Select linked spatial element
        alert("Test 7: Select a linked spatial element to find all intersecting MEP elements")
        spatial_element, link_instance, doc_wrapper = SpatialElementSelector.pick_linked_spatial()
        if not spatial_element:
            TestLogger.log_test_end("07 - Show MEP Intersections", success=False)
            return
        
        TestLogger.log_element_info(spatial_element, "Linked Spatial Element")
        
        # Create spatial wrapper
        spatial_wrapper = SpatialWrapperFactory.create_spatial_wrapper(spatial_element, doc_wrapper)
        if not spatial_wrapper:
            Debug.WriteLine("Failed to create spatial wrapper")
            TestLogger.log_test_end("07 - Show MEP Intersections", success=False)
            return
        
        # Define MEP categories to test
        mep_categories = [
            (DB.BuiltInCategory.OST_DuctCurves, "Duct Curves"),
            (DB.BuiltInCategory.OST_PipeCurves, "Pipe Curves"),
            (DB.BuiltInCategory.OST_CableTray, "Cable Trays"),
            (DB.BuiltInCategory.OST_Conduit, "Conduits"),
            (DB.BuiltInCategory.OST_DuctFitting, "Duct Fittings"),
            (DB.BuiltInCategory.OST_PipeFitting, "Pipe Fittings"),
            (DB.BuiltInCategory.OST_ElectricalFixtures, "Electrical Fixtures"),
            (DB.BuiltInCategory.OST_ElectricalEquipment, "Electrical Equipment"),
            (DB.BuiltInCategory.OST_MechanicalEquipment, "Mechanical Equipment")
        ]
        
        Debug.WriteLine("Searching MEP elements by category...")
        tester = IntersectionTester()
        total_intersections = 0
        
        for category, category_name in mep_categories:
            try:
                # Collect elements in this category
                category_filter = DB.ElementCategoryFilter(category)
                elements = (DB.FilteredElementCollector(HOST_APP.doc)
                           .WhereElementIsNotElementType()
                           .WherePasses(category_filter)
                           .ToElements())
                
                Debug.WriteLine("\n--- Testing {0} ({1} elements) ---".format(category_name, len(elements)))
                
                intersecting_elements = []
                
                for element in elements:
                    try:
                        is_intersecting, distance, details = tester.test_mep_spatial_intersection(
                            element, spatial_wrapper, use_proximity=False
                        )
                        
                        if is_intersecting:
                            intersecting_elements.append((element, distance))
                            
                    except Exception as e:
                        Debug.WriteLine("Error testing element {0}: {1}".format(element.Id, str(e)))
                
                # Report results for this category
                if intersecting_elements:
                    Debug.WriteLine("  Intersecting {0}: {1}".format(category_name, len(intersecting_elements)))
                    # Sort by distance (closest first)
                    intersecting_elements.sort(key=lambda x: x[1])
                    
                    for i, (element, distance) in enumerate(intersecting_elements[:5]):  # Show top 5
                        element_name = element.Name if hasattr(element, 'Name') else 'Unknown'
                        Debug.WriteLine("    {0}. ID: {1} | Name: {2} | Distance: {3:.2f}mm".format(
                            i+1, element.Id, element_name, distance * 304.8))
                    
                    if len(intersecting_elements) > 5:
                        Debug.WriteLine("    ... and {0} more".format(len(intersecting_elements) - 5))
                    
                    total_intersections += len(intersecting_elements)
                else:
                    Debug.WriteLine("  No intersecting {0}".format(category_name))
                    
            except Exception as e:
                Debug.WriteLine("Error processing category {0}: {1}".format(category_name, str(e)))
        
        Debug.WriteLine("\n=== SUMMARY ===")
        Debug.WriteLine("Total intersecting MEP elements: {0}".format(total_intersections))
        
        alert("Test 7 Completed!\n\nFound {0} MEP elements intersecting with spatial.\nCheck Output window for detailed breakdown by category.".format(total_intersections))
        TestLogger.log_test_end("07 - Show MEP Intersections", success=True)
        
    except Exception as e:
        Debug.WriteLine("Error in test_07: {0}".format(str(e)))
        alert("Test 7 Failed: {0}".format(str(e)))
        TestLogger.log_test_end("07 - Show MEP Intersections", success=False)


def test_08_linked_spatial_show_proximity_meps():
    """
    Test 8: Select linked spatial then show all MEPs within 300mm proximity by category.
    Finds all MEP elements within proximity of a selected spatial element.
    """
    TestLogger.log_test_start("08 - Show All MEP Proximity Intersections (300mm)")
    
    try:
        # Select linked spatial element
        alert("Test 8: Select a linked spatial element to find all MEP elements within 300mm proximity")
        spatial_element, link_instance, doc_wrapper = SpatialElementSelector.pick_linked_spatial()
        if not spatial_element:
            TestLogger.log_test_end("08 - Show MEP Proximity", success=False)
            return
        
        TestLogger.log_element_info(spatial_element, "Linked Spatial Element")
        
        # Create spatial wrapper
        spatial_wrapper = SpatialWrapperFactory.create_spatial_wrapper(spatial_element, doc_wrapper)
        if not spatial_wrapper:
            Debug.WriteLine("Failed to create spatial wrapper")
            TestLogger.log_test_end("08 - Show MEP Proximity", success=False)
            return
        
        # Use bounding box filter for performance (pre-filter)
        bbox = spatial_wrapper.bounding_box
        if bbox:
            options = TestOptions()
            expanded_outline = GeometryHelper.create_expanded_outline(bbox, options.proximity_distance_internal)
            bbox_filter = DB.BoundingBoxIntersectsFilter(expanded_outline)
            Debug.WriteLine("Using bounding box pre-filter for performance")
        else:
            bbox_filter = None
            Debug.WriteLine("No bounding box available, testing all elements")
        
        # Define MEP categories to test
        mep_categories = [
            (DB.BuiltInCategory.OST_DuctCurves, "Duct Curves"),
            (DB.BuiltInCategory.OST_PipeCurves, "Pipe Curves"),
            (DB.BuiltInCategory.OST_CableTray, "Cable Trays"),
            (DB.BuiltInCategory.OST_Conduit, "Conduits"),
            (DB.BuiltInCategory.OST_DuctFitting, "Duct Fittings"),
            (DB.BuiltInCategory.OST_PipeFitting, "Pipe Fittings"),
            (DB.BuiltInCategory.OST_ElectricalFixtures, "Electrical Fixtures"),
            (DB.BuiltInCategory.OST_ElectricalEquipment, "Electrical Equipment"),
            (DB.BuiltInCategory.OST_MechanicalEquipment, "Mechanical Equipment")
        ]
        
        Debug.WriteLine("Searching MEP elements within 300mm proximity...")
        tester = IntersectionTester()
        total_proximity_hits = 0
        
        for category, category_name in mep_categories:
            try:
                # Collect elements in this category with optional bbox filter
                collector = (DB.FilteredElementCollector(HOST_APP.doc)
                           .WhereElementIsNotElementType()
                           .WherePasses(DB.ElementCategoryFilter(category)))
                
                if bbox_filter:
                    collector = collector.WherePasses(bbox_filter)
                
                elements = collector.ToElements()
                
                Debug.WriteLine("\n--- Testing {0} ({1} candidates) ---".format(category_name, len(elements)))
                
                proximity_elements = []
                
                for element in elements:
                    try:
                        is_intersecting, distance, details = tester.test_mep_spatial_intersection(
                            element, spatial_wrapper, use_proximity=True
                        )
                        
                        if is_intersecting:
                            proximity_elements.append((element, distance))
                            
                    except Exception as e:
                        Debug.WriteLine("Error testing element {0}: {1}".format(element.Id, str(e)))
                
                # Report results for this category
                if proximity_elements:
                    Debug.WriteLine("  Elements within 300mm: {0}".format(len(proximity_elements)))
                    # Sort by distance (closest first)
                    proximity_elements.sort(key=lambda x: x[1])
                    
                    # Separate direct intersections from proximity
                    direct_intersections = [e for e in proximity_elements if e[1] == 0]
                    proximity_only = [e for e in proximity_elements if e[1] > 0]
                    
                    if direct_intersections:
                        Debug.WriteLine("    Direct intersections: {0}".format(len(direct_intersections)))
                    if proximity_only:
                        Debug.WriteLine("    Proximity only: {0}".format(len(proximity_only)))
                    
                    # Show top 5 results
                    for i, (element, distance) in enumerate(proximity_elements[:5]):
                        element_name = element.Name if hasattr(element, 'Name') else 'Unknown'
                        intersection_type = "Direct" if distance == 0 else "Proximity"
                        Debug.WriteLine("    {0}. ID: {1} | {2} | Distance: {3:.2f}mm | {4}".format(
                            i+1, element.Id, element_name, distance * 304.8, intersection_type))
                    
                    if len(proximity_elements) > 5:
                        Debug.WriteLine("    ... and {0} more".format(len(proximity_elements) - 5))
                    
                    total_proximity_hits += len(proximity_elements)
                else:
                    Debug.WriteLine("  No {0} within 300mm".format(category_name))
                    
            except Exception as e:
                Debug.WriteLine("Error processing category {0}: {1}".format(category_name, str(e)))
        
        Debug.WriteLine("\n=== SUMMARY ===")
        Debug.WriteLine("Total MEP elements within 300mm: {0}".format(total_proximity_hits))
        
        alert("Test 8 Completed!\n\nFound {0} MEP elements within 300mm of spatial.\nCheck Output window for detailed breakdown by category.".format(total_proximity_hits))
        TestLogger.log_test_end("08 - Show MEP Proximity", success=True)
        
    except Exception as e:
        Debug.WriteLine("Error in test_08: {0}".format(str(e)))
        alert("Test 8 Failed: {0}".format(str(e)))
        TestLogger.log_test_end("08 - Show MEP Proximity", success=False)


def test_09_interactive_mep_spatial_intersection():
    """
    Test 9: Select linked spatial then select MEP element to check intersection.
    Interactive test allowing user to pick both elements manually.
    """
    TestLogger.log_test_start("09 - Interactive MEP-Spatial Intersection")
    
    try:
        # Select linked spatial element first
        alert("Test 9: First select a linked spatial element")
        spatial_element, link_instance, doc_wrapper = SpatialElementSelector.pick_linked_spatial()
        if not spatial_element:
            TestLogger.log_test_end("09 - Interactive Intersection", success=False)
            return
        
        TestLogger.log_element_info(spatial_element, "Linked Spatial Element")
        
        # Create spatial wrapper
        spatial_wrapper = SpatialWrapperFactory.create_spatial_wrapper(spatial_element, doc_wrapper)
        if not spatial_wrapper:
            Debug.WriteLine("Failed to create spatial wrapper")
            TestLogger.log_test_end("09 - Interactive Intersection", success=False)
            return
        
        # Select MEP element
        alert("Now select a MEP element from current document to test intersection")
        mep_element = MepElementSelector.pick_mep_element()
        if not mep_element:
            TestLogger.log_test_end("09 - Interactive Intersection", success=False)
            return
        
        TestLogger.log_element_info(mep_element, "MEP Element")
        
        # Test both direct and proximity intersection
        tester = IntersectionTester()
        
        Debug.WriteLine("Testing intersection scenarios...")
        
        # Test 1: Direct intersection
        Debug.WriteLine("\n--- Direct Intersection Test ---")
        is_direct, distance_direct, details_direct = tester.test_mep_spatial_intersection(
            mep_element, spatial_wrapper, use_proximity=False
        )
        
        Debug.WriteLine("Direct Result:")
        Debug.WriteLine("  Is Intersecting: {0}".format(is_direct))
        Debug.WriteLine("  Distance: {0:.6f} feet ({1:.2f}mm)".format(distance_direct, distance_direct * 304.8))
        Debug.WriteLine("  Strategy: {0}".format(details_direct.get('strategy_used', 'Unknown')))
        
        # Test 2: Proximity intersection (300mm) with visual debugging
        Debug.WriteLine("\n--- Proximity Intersection Test (300mm) with Visual Debugging ---")
        is_proximity, distance_proximity, details_proximity = tester.test_mep_spatial_intersection(
            mep_element, spatial_wrapper, use_proximity=True, show_visual=True
        )
        
        Debug.WriteLine("Proximity Result:")
        Debug.WriteLine("  Is Intersecting: {0}".format(is_proximity))
        Debug.WriteLine("  Distance: {0:.6f} feet ({1:.2f}mm)".format(distance_proximity, distance_proximity * 304.8))
        Debug.WriteLine("  Strategy: {0}".format(details_proximity.get('strategy_used', 'Unknown')))
        
        # Analysis
        Debug.WriteLine("\n--- Analysis ---")
        if is_direct:
            if distance_direct == 0:
                Debug.WriteLine("MEP element is INSIDE the spatial element")
            else:
                Debug.WriteLine("MEP element DIRECTLY intersects spatial boundary")
        elif is_proximity:
            proximity_distance = distance_proximity * 304.8
            Debug.WriteLine("MEP element is NEAR spatial element (distance: {0:.2f}mm)".format(proximity_distance))
        else:
            Debug.WriteLine("MEP element is NOT related to spatial element")
        
        # Create summary message
        if is_direct:
            status = "DIRECT INTERSECTION"
            distance_info = "{0:.2f}mm".format(distance_direct * 304.8)
        elif is_proximity:
            status = "PROXIMITY INTERSECTION"
            distance_info = "{0:.2f}mm (within 300mm)".format(distance_proximity * 304.8)
        else:
            status = "NO INTERSECTION"
            distance_info = "Beyond detection range"
        
        alert("Test 9 Completed!\n\nMEP: {0}\nSpatial: {1}\n\nResult: {2}\nDistance: {3}\n\nVisual debugging enabled for proximity test - check Revit view!\nCheck Output window for details.".format(
            mep_element.Id, spatial_element.Id, status, distance_info))
        
        TestLogger.log_test_end("09 - Interactive Intersection", success=True)
        
    except Exception as e:
        Debug.WriteLine("Error in test_09: {0}".format(str(e)))
        alert("Test 9 Failed: {0}".format(str(e)))
        TestLogger.log_test_end("09 - Interactive Intersection", success=False)


# Test runner for MEP intersection tests
def run_mep_intersection_tests():
    """Run all MEP intersection tests (5-9)."""
    Debug.WriteLine("\n" + "=" * 60)
    Debug.WriteLine("RUNNING MEP INTERSECTION TESTS (5-9)")
    Debug.WriteLine("=" * 60)
    
    tests = [
        ("Test 5 - MEP Intersection", test_05_mep_intersection_with_linked_spatial),
        ("Test 6 - MEP Intersection with Proximity", test_06_mep_intersection_with_proximity),
        ("Test 7 - Show All MEP Intersections", test_07_linked_spatial_show_intersection_meps),
        ("Test 8 - Show MEP Proximity", test_08_linked_spatial_show_proximity_meps),
        ("Test 9 - Interactive Intersection", test_09_interactive_mep_spatial_intersection)
    ]
    
    alert("Starting MEP Intersection Tests\n\nYou will be prompted to select elements for each test.\nTests 5-9 will run sequentially.")
    
    for test_name, test_function in tests:
        try:
            Debug.WriteLine("\n{0}".format("=" * 40))
            Debug.WriteLine("Preparing: {0}".format(test_name))
            Debug.WriteLine("=" * 40)
            test_function()
        except Exception as e:
            Debug.WriteLine("Failed to run {0}: {1}".format(test_name, str(e)))
    
    Debug.WriteLine("\n" + "=" * 60)
    Debug.WriteLine("MEP INTERSECTION TESTS COMPLETED")
    Debug.WriteLine("=" * 60)
    alert("All MEP Intersection Tests Completed!\nCheck Output window for detailed results.")


# Individual test runners for manual execution
def run_test_05():
    """Run only Test 5."""
    test_05_mep_intersection_with_linked_spatial()

def run_test_06():
    """Run only Test 6."""
    test_06_mep_intersection_with_proximity()

def run_test_07():
    """Run only Test 7."""
    test_07_linked_spatial_show_intersection_meps()

def run_test_08():
    """Run only Test 8."""
    test_08_linked_spatial_show_proximity_meps()

def run_test_09():
    """Run only Test 9."""
    test_09_interactive_mep_spatial_intersection()