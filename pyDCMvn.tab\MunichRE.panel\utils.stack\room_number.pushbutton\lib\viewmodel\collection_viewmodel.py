# coding: utf-8
import clr
import System
from DCMvn.forms.mvvm import ViewModelBase

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)  # noqa


class CollectionViewModel(ViewModelBase):
    def __init__(self, collector_service):
        ViewModelBase.__init__(self)
        self.__collector_service = collector_service
        
        # Current selection state
        self.__current_doc_index = -1
        self.__current_cat_index = -1
        
    def update_selection(self, doc_index, cat_index):
        """Update selection and return elements from collector service"""
        # Check if we need to update
        if self.__current_doc_index == doc_index and self.__current_cat_index == cat_index:
            return self.current_elements
        
        # Update current selection
        self.__current_doc_index = doc_index
        self.__current_cat_index = cat_index
        
        # Notify property changes
        self.RaisePropertyChanged("current_elements")
        self.RaisePropertyChanged("current_element_count")
        
        return self.current_elements
    
    @property
    def current_elements(self):
        """Get currently collected elements"""
        return self.__collector_service.get_spatial_elements(
            self.__current_doc_index, self.__current_cat_index
        )
    
    @property
    def current_element_count(self):
        """Get count of currently collected elements"""
        elements = self.current_elements
        return len(elements) if elements else 0
    
    def clear_cache(self):
        """Clear the cache in collector service"""
        self.__collector_service.clear_cache()
    
    def get_cache_info(self):
        """Get cache information for debugging"""
        cache_info = self.__collector_service.get_cache_info()
        return "CollectorService cache: {}, Current: doc={}, cat={}, count={}".format(
            cache_info, 
            self.__current_doc_index,
            self.__current_cat_index,
            self.current_element_count
        ) 