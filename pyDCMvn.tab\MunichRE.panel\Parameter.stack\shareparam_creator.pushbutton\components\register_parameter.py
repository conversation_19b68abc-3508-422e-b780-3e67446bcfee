# coding: utf-8
"""Register parameter object"""
from . import constant

class RegisterParameter:
    """Register parameter object"""
    def __init__(self, expando_object):
        """Create SharedParameter from ExpandoObject

        Args:
            expando_object (ExpandoObject): .Net ExpandoObject
        """
        # type : (object) -> SharedParameter
        self._object = expando_object

    @property
    def order(self):
        # type: () -> str
        """Numerical order of shared parameter"""
        return getattr(self._object, constant.SP_ITEM)

    @property
    def proposer(self):
        """Proposer of shared parameter"""
        return getattr(self._object, constant.SP_PROPOSER)

    @property
    def approver(self):
        """Approver of shared parameter"""
        return getattr(self._object, constant.SP_APPROVER)

    @property
    def guid(self):
        """Guid of shared parameter"""
        return getattr(self._object, constant.SP_GUID)

    @property
    def name(self):
        """Name of shared parameter"""
        return getattr(self._object, constant.SP_NAME)

    @property
    def group(self):
        """Group of shared parameter e.g: 20_HealthCheck, 03_Space, 05_Tag,..."""
        return getattr(self._object, constant.SP_GROUP)

    @property
    def discipline(self):
        """Discipline of shared parameter e.g: Common, HVAC, Piping,..."""
        return getattr(self._object, constant.SP_DISCIPLINE)

    @property
    def type_of_parameter(self):
        """Type of shared parameter e.g: Interger, Text, Length, Yes/No..."""
        return getattr(self._object, constant.SP_TYPE_OF_PARAMETER)

    @property
    def tooltip(self):
        """Tooltip of shared parameter"""
        return getattr(self._object, constant.SP_TOOLTIP)

    @property
    def group_under(self):
        """Group under of shared parameter e.g: IFC Parameters, Dimensions, Identity Data,..."""
        return getattr(self._object, constant.SP_GROUP_UNDER)

    @property
    def type(self):
        """Type of shared parameter e.g: Instance or Type parameter"""
        return getattr(self._object, constant.SP_TYPE)

    @property
    def project_prefix(self):
        """Project prefix of shared parameter e.g: SAN, SPR, RLT,..."""
        return getattr(self._object, constant.SP_PROJECT_PREFIX)

    @property
    def categories(self):
        """Categories that be applied this shared parameter, separate by ','"""
        return getattr(self._object, constant.SP_CATEGORIES)

    @property
    def families(self):
        """Families that be applied this shared parameter, separate by ','"""
        return getattr(self._object, constant.SP_FAMILIES)
    

