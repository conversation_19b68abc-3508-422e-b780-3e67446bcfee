# coding: utf-8
"""
Utils package for room number mapping functionality.

This package provides utility functions for collecting elements, geometry operations,
and other helper functions.
"""

from .collector_utils import (
    get_element_physical_filter,
    get_loaded_link_documents,
    get_physical_elements,
    get_spatial_categories,
    group_elements_by_category,
    to_builtin_parameter,
    get_filterable_parameters_items,
    get_applicable_parameters,
    get_ifc_space_type_filter
)

from .geometry_utils import (
    convert_mm_to_internal,
    create_line,
    create_circle, 
    create_rectangle,
    create_rectangle_from_curve,
    create_circle_around_point,
    sample_curve_points,
    get_element_center_point,
    get_closest_bbox_point,
    translate_point_by_z,
    boundingbox_to_solid,
    create_reference_intersector,
    check_ray_distance
)

__all__ = [
    'get_ifc_space_type_filter',
    'get_element_physical_filter',
    'get_loaded_link_documents',
    'get_physical_elements', 
    'get_spatial_categories',
    'get_closest_bbox_point',
    'group_elements_by_category',
    'to_builtin_parameter',
    'get_filterable_parameters_items',
    'get_applicable_parameters',
    'convert_mm_to_internal',
    'create_line',
    'create_circle', 
    'create_rectangle',
    'create_rectangle_from_curve',
    'create_circle_around_point',
    'sample_curve_points',
    'get_element_center_point',
    'translate_point_by_z',
    'boundingbox_to_solid',
    'create_reference_intersector',
    'check_ray_distance'
] 