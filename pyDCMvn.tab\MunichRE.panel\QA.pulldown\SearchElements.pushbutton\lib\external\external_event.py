from DCMvn.core import UI, RevitExceptions

class _ExternalHandler(UI.IExternalEventHandler):
    """ Implement of IExternalEventHandler intended to be used in External Event"""

    def __init__(self, function_or_method, *args, **kwargs): # noqa
        self.function_or_method = function_or_method
        self.args = args
        self.kwargs = kwargs

    # Execute method run in Revit API environment.
    def Execute(self, application):
        try:
            self.function_or_method(*self.args, **self.kwargs)
        except RevitExceptions.InvalidOperationException:
            print("The operation is invalid.")

    def GetName(self):
        return "Execute an function or method in a IExternalHandler"


class ExternalEventSolver:
    """ Class to create and raise an External Event"""

    def __init__(self, function_or_method, *args, **kwargs):
        self.__handler = _ExternalHandler(function_or_method, *args, **kwargs)
        self.__event = UI.ExternalEvent.Create(self.__handler)

    def set_args(self, *args, **kwargs):
        self.__handler.args = args
        self.__handler.kwargs = kwargs

    def raise_event(self):
        self.__event.Raise()