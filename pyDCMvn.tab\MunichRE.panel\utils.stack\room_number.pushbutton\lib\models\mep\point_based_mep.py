# coding: utf-8
from DCMvn.core import DB
from .base_mep import BaseMep


class PointBasedMep(BaseMep):
    """Wrapper for point-based MEP elements (Family Instances, Equipment, Fixtures).

    This class provides specialized functionality for MEP elements that have
    location points, with optimized room calculation point handling for spatial mapping.
    """

    def __init__(self, mep_element):
        # type: (DB.Element) -> None
        """Initialize a point-based MEP element wrapper.

        Args:
            mep_element (DB.Element): The Revit MEP element with location point
        """
        super(PointBasedMep, self).__init__(mep_element)
        self._location_point = None  # type: DB.XYZ | None
        self._family_symbol = None  # type: DB.FamilySymbol | None
        self._is_door_or_window = None  # type: bool | None

    @property
    def location_point(self):
        # type: () -> DB.XYZ | None
        """Get the location point of the MEP element.

        Returns:
            DB.XYZ: The location point or None if not available
        """
        if self._location_point is None:
            try:
                location = self.location
                if isinstance(location, DB.LocationPoint):
                    self._location_point = location.Point
            except Exception:
                pass
        return self._location_point
        
    @property 
    def family_symbol(self):
        # type: () -> DB.FamilySymbol | None
        """Get the family symbol of the family instance.

        Returns:
            DB.FamilySymbol: The family symbol or None if not available
        """
        if self._family_symbol is None:
            try:
                if isinstance(self._element, DB.FamilyInstance):
                    self._family_symbol = self._element.Symbol
            except Exception:
                pass
        return self._family_symbol

    def get_family_name(self):
        # type: () -> str
        """Get the family name of the family instance.

        Returns:
            str: The family name or empty string if not available
        """
        try:
            symbol = self.family_symbol
            if symbol:
                return symbol.Family.Name
        except Exception:
            pass
        return ""

    def get_type_name(self):
        # type: () -> str
        """Get the type name of the family instance.

        Returns:
            str: The type name or empty string if not available
        """
        try:
            symbol = self.family_symbol
            if symbol:
                return symbol.Name
        except Exception:
            pass
        return ""

    def __str__(self):
        # type: () -> str
        """String representation of the point-based MEP element.

        Returns:
            str: String representation
        """
        family_name = self.get_family_name()
        type_name = self.get_type_name()

        return "PointBasedMEP (Id={}, Family={}, Type={}, Category={})".format(
            self._get_elementid_value(self.id),
            family_name,
            type_name,
            self.get_category_name(),
        )

    def __repr__(self):
        # type: () -> str
        """Detailed string representation of the point-based MEP element.

        Returns:
            str: Detailed string representation
        """
        family_name = self.get_family_name()
        type_name = self.get_type_name()

        return "PointBasedMEP (Id={}, GUID={}, Family={}, Type={}, Category={})".format(
            self._get_elementid_value(self.id),
            self.guid,
            family_name,
            type_name,
            self.get_category_name(),
        )
