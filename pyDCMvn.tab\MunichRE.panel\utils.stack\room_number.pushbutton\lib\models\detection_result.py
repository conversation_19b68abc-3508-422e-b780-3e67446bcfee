# coding: utf-8
from .mep.base_mep import BaseMep  # noqa: F401
from .spatial.base_spatial import BaseSpatial  # noqa: F401
from .parameter_pair import ParameterPair  # noqa: F401
from pyrevit.compat import get_elementid_value_func


class DetectionResult(object):
    """Stores the result of MEP element spatial detection.
    
    Supports multiple spatial elements mapped to one MEP element when 
    allow_multiple_values is enabled in advanced options.
    """
    
    def __init__(self, mep_element, spatial_elements=None, parameter_pair=None, detection_method=None):
        # type: (BaseMep, list[BaseSpatial], ParameterPair, str) -> None
        self.mep_element = mep_element
        self.spatial_elements = spatial_elements or []
        self.parameter_pair = parameter_pair
        self.detection_method = detection_method or "unknown"
        
    @property
    def has_spatial_match(self):
        # type: () -> bool
        """Check if any spatial element was found."""
        return len(self.spatial_elements) > 0
        
    @property
    def has_multiple_matches(self):
        # type: () -> bool
        """Check if multiple spatial elements were found."""
        return len(self.spatial_elements) > 1
        
    @property
    def spatial_count(self):
        # type: () -> int
        """Get the number of spatial elements matched."""
        return len(self.spatial_elements)
        
    def add_spatial_element(self, spatial_element):
        # type: (BaseSpatial) -> None
        """Add a spatial element to the detection result."""
        if spatial_element and spatial_element not in self.spatial_elements:
            self.spatial_elements.append(spatial_element)
            
    def get_spatial_values(self, separator="/"):
        # type: (str) -> str
        """Get parameter values from all spatial elements.
        
        Args:
            separator (str): Separator for multiple values
            
        Returns:
            str: Combined values separated by separator, or empty string if no matches
        """
        if not self.has_spatial_match or not self.parameter_pair:
            return ""
            
        values = []
        for spatial_element in self.spatial_elements:
            value = self.parameter_pair.get_source_value(spatial_element.element)
            if value is not None:
                # Convert to string for consistent handling
                str_value = str(value).strip()
                if str_value and str_value not in values:
                    values.append(str_value)
                    
        return separator.join(values) if values else ""
        
    def get_spatial_value(self):
        # type: () -> object
        """Get parameter value from the first spatial element.
        
        Returns:
            object: Parameter value or None if no matches
        """
        if not self.has_spatial_match or not self.parameter_pair:
            return None
        return self.parameter_pair.get_source_value(self.spatial_elements[0].element)
        
    def apply_to_mep(self, separator="/", allow_multiple=False, override_existing=True):
        # type: (str, bool, bool) -> bool
        """Apply spatial values to MEP element parameter.
        
        Args:
            separator (str): Separator for multiple values
            allow_multiple (bool): Whether to allow multiple values
            override_existing (bool): Whether to override existing parameter values
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.has_spatial_match or not self.parameter_pair:
            return False
            
        if allow_multiple and self.has_multiple_matches and self._can_target_parameter_handle_multiple_values():
            combined_value = self.get_spatial_values(separator)
            if combined_value:
                return self.parameter_pair.set_target_value(self.mep_element.element, combined_value, override_existing)
        
        # Use single value from first spatial element
        single_value = self.get_spatial_value()
        if single_value is not None:
            return self.parameter_pair.set_target_value(self.mep_element.element, single_value, override_existing)
            
        return False
        
    def _can_target_parameter_handle_multiple_values(self):
        # type: () -> bool
        """Check if target parameter can handle multiple values (Text storage type only)."""
        if not self.parameter_pair:
            return False
            
        target_wrapper = self.parameter_pair.target_param_wrapper
        if not target_wrapper:
            return False
            
        try:
            param = target_wrapper.get_parameter_from_element(self.mep_element.element)
            if param:
                from DCMvn.core import DB
                return param.StorageType == DB.StorageType.String
        except: # noqa
            pass
        return False

    def __eq__(self, other):
        # type: (object) -> bool
        """Check equality based on MEP element ID."""
        if not isinstance(other, DetectionResult):
            return False
        return self.mep_element.id == other.mep_element.id

    def __hash__(self):
        return hash(self.mep_element.id)
        
    def __str__(self):
        # type: () -> str
        """String representation of the detection result."""
        get_elementid_value = get_elementid_value_func()
        mep_id = get_elementid_value(self.mep_element.id)
        
        if self.has_spatial_match:
            spatial_ids = [get_elementid_value(spatial.id) for spatial in self.spatial_elements]
            return "DetectionResult(MEP:{} -> Spatial:{} [{}])".format(
                mep_id, spatial_ids, self.detection_method)
        else:
            return "DetectionResult(MEP:{} -> No Match [{}])".format(
                mep_id, self.detection_method) 