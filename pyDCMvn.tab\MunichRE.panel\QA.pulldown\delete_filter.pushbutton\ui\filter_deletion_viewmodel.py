# coding: utf-8
from DCMvn.forms.mvvm import ViewModelBase, RelayCommand
from DCMvn.core.framework import Observable<PERSON>ollection, Debug
from DCMvn.core import DB
from DCMvn.forms import alert


class FilterItemViewModel(ViewModelBase):
    """ViewModel cho từng filter item trong danh sách"""
    
    def __init__(self, filter_element, filter_name):
        ViewModelBase.__init__(self)
        self._filter_element = filter_element
        self._filter_name = filter_name
        self._is_selected = False
        self._filter_type = self._get_filter_type()
        
    @property
    def filter_element(self):
        return self._filter_element
    
    @property
    def filter_name(self):
        return self._filter_name
    
    @property
    def filter_type(self):
        return self._filter_type
        
    @property
    def is_selected(self):
        return self._is_selected
        
    @is_selected.setter
    def is_selected(self, value):
        if value != self._is_selected:
            self._is_selected = value
            self.RaisePropertyChanged("is_selected")
    
    def _get_filter_type(self):
        """Xác định loại filter"""
        try:
            if hasattr(self._filter_element, 'GetFilterRules'):
                return "View Filter"
            elif hasattr(self._filter_element, 'GetElementFilter'):
                return "Selection Filter" 
            else:
                return "Unknown Filter"
        except:
            return "Filter"


class FilterDeletionViewModel(ViewModelBase):
    """ViewModel chính cho công cụ xóa filter"""
    
    def __init__(self, document):
        ViewModelBase.__init__(self)
        self._document = document
        self._filter_items = ObservableCollection[FilterItemViewModel]()
        self._is_loading = False
        self._total_count = 0
        self._selected_count = 0
        
        # Commands
        self.load_filters_command = RelayCommand(self._load_filters)
        self.select_all_command = RelayCommand(self._select_all)
        self.deselect_all_command = RelayCommand(self._deselect_all)
        self.delete_selected_command = RelayCommand(self._delete_selected, self._can_delete)
        
        # Tự động load filters khi khởi tạo
        self._load_filters()
    
    @property
    def filter_items(self):
        return self._filter_items
    
    @property
    def is_loading(self):
        return self._is_loading
        
    @is_loading.setter
    def is_loading(self, value):
        if value != self._is_loading:
            self._is_loading = value
            self.RaisePropertyChanged("is_loading")
    
    @property
    def total_count(self):
        return self._total_count
        
    @total_count.setter
    def total_count(self, value):
        if value != self._total_count:
            self._total_count = value
            self.RaisePropertyChanged("total_count")
    
    @property
    def selected_count(self):
        return self._selected_count
        
    @selected_count.setter
    def selected_count(self, value):
        if value != self._selected_count:
            self._selected_count = value
            self.RaisePropertyChanged("selected_count")
    
    def _load_filters(self, _=None):
        """Load tất cả filters trong document"""
        try:
            self.is_loading = True
            self._filter_items.Clear()
            
            Debug.WriteLine("Loading filters from document...")
            
            # Thu thập View Filters
            view_filters = list(DB.FilteredElementCollector(self._document)
                               .OfClass(DB.ParameterFilterElement))
            
            # Thu thập Selection Sets (có thể chứa filters)
            selection_filters = list(DB.FilteredElementCollector(self._document)
                                   .OfClass(DB.SelectionFilterElement))
            
            Debug.WriteLine("Found {} view filters and {} selection filters".format(
                len(view_filters), len(selection_filters)))
            
            # Thêm view filters
            for filter_elem in view_filters:
                try:
                    filter_name = filter_elem.Name
                    if filter_name:
                        filter_item = FilterItemViewModel(filter_elem, filter_name)
                        # Subscribe to selection changes
                        try:
                            filter_item.PropertyChanged += self._on_filter_selection_changed
                        except:
                            pass  # PropertyChanged subscription might not work in IronPython
                        self._filter_items.Add(filter_item)
                except Exception as e:
                    Debug.WriteLine("Error processing view filter: {}".format(str(e)))
                    continue
            
            # Thêm selection filters
            for filter_elem in selection_filters:
                try:
                    filter_name = filter_elem.Name
                    if filter_name:
                        filter_item = FilterItemViewModel(filter_elem, filter_name)
                        try:
                            filter_item.PropertyChanged += self._on_filter_selection_changed
                        except:
                            pass  # PropertyChanged subscription might not work in IronPython
                        self._filter_items.Add(filter_item)
                except Exception as e:
                    Debug.WriteLine("Error processing selection filter: {}".format(str(e)))
                    continue
            
            self.total_count = len(self._filter_items)
            self._update_selected_count()
            
            Debug.WriteLine("Loaded {} total filters".format(self.total_count))
            
        except Exception as e:
            Debug.WriteLine("Error loading filters: {}".format(str(e)))
            alert("Lỗi khi tải danh sách filter: {}".format(str(e)))
        finally:
            self.is_loading = False
    
    def _on_filter_selection_changed(self, sender, e):
        """Xử lý khi có filter được chọn/bỏ chọn"""
        try:
            if hasattr(e, 'PropertyName') and e.PropertyName == "is_selected":
                self._update_selected_count()
        except:
            self._update_selected_count()
    
    def _update_selected_count(self):
        """Cập nhật số lượng filter được chọn"""
        try:
            count = 0
            for item in self._filter_items:
                if item.is_selected:
                    count += 1
            self.selected_count = count
        except Exception as e:
            Debug.WriteLine("Error updating selected count: {}".format(str(e)))
            self.selected_count = 0
    
    def _select_all(self, _=None):
        """Chọn tất cả filters"""
        try:
            for item in self._filter_items:
                item.is_selected = True
            self._update_selected_count()
        except Exception as e:
            Debug.WriteLine("Error selecting all: {}".format(str(e)))
    
    def _deselect_all(self, _=None):
        """Bỏ chọn tất cả filters"""
        try:
            for item in self._filter_items:
                item.is_selected = False
            self._update_selected_count()
        except Exception as e:
            Debug.WriteLine("Error deselecting all: {}".format(str(e)))
    
    def _can_delete(self, _=None):
        """Kiểm tra có thể xóa hay không"""
        return self.selected_count > 0 and not self.is_loading
    
    def _delete_selected(self, _=None):
        """Xóa các filters được chọn"""
        try:
            selected_items = []
            for item in self._filter_items:
                if item.is_selected:
                    selected_items.append(item)
            
            if not selected_items:
                alert("Không có filter nào được chọn để xóa.")
                return
            
            # Xác nhận với người dùng
            confirm_msg = "Bạn có chắc chắn muốn xóa {} filter(s) đã chọn?\n\nDanh sách filters sẽ bị xóa:\n".format(len(selected_items))
            for item in selected_items[:10]:  # Hiển thị tối đa 10 items
                confirm_msg += "- {}\n".format(item.filter_name)
            if len(selected_items) > 10:
                confirm_msg += "... và {} filters khác".format(len(selected_items) - 10)
            
            if not alert(confirm_msg, yes=True, no=True):
                return
            
            # Thực hiện xóa trong transaction
            with DB.Transaction(self._document, "Delete Selected Filters") as trans:
                trans.Start()
                
                deleted_count = 0
                failed_count = 0
                failed_filters = []
                
                for item in selected_items:
                    try:
                        if item.filter_element and item.filter_element.IsValidObject:
                            self._document.Delete(item.filter_element.Id)
                            deleted_count += 1
                            Debug.WriteLine("Deleted filter: {}".format(item.filter_name))
                        else:
                            failed_count += 1
                            failed_filters.append(item.filter_name)
                    except Exception as e:
                        failed_count += 1
                        failed_filters.append(item.filter_name)
                        Debug.WriteLine("Failed to delete filter {}: {}".format(item.filter_name, str(e)))
                
                trans.Commit()
                
                # Hiển thị kết quả
                result_msg = "Kết quả xóa filter:\n"
                result_msg += "- Đã xóa thành công: {} filters\n".format(deleted_count)
                if failed_count > 0:
                    result_msg += "- Không thể xóa: {} filters\n".format(failed_count)
                    if failed_filters:
                        result_msg += "Filters không thể xóa: {}".format(", ".join(failed_filters[:5]))
                        if len(failed_filters) > 5:
                            result_msg += " và {} filters khác".format(len(failed_filters) - 5)
                
                alert(result_msg)
                
                # Reload danh sách filters
                self._load_filters()
        
        except Exception as e:
            Debug.WriteLine("Error deleting filters: {}".format(str(e)))
            alert("Lỗi khi xóa filters: {}".format(str(e)))
