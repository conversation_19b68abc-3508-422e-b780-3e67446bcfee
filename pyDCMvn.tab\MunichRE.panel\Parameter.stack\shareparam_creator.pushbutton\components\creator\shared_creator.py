# coding: utf-8
from DCMvn.core import ApplicationServices, DB  # noqa
from pyrevit.output import PyRevitOutputWindow  # noqa
from .. validator.shared_param_validator import SharedParamValidator, get_all_unit_types  # noqa

class SharedCreator:
    def __init__(self, app):
        # type: (ApplicationServices.Application) -> SharedCreator
        self.__app = app # type: ApplicationServices.Application

    def create_shared(self, shared_param_validators, output_window):
        # type: (list[SharedParamValidator], PyRevitOutputWindow) -> None
        """Create shared parameter

        Args:
            shared_param_validator (RegisterParameter): register parameter object
        """

        sp_file = self.__app.OpenSharedParameterFile()
        all_unit_types = get_all_unit_types()
        
        for sp_validator in shared_param_validators:
            group_name = sp_validator.register_param.group
            para_name = sp_validator.register_param.name
            type_of_parameter = sp_validator.register_param.type_of_parameter
            
            group = sp_file.Groups.get_Item(group_name)
            if group is None:
                group = sp_file.Groups.Create(group_name)

            def_ = group.Definitions.get_Item(para_name)
            if def_ is None:
                opt = DB.ExternalDefinitionCreationOptions(para_name, all_unit_types.get(type_of_parameter))
                opt.Description = sp_validator.register_param.tooltip
                group.Definitions.Create(opt)
                
                para_name_report = "<strong style=\"color: #f37326\">{}</strong>".format(para_name)
                group_name_report = "<strong style=\"color: #0973ba\">{}</strong>".format(group_name)
                output_window.print_html(":white_heavy_check_mark: Create paramter: {} in group: {}".format(para_name_report, group_name_report))

                
