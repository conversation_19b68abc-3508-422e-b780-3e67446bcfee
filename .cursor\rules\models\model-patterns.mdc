---
description: Model patterns, element wrappers, and data structures for DCMvn architecture
globs: ["**/models/**/*.py"]
alwaysApply: true
---

# Model and Wrapper Patterns

## Model Architecture
- **Domain Models**: Core business entities (BaseMep, RevitSpatial, DocumentWrapper)
- **Data Transfer Objects**: Simple containers (ParameterPair, DetectionResult, TargetCategory)
- **Configuration Objects**: Settings and options (AdvancedOptionsViewModel)
- **UI Binding Models**: Data specifically formatted for UI consumption

## Element Wrapper Implementation
- **Purpose**: Add functionality to Revit elements without modification
- **Coordinate Transformation**: Handle linked document transformations
- **Property Caching**: Cache expensive calculations (geometry, parameters)
- **Error Resilience**: Graceful handling of invalid elements
- **Validation**: Always check `element.IsValidObject` before operations

## DocumentWrapper Pattern
```python
class DocumentWrapper(object):
    """Handles document and transformation context."""
    def __init__(self, document, link_instance=None):
        self.document = document
        self.link_instance = link_instance
        self._transform = None
        
    @property
    def transform(self):
        if self._transform is None:
            if self.link_instance:
                self._transform = self.link_instance.GetTotalTransform()
            else:
                self._transform = DB.Transform.Identity
        return self._transform
```

## Property and Caching Patterns
- Use `@property` for computed values
- Implement lazy loading for expensive operations
- Cache geometry calculations in instance variables
- Provide fallback values for missing properties
- Use type hints in comments for IronPython compatibility

## Validation and Error Handling
- Validate constructor parameters
- Check element validity before operations
- Implement graceful degradation for missing data
- Log errors using `Debug.WriteLine` without propagating

[element-wrapper-template.py](mdc:element-wrapper-template.py)
[document-wrapper-template.py](mdc:document-wrapper-template.py)