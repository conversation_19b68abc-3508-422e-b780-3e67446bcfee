# coding: utf-8
"""
Validation workflow orchestrator for TabooZone QA
"""
from DCMvn.core.framework import Debug
from DCMvn.core import HOST_APP, get_output, DB  # noqa: F401

from .models import ValidationResult, MatchedPair, GuidPair
from .centroid_validator import CentroidValidator
from .guid_map import GuidMultiMap
from .guid_validator import GuidValidator
from . import constants
from ..reporting import HtmlReporter, ExcelReporter  # noqa: F401


class ValidationWorkflow(object):
    def __init__(self):
        self.centroid_validator = CentroidValidator()
        self.guid_validator = GuidValidator()

    def _build_many_to_many(self, arc_elements, mass_elements, arc_guid_param, mass_guid_param):
        # type: (list[DB.Element], list[DB.Element], DB.BuiltInParameter | DB.Definition | str, DB.BuiltInParameter | DB.Definition | str) -> GuidMultiMap
        """Build many-to-many DTO sets using GuidMultiMap and GuidValidator.get_element_guid."""
        gmm = GuidMultiMap()
        try:
            for e in arc_elements or []:
                guid = self.guid_validator.get_element_guid(e, arc_guid_param)
                if guid:
                    gmm.add_arc(guid, e)
            for e in mass_elements or []:
                guid = self.guid_validator.get_element_guid(e, mass_guid_param)
                if guid:
                    gmm.add_mass(guid, e)
        except Exception as ex:
            Debug.WriteLine("_build_many_to_many error: {}".format(str(ex)))
        return gmm

    def execute_validation(self,
                           arc_elements,
                           mass_elements,
                           arc_name=constants.DEFAULT_ARC_NAME,
                           mass_name=constants.DEFAULT_MASS_NAME,
                           tolerance_mm=0.0,
                           arc_guid_param=None,
                           mass_guid_param=None,
                           excel_reporter=None,
                           html_reporter=None,
                           rule_name=None,
                           extra_columns=None):
        # type: (list[DB.Element], list[DB.Element], str, str, float, DB.BuiltInParameter | DB.Definition | str, DB.BuiltInParameter | DB.Definition | str, ExcelReporter, HtmlReporter, str, dict[str, str]) -> ValidationResult
        """Run GUID validation, optional centroid validation, and optional export.
        Returns ValidationResult DTO.
        """
        result = ValidationResult(arc_name, mass_name, tolerance_mm)

        # Base statistics
        result.total_arc_elements = len(arc_elements or [])
        result.total_mass_elements = len(mass_elements or [])

        # 1b) Build many-to-many mapping and convert to DTOs
        gmm = self._build_many_to_many(arc_elements, mass_elements, arc_guid_param, mass_guid_param)

        # Matched: all combinations for the same GUID
        for guid, arc_elem, mass_elem in [(g, a, m) for g, a, m in gmm.matched_pairs()]:
            result.matched_pairs.append(MatchedPair(guid, arc_elem, mass_elem))

        # Missing in Mass: every ARC element for GUIDs not present in Mass
        for guid in gmm.missing_arc():
            for arc_elem in gmm.arc.get(guid, []) or []:
                result.missing_pairs.append(GuidPair(guid, arc_elem))

        # Extra in Mass: every Mass element for GUIDs not present in ARC
        for guid in gmm.extra_mass():
            for mass_elem in gmm.mass.get(guid, []) or []:
                result.extra_pairs.append(GuidPair(guid, mass_elem))

        # Duplicates
        for guid, elements in gmm.duplicates_arc().items():
            for e in elements:
                result.duplicate_arc_pairs.append(GuidPair(guid, e))
        for guid, elements in gmm.duplicates_mass().items():
            for e in elements:
                result.duplicate_mass_pairs.append(GuidPair(guid, e))

        # 2) Centroid validation if tolerance > 0
        if tolerance_mm and tolerance_mm > 0 and len(result.matched_pairs) > 0:
            centroid_pairs = self.centroid_validator.validate_centroid_distance(result.matched_pairs, tolerance_mm)
            result.centroid_pairs = centroid_pairs

        # 3) Optional Excel export via reporter
        if excel_reporter is not None:
            try:
                excel_reporter.export_validation_result(result, rule_name=rule_name, concluded_parameter_dependency=extra_columns)
            except Exception as ex:
                Debug.WriteLine("Excel export failed: {}".format(str(ex)))

        # 4) Optional HTML export via reporter
        if html_reporter is not None:
            try:
                output = get_output()
                html_reporter.export_validation_result(result, output, HOST_APP.doc, rule_name=rule_name)
            except Exception as ex:
                Debug.WriteLine("HTML export failed: {}".format(str(ex)))

        return result

