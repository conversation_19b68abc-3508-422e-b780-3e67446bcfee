<mah:MetroWindow
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Room Number Mapping Tool"
    Width="550"
    MinWidth="500"
    MinHeight="600"
    ResizeMode="CanResizeWithGrip"
    ShowMaxRestoreButton="False"
    ShowMinButton="False"
    SizeToContent="Height"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">

    <mah:MetroWindow.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
    </mah:MetroWindow.Resources>

    <mah:MetroWindow.LeftWindowCommands>
        <mah:WindowCommands ShowSeparators="True">
            <Button
                Command="{Binding ShowAdvancedOptionsCommand}"
                Content=""
                FontFamily="Segoe Fluent Icons"
                FontSize="20"
                ToolTip="Open advanced options" />
        </mah:WindowCommands>
    </mah:MetroWindow.LeftWindowCommands>

    <mah:MetroWindow.Flyouts>
        <mah:FlyoutsControl>
            <mah:Flyout
                x:Name="ModalFlyout"
                Width="370"
                Header="Advanced Options"
                IsModal="True"
                IsOpen="{Binding is_advanced_options_flyout_open}"
                Position="Right">
                <StackPanel x:Name="SpatialRelationshipOptionsStackPanel" Margin="10,5,0,0">
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <TextBlock
                            Grid.Column="0"
                            VerticalAlignment="Center"
                            FontWeight="SemiBold"
                            Foreground="#2E74B5"
                            Text="Spatial Relationship Options:" />
                        <Button
                            x:Name="SpatialInfoButton"
                            Grid.Column="1"
                            Margin="0,0,6,0"
                            Background="Transparent"
                            BorderBrush="Transparent"
                            Content="ℹ️"
                            Foreground="{DynamicResource MahApps.Brushes.ThemeForeground}"
                            Style="{DynamicResource MahApps.Styles.Button.Circle}"
                            ToolTip="Preview how Above and Proximity checks work" />
                        <Popup
                            x:Name="DetectionPreviewPopup"
                            AllowsTransparency="True"
                            Placement="Right"
                            PlacementTarget="{Binding ElementName=SpatialInfoButton}"
                            StaysOpen="False">
                            <Border
                                MaxWidth="600"
                                Padding="10"
                                Background="{DynamicResource MahApps.Brushes.ThemeBackground}"
                                BorderBrush="{DynamicResource MahApps.Brushes.Separator}"
                                BorderThickness="1"
                                CornerRadius="5">
                                <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
                                    <StackPanel>
                                        <!--  Above Check Group  -->
                                        <GroupBox
                                            Margin="0,0,0,15"
                                            FontWeight="SemiBold"
                                            Foreground="#2E74B5"
                                            Header="Above Check">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="*" />
                                                </Grid.ColumnDefinitions>

                                                <!--  Point Above Check  -->
                                                <DockPanel Grid.Column="0" Margin="0,0,15,0">
                                                    <Border
                                                        BorderBrush="{DynamicResource MahApps.Brushes.Separator}"
                                                        BorderThickness="1"
                                                        CornerRadius="2"
                                                        DockPanel.Dock="Top">
                                                        <Canvas
                                                            x:Name="AbovePointRevitCanvas"
                                                            Width="240"
                                                            Height="140"
                                                            Background="{DynamicResource MahApps.Brushes.ThemeBackground}" />
                                                    </Border>
                                                    <TextBlock
                                                        Margin="0,8,0,0"
                                                        TextAlignment="Center"
                                                        Style="{DynamicResource MahApps.Styles.TextBlock.Caption}"
                                                        Text="Translates point by above distance along -Z and tests containment"
                                                        TextWrapping="Wrap" />
                                                </DockPanel>

                                                <!--  Curve Above Check  -->
                                                <DockPanel Grid.Column="1" Margin="15,0,0,0">
                                                    <Border
                                                        BorderBrush="{DynamicResource MahApps.Brushes.Separator}"
                                                        BorderThickness="1"
                                                        CornerRadius="2"
                                                        DockPanel.Dock="Top">
                                                        <Canvas
                                                            x:Name="AboveCurveRevitCanvas"
                                                            Width="240"
                                                            Height="140"
                                                            Background="{DynamicResource MahApps.Brushes.ThemeBackground}" />
                                                    </Border>
                                                    <TextBlock
                                                        Margin="0,8,0,0"
                                                        TextAlignment="Center"
                                                        Style="{DynamicResource MahApps.Styles.TextBlock.Caption}"
                                                        Text="Translates start/mid/end points by above distance"
                                                        TextWrapping="Wrap" />
                                                </DockPanel>
                                            </Grid>
                                        </GroupBox>

                                        <!--  Proximity Revit Group  -->
                                        <GroupBox
                                            Margin="0,0,0,15"
                                            FontWeight="SemiBold"
                                            Foreground="#28A745"
                                            Header="Proximity Check - Revit Spatial (Rooms/Spaces)">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="*" />
                                                </Grid.ColumnDefinitions>

                                                <!--  Point Proximity Revit  -->
                                                <DockPanel Grid.Column="0" Margin="0,0,15,0">
                                                    <Border
                                                        BorderBrush="{DynamicResource MahApps.Brushes.Separator}"
                                                        BorderThickness="1"
                                                        CornerRadius="2"
                                                        DockPanel.Dock="Top">
                                                        <Canvas
                                                            x:Name="ProximityPointRevitCanvas"
                                                            Width="240"
                                                            Height="140"
                                                            Background="{DynamicResource MahApps.Brushes.ThemeBackground}" />
                                                    </Border>
                                                    <TextBlock
                                                        Margin="0,8,0,0"
                                                        TextAlignment="Center"
                                                        Style="{DynamicResource MahApps.Styles.TextBlock.Caption}"
                                                        Text="Circle around point with nearest distance"
                                                        TextWrapping="Wrap" />
                                                </DockPanel>

                                                <!--  Curve Proximity Revit  -->
                                                <DockPanel Grid.Column="1" Margin="15,0,0,0">
                                                    <Border
                                                        BorderBrush="{DynamicResource MahApps.Brushes.Separator}"
                                                        BorderThickness="1"
                                                        CornerRadius="2"
                                                        DockPanel.Dock="Top">
                                                        <Canvas
                                                            x:Name="ProximityCurveRevitCanvas"
                                                            Width="240"
                                                            Height="140"
                                                            Background="{DynamicResource MahApps.Brushes.ThemeBackground}" />
                                                    </Border>
                                                    <TextBlock
                                                        Margin="0,8,0,0"
                                                        TextAlignment="Center"
                                                        Style="{DynamicResource MahApps.Styles.TextBlock.Caption}"
                                                        Text="Rectangle around curve with nearest distance"
                                                        TextWrapping="Wrap" />
                                                </DockPanel>
                                            </Grid>
                                        </GroupBox>

                                        <!--  Proximity Generic Group  -->
                                        <GroupBox
                                            FontWeight="SemiBold"
                                            Foreground="#E91E63"
                                            Header="Proximity Check - Generic Spatial (IFC DirectShape)">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*" />
                                                    <ColumnDefinition Width="*" />
                                                </Grid.ColumnDefinitions>

                                                <!--  Point Proximity Generic  -->
                                                <DockPanel Grid.Column="0" Margin="0,0,15,0">
                                                    <Border
                                                        BorderBrush="{DynamicResource MahApps.Brushes.Separator}"
                                                        BorderThickness="1"
                                                        CornerRadius="2"
                                                        DockPanel.Dock="Top">
                                                        <Canvas
                                                            x:Name="ProximityPointGenericCanvas"
                                                            Width="240"
                                                            Height="140"
                                                            Background="{DynamicResource MahApps.Brushes.ThemeBackground}" />
                                                    </Border>
                                                    <TextBlock
                                                        Margin="0,8,0,0"
                                                        TextAlignment="Center"
                                                        Style="{DynamicResource MahApps.Styles.TextBlock.Caption}"
                                                        Text="Ray casting from point to spatial center"
                                                        TextWrapping="Wrap" />
                                                </DockPanel>

                                                <!--  Curve Proximity Generic  -->
                                                <DockPanel Grid.Column="1" Margin="15,0,0,0">
                                                    <Border
                                                        BorderBrush="{DynamicResource MahApps.Brushes.Separator}"
                                                        BorderThickness="1"
                                                        CornerRadius="2"
                                                        DockPanel.Dock="Top">
                                                        <Canvas
                                                            x:Name="ProximityCurveGenericCanvas"
                                                            Width="240"
                                                            Height="140"
                                                            Background="{DynamicResource MahApps.Brushes.ThemeBackground}" />
                                                    </Border>
                                                    <TextBlock
                                                        Margin="0,8,0,0"
                                                        TextAlignment="Center"
                                                        Style="{DynamicResource MahApps.Styles.TextBlock.Caption}"
                                                        Text="Ray casting from curve sample points"
                                                        TextWrapping="Wrap" />
                                                </DockPanel>
                                            </Grid>
                                        </GroupBox>
                                    </StackPanel>
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Grid>

                    <CheckBox
                        x:Name="MapElementsAboveSpacesCheckBox"
                        Margin="10,0,10,8"
                        Content="Map elements located above spatial elements"
                        IsChecked="{Binding advanced_options.map_elements_above_spaces}"
                        ToolTip="Include elements that are positioned above spatial elements within the boundaries" />

                    <mah:NumericUpDown
                        x:Name="AboveAllowedDistanceNumericUpDown"
                        Margin="10,0,8,10"
                        mah:TextBoxHelper.UseFloatingWatermark="True"
                        mah:TextBoxHelper.Watermark="Above allowed Distance (mm)"
                        Maximum="10000"
                        Minimum="1"
                        Visibility="{Binding advanced_options.map_elements_above_spaces, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Value="{Binding advanced_options.above_allowed_distance, UpdateSourceTrigger=PropertyChanged, FallbackValue=1000}" />

                    <CheckBox
                        x:Name="UseProximityMappingCheckBox"
                        Margin="10,0,0,10"
                        Content="Map elements located not above spatial elements"
                        IsChecked="{Binding advanced_options.use_proximity_mapping}"
                        ToolTip="Map elements to the nearest spatial element when they are not contained within any boundaries" />

                    <mah:NumericUpDown
                        x:Name="NearestAllowedDistanceNumericUpDown"
                        Margin="10,0,8,10"
                        mah:TextBoxHelper.UseFloatingWatermark="True"
                        mah:TextBoxHelper.Watermark="Nearest allowed Distance (mm)"
                        Maximum="10000"
                        Minimum="1"
                        Visibility="{Binding advanced_options.use_proximity_mapping, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Value="{Binding advanced_options.nearest_allowed_distance, UpdateSourceTrigger=PropertyChanged}" />

                    <CheckBox
                        x:Name="UseDefaultValueWhenNotFoundCheckBox"
                        Margin="10,0,0,8"
                        Content="Use default value when spatial element not found"
                        IsChecked="{Binding advanced_options.use_default_value_when_not_found}"
                        ToolTip="Assign a default value to elements that cannot be mapped to any spatial element" />

                    <TextBox
                        x:Name="DefaultValueWhenNotFoundTextBox"
                        Margin="10,0,8,15"
                        mah:TextBoxHelper.UseFloatingWatermark="True"
                        mah:TextBoxHelper.Watermark="Default value when not found"
                        Text="{Binding advanced_options.default_value_when_not_found, UpdateSourceTrigger=PropertyChanged}"
                        Visibility="{Binding advanced_options.use_default_value_when_not_found, Converter={StaticResource BooleanToVisibilityConverter}}" />

                    <CheckBox
                        x:Name="AllowMultipleValuesCheckBox"
                        Margin="10,0,0,8"
                        Content="Allow multiple values for elements in multiple spatial"
                        IsChecked="{Binding advanced_options.allow_multiple_values}"
                        ToolTip="Combine values when an element is positioned in multiple spatial elements" />

                    <TextBox
                        x:Name="SeparatorValueTextBox"
                        Margin="10,0,8,5"
                        mah:TextBoxHelper.UseFloatingWatermark="True"
                        mah:TextBoxHelper.Watermark="Separator value (e.g., ' / ' or ' | ', etc.)"
                        Text="{Binding advanced_options.separator_value, UpdateSourceTrigger=PropertyChanged}"
                        Visibility="{Binding advanced_options.allow_multiple_values, Converter={StaticResource BooleanToVisibilityConverter}}" />

                    <TextBlock
                        Margin="10,0,8,15"
                        FontStyle="Italic"
                        Foreground="Orange"
                        Text="*Note: Multiple values only valid when target parameter storage is Text. If not, only the first spatial value will be filled in."
                        TextWrapping="Wrap"
                        Visibility="{Binding advanced_options.allow_multiple_values, Converter={StaticResource BooleanToVisibilityConverter}}" />

                    <!--  Assignment Behavior Options  -->
                    <TextBlock
                        Margin="0,0,0,8"
                        FontWeight="SemiBold"
                        Foreground="#2E74B5"
                        Text="Assignment Behavior:" />

                    <CheckBox
                        x:Name="OverrideExistingAssignmentsCheckBox"
                        Margin="10,0,0,8"
                        Content="Override existing value assignments"
                        IsChecked="{Binding advanced_options.override_existing_assignments}"
                        ToolTip="Replace existing values on target elements. If unchecked, only empty parameters will be populated" />

                    <CheckBox
                        x:Name="LogUnmappedElementsCheckBox"
                        Margin="10,0,0,15"
                        Content="Generate detailed log for unmapped elements"
                        IsChecked="{Binding advanced_options.log_unmapped_elements}"
                        ToolTip="Create a comprehensive report of elements that could not be mapped to any spatial" />

                </StackPanel>
            </mah:Flyout>
        </mah:FlyoutsControl>
    </mah:MetroWindow.Flyouts>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Source Document Selection  -->
        <GroupBox
            Grid.Row="0"
            Margin="0,0,0,10"
            Header="Source Configuration">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <ComboBox
                    x:Name="SourceDocumentComboBox"
                    Grid.Row="0"
                    Margin="0,5,0,5"
                    mah:TextBoxHelper.UseFloatingWatermark="True"
                    mah:TextBoxHelper.Watermark="Source Document"
                    DisplayMemberPath="title"
                    IsEditable="True"
                    ItemsSource="{Binding source_documents, Mode=OneWay}"
                    SelectedIndex="{Binding selected_source_document_index}"
                    ToolTip="Select the document containing Spatial elements to extract data from" />

                <ComboBox
                    x:Name="SpatialCategoryComboBox"
                    Grid.Row="1"
                    Margin="0,5,0,5"
                    mah:TextBoxHelper.UseFloatingWatermark="True"
                    mah:TextBoxHelper.Watermark="Spatial Category"
                    DisplayMemberPath="Name"
                    IsEditable="True"
                    ItemsSource="{Binding source_categories, Mode=OneWay}"
                    SelectedIndex="{Binding selected_source_category_index}"
                    ToolTip="Select the type of Spatial to collect (Generic Models, Spaces, Rooms)" />

                <TextBlock
                    Grid.Row="2"
                    Margin="0,5,0,0"
                    FontStyle="Italic"
                    Foreground="Orange"
                    Text="*Note: for 'Generic Models', finding element with 'Export to IFC As' equal 'IfcSpaceType'"
                    TextWrapping="Wrap" />
            </Grid>
        </GroupBox>

        <!--  Target Element Category Selection  -->
        <GroupBox
            Grid.Row="1"
            Margin="0,0,0,10"
            Header="Target Configuration">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <CheckBox
                    x:Name="ActiveViewOnlyTargetCheckBox"
                    Grid.Row="0"
                    Margin="10,5,10,10"
                    mah:CheckBoxHelper.CheckCornerRadius="2"
                    Content="Show elements from active view only"
                    IsChecked="{Binding active_view_only_target, Mode=TwoWay}"
                    Style="{DynamicResource MahApps.Styles.CheckBox.Win10}"
                    ToolTip="Filter target elements to show only those visible in the current active view" />

                <ListBox
                    x:Name="TargetElementCategoryListBox"
                    Grid.Row="1"
                    Margin="5"
                    AlternationCount="2"
                    ItemsSource="{Binding target_elements_by_category}"
                    ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                    SelectionMode="Single"
                    ToolTip="Select the element categories in the current document to map spatial data to">
                    <ListBox.ItemsPanel>
                        <ItemsPanelTemplate>
                            <UniformGrid Columns="3" Rows="0" />
                        </ItemsPanelTemplate>
                    </ListBox.ItemsPanel>
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="2">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <CheckBox
                                    Grid.Column="0"
                                    Margin="0,0,8,0"
                                    VerticalAlignment="Center"
                                    IsChecked="{Binding is_selected, Mode=TwoWay}" />

                                <TextBlock
                                    Grid.Column="1"
                                    VerticalAlignment="Center"
                                    TextTrimming="CharacterEllipsis"
                                    ToolTip="{Binding display_name}">
                                    <TextBlock.Text>
                                        <MultiBinding StringFormat="{}{0}: {1}">
                                            <Binding Path="name" />
                                            <Binding Path="element_count" />
                                        </MultiBinding>
                                    </TextBlock.Text>
                                </TextBlock>
                            </Grid>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </Grid>
        </GroupBox>

        <!--  Parameter Mapping Configuration  -->
        <GroupBox
            Grid.Row="2"
            Margin="0,0,0,10"
            Header="Parameter Mapping">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Header Information  -->
                <TextBlock
                    Grid.Row="0"
                    Margin="0,0,0,10"
                    FontStyle="Italic"
                    Text="Configure multiple parameter mapping pairs between source Spatial elements and target MEP elements:"
                    TextWrapping="Wrap" />

                <!--  Parameter Mappings ListBox  -->
                <ListBox
                    x:Name="ParameterMappingsListBox"
                    Grid.Row="1"
                    MinHeight="80"
                    Margin="0,0,0,10"
                    ItemsSource="{Binding mapping_viewmodel.parameter_mappings, Mode=TwoWay}"
                    ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                    ScrollViewer.VerticalScrollBarVisibility="Auto">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <!--  Source Parameter Label  -->
                                <TextBlock
                                    Grid.Column="0"
                                    Margin="0,0,8,0"
                                    VerticalAlignment="Center"
                                    FontWeight="SemiBold"
                                    Foreground="#28A745"
                                    Text="Source:" />

                                <!--  Source Parameter ComboBox  -->
                                <ComboBox
                                    x:Name="SourceParameterComboBox"
                                    Grid.Column="1"
                                    MinWidth="150"
                                    Margin="0,0,15,0"
                                    DisplayMemberPath="name"
                                    IsEditable="True"
                                    IsTextSearchEnabled="True"
                                    ItemsSource="{Binding DataContext.mapping_viewmodel.source_parameters, RelativeSource={RelativeSource AncestorType=ListBox}, Mode=OneWay}"
                                    SelectedIndex="{Binding source_param_index, Mode=TwoWay}"
                                    ToolTip="Select or type the parameter name from Spatial elements to use as the source" />

                                <!--  Target Parameter Label  -->
                                <TextBlock
                                    Grid.Column="2"
                                    Margin="0,0,8,0"
                                    VerticalAlignment="Center"
                                    FontWeight="SemiBold"
                                    Foreground="#28A745"
                                    Text="Target:" />

                                <!--  Target Parameter ComboBox  -->
                                <ComboBox
                                    x:Name="TargetParameterComboBox"
                                    Grid.Column="3"
                                    MinWidth="150"
                                    DisplayMemberPath="name"
                                    IsEditable="True"
                                    IsTextSearchEnabled="True"
                                    ItemsSource="{Binding DataContext.mapping_viewmodel.target_parameters, RelativeSource={RelativeSource AncestorType=ListBox}, Mode=OneWay}"
                                    SelectedIndex="{Binding target_param_index, Mode=TwoWay}"
                                    ToolTip="Select or type the parameter name on target elements to receive the values" />
                            </Grid>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>

                <!--  Action Buttons  -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <Button
                        x:Name="AddParameterMappingButton"
                        Grid.Column="0"
                        Margin="0,0,10,0"
                        Command="{Binding mapping_viewmodel.add_parameter_mapping_command}"
                        Content="Add Mapping"
                        Style="{DynamicResource MahApps.Styles.Button.Square.Accent}"
                        ToolTip="Add a new parameter mapping pair" />

                    <Button
                        x:Name="RemoveParameterMappingButton"
                        Grid.Column="1"
                        Command="{Binding mapping_viewmodel.remove_parameter_mapping_command}"
                        CommandParameter="{Binding SelectedItem, ElementName=ParameterMappingsListBox}"
                        Content="Remove Selected"
                        Style="{DynamicResource MahApps.Styles.Button.Square}"
                        ToolTip="Remove the selected parameter mapping pair" />

                    <TextBlock
                        x:Name="MappingCountTextBlock"
                        Grid.Column="3"
                        VerticalAlignment="Center"
                        FontWeight="SemiBold"
                        Text="{Binding mapping_viewmodel.parameter_mappings.Count, StringFormat='Mappings: {0}'}" />
                </Grid>
            </Grid>
        </GroupBox>

        <!--  Status and Action Buttons  -->
        <Grid Grid.Row="3" Margin="0,10,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!--  Status Information  -->
            <Border
                Grid.Row="0"
                Margin="0,0,0,10"
                Padding="10,8"
                BorderThickness="1"
                CornerRadius="4">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="53*" />
                        <ColumnDefinition Width="425*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBlock
                        x:Name="SourceElementsCountTextBlock"
                        Grid.Column="0"
                        Margin="0,0,15,0"
                        VerticalAlignment="Center"
                        FontWeight="SemiBold"
                        Foreground="#28A745"
                        Text="{Binding source_element_count, StringFormat='Source Elements: {0}'}" />

                    <TextBlock
                        x:Name="TargetElementsCountTextBlock"
                        Grid.Column="1"
                        Margin="0,0,15,0"
                        VerticalAlignment="Center"
                        FontWeight="SemiBold"
                        Foreground="#007BFF"
                        Text="{Binding target_element_count, StringFormat='Target Elements: {0}'}" />

                    <TextBlock
                        x:Name="StatusTextBlock"
                        Grid.Column="4"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontWeight="SemiBold"
                        Foreground="{Binding StatusColor}"
                        Text="{Binding StatusMessage}" />
                </Grid>
            </Border>

            <!--  Action Buttons  -->
            <StackPanel
                Grid.Row="1"
                HorizontalAlignment="Right"
                Orientation="Horizontal">
                <Button
                    x:Name="RunMappingButton"
                    Width="140"
                    Height="35"
                    Margin="0"
                    Background="#28A745"
                    BorderBrush="#28A745"
                    Command="{Binding run_command}"
                    Content="Run Mapping"
                    Foreground="White"
                    ToolTip="Preview and execute the spatial mapping" />
            </StackPanel>
        </Grid>
    </Grid>
</mah:MetroWindow>
