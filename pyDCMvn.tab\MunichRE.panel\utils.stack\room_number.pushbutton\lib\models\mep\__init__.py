# coding: utf-8
"""
MEP Elements Module

This module provides object-oriented wrappers for Revit mep elements
including curve based element and Family instance elements

Classes:
    BaseMep: Base class for all mep elements
    CurveBasedMep: Wrapper for Revit curve based mep elements
    PointBasedMep: Wrapper for Revit point based mep elements
"""

# Import base classes
from .base_mep import BaseMep

# Import specific mep element classes
from .curve_based_mep import CurveBasedMep
from .point_based_mep import PointBasedMep

# Define what gets imported with "from mep import *"
__all__ = [
    'BaseMep',
    'CurveBasedMep',
    'PointBasedMep',
]