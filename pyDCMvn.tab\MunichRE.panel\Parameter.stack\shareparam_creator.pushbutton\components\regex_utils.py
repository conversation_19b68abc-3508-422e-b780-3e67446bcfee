# coding: utf-8
"""Use regex to extract categories and families from a string."""
import re


def extract_categories(input_string):
    # type: (str) -> list[str]
    """Extract categories from a string

    Args:
        input_string (str): The input string

    Returns:
        List[str]: The list of categories name
    """
    # Split the string by non-alphanumeric characters to get the groups
    groups = re.split(r'[^\w\s]', input_string)

    # Trim all spaces before and after each group + remove extra spaces between words
    groups = [re.sub(r'\s{2,}', ' ', group.strip()) for group in groups]

    # Remove empty groups
    groups = [group for group in groups if group]

    return groups

def extract_families(input_string):
    # type: (str) -> list[str]
    """Extract families from a string

    Args:
        input_string (str): The input string

    Returns:
        List[str]: The list of families name
    """
    # Split the string by non-alphanumeric characters to get the families
    families = re.split(",", input_string)

    # Trim all spaces before and after each family + remove extra spaces between words
    families = [re.sub(r'\s{2,}', ' ', family.strip()) for family in families]

    # Remove empty families
    families = [family for family in families if family]

    return families


# Test the function
"""
Case 1: 'Air Terminals,Mechanical Equipment,Duct Accessories' --> ['Air Terminals', 'Mechanical Equipment', 'Duct Accessories']
Case 2: 'All/Detail Items,Air Terminals' --> ['All', 'Detail Items', 'Air Terminals']
Case 3: 'All' --> ['All']
Case 4: 'All?' --> 'categories format not valid: not acceptable character "?"'
Case 5: 'All/Detail Items-Air Terminals' --> 'categories format not valid: not acceptable character "-"'
Case 6: 'Air Terminals-Mechanical Equipment-Duct Accessories' --> 'categories format not valid: not acceptable character "-"'
Case 7: 'All+Air Terminals-Mechanical Equipment-Duct' --> 'categories format not valid: not acceptable character "+" and "-"'
Case 8: 'N/A' --> []
Case 9: '   qưeqwe' --> ['qưeqwe']
Case 10: '420_ME_Test1,420_ME_Test2,420_ME_Test3' --> ['420_ME_Test1', '420_ME_Test2', '420_ME_Test3']
"""
if __name__ == '__main__':
    print(extract_categories('Air Terminals,Mechanical Equipment,Duct Accessories'))  # case 1
    print(extract_categories('All/ Detail Items, Air Terminals'))  # case 2
    print(extract_categories('All'))  # case 3
    print(extract_categories('All?'))  # case 4
    print(extract_categories('All/Detail Items-Air Terminals'))  # case 5
    print(extract_categories('Air Terminals-Mechanical Equipment-Duct Accessories'))  # case 6
    print(extract_categories('All+Air Terminals-Mechanical Equipment-Duct'))  # case 7
    print(extract_categories('N/A'))  # case 8
    print(extract_categories('   qưeqwe'))  # case 9
    print(extract_families('420_ME_Test1,420_ME_Test2,420_ME_Test3'))  # case 10
    print(extract_families(''))  # case 10
