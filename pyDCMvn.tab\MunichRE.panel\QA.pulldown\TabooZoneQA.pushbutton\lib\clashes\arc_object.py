# coding: utf-8
from DCMvn.core import DB
from DCMvn.revit.geometry import GetElementMergedSolid


class ArcObject:
    """ Provide a wrapper for Architecture element """
    def __init__(self, revit_arc_element, transform=None):
        # type: (DB.Element, DB.Transform) -> None
        self.__revit_arc_element = revit_arc_element
        self.__transform = transform
        self.__document = revit_arc_element.Document

    @property
    def transform(self):
        # type: () -> DB.Transform
        return self.__transform

    @transform.setter
    def transform(self, value):
        # type: (DB.Transform) -> None
        self.__transform = value

    @property
    def id(self):
        # type: () -> DB.ElementId
        return self.__revit_arc_element.Id

    @property
    def source_title(self):
        # type: () -> str
        """
        Get the Link title of the source
        Returns:
            str: Link title of the source
        """
        return self.__document.Title

    @property
    def category(self):
        # type: () -> str
        """
        Get the category of the arc element
        Returns:
            str: category name
        """
        return self.__revit_arc_element.Category.Name

    @property
    def guid(self):
        # type: () -> str
        """
        Get the GUID of the arc element
        Returns:
            str: GUID of the arc element
        """
        return self.__revit_arc_element.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()

    @property
    def phase(self):
        # type: () -> str
        """
        Get the phase of the arc element
        Returns:
            str: phase of the arc element or None when not found
        """
        phase_param = self.__revit_arc_element.LookupParameter("Phasing.Phase Created")
        if phase_param:
            return phase_param.AsValueString()
        return ""

    def get_solid(self):
        # type: () -> DB.Solid
        """
        Get the solid of the arc element
        Returns:
            DB.Solid: solid of the arc element
        """
        arc_solid = GetElementMergedSolid(self.__revit_arc_element)
        if self.__transform:
            return DB.SolidUtils.CreateTransformed(arc_solid, self.__transform) if arc_solid else None
        return arc_solid

    def get_bbox(self):
        # type: () -> DB.BoundingBoxXYZ
        """
        Get the bounding box of the arc element
        Returns:
            DB.BoundingBoxXYZ: bounding box of the arc element
        """
        origin_bbox = self.__revit_arc_element.get_BoundingBox(None)
        bbox = DB.BoundingBoxXYZ()
        bbox.Min = bbox.Transform.OfPoint(origin_bbox.Min)
        bbox.Max = bbox.Transform.OfPoint(origin_bbox.Max)
        return bbox

    @staticmethod
    def get_outline(solid):
        # type: (DB.Solid) -> DB.Outline
        bbox = solid.GetBoundingBox()
        min_point = bbox.Transform.OfPoint(bbox.Min)
        max_point = bbox.Transform.OfPoint(bbox.Max)
        return DB.Outline(min_point, max_point)

    def get_associated_condition(self):
        condition = self.source_title.split("_")[-1].split(".ifc")[0]
        return condition

    def to_element(self):
        # type: () -> DB.Element
        return self.__revit_arc_element
