# coding: utf-8
from DCMvn.forms.mvvm import ViewModelBase
from DCMvn.core.framework import Debug
from DCMvn.io import get_config_property, set_config_property


class AdvancedOptionsViewModel(ViewModelBase):
    def __init__(self):
        ViewModelBase.__init__(self)
        
        # Config section
        self.__config_section = "pyDCMvn.MunichRE.RoomNumberMapping.AdvancedOptions"
        
        # Spatial Relationship Options - with default values
        self.__map_elements_above_spaces = False
        self.__above_allowed_distance = 1000  # Default value in mm
        self.__use_proximity_mapping = False
        self.__nearest_allowed_distance = 1000  # Default value in mm
        self.__use_default_value_when_not_found = False
        self.__default_value_when_not_found = "N/a"
        self.__allow_multiple_values = False
        self.__separator_value = "/"  # Default separator
        
        # Assignment Behavior Options
        self.__override_existing_assignments = True
        self.__log_unmapped_elements = True
        
        # Load saved config on initialization
        self.load_config()

    def load_config(self):
        """Load advanced options from config file"""
        try:
            # Spatial Relationship Options
            map_above = get_config_property(self.__config_section, "map_elements_above_spaces")
            if map_above is not None:
                self.__map_elements_above_spaces = bool(map_above)
                
            above_distance = get_config_property(self.__config_section, "above_allowed_distance")
            if above_distance is not None:
                self.__above_allowed_distance = float(above_distance)
                
            use_proximity = get_config_property(self.__config_section, "use_proximity_mapping")
            if use_proximity is not None:
                self.__use_proximity_mapping = bool(use_proximity)
                
            proximity_distance = get_config_property(self.__config_section, "nearest_allowed_distance")
            if proximity_distance is not None:
                self.__nearest_allowed_distance = float(proximity_distance)
                
            use_default = get_config_property(self.__config_section, "use_default_value_when_not_found")
            if use_default is not None:
                self.__use_default_value_when_not_found = bool(use_default)
                
            default_value = get_config_property(self.__config_section, "default_value_when_not_found")
            if default_value is not None:
                self.__default_value_when_not_found = str(default_value)
                
            allow_multiple = get_config_property(self.__config_section, "allow_multiple_values")
            if allow_multiple is not None:
                self.__allow_multiple_values = bool(allow_multiple)
                
            separator = get_config_property(self.__config_section, "separator_value")
            if separator is not None:
                self.__separator_value = str(separator)
                
            # Assignment Behavior Options
            override_existing = get_config_property(self.__config_section, "override_existing_assignments")
            if override_existing is not None:
                self.__override_existing_assignments = bool(override_existing)
                
            log_unmapped = get_config_property(self.__config_section, "log_unmapped_elements")
            if log_unmapped is not None:
                self.__log_unmapped_elements = bool(log_unmapped)
                
            Debug.WriteLine("Advanced options config loaded successfully")
            
        except Exception as ex:
            Debug.WriteLine("Error loading advanced options config: {}".format(str(ex)))
            # Keep default values if loading fails
            
    def save_config(self):
        """Save current advanced options to config file"""
        try:
            # Spatial Relationship Options
            set_config_property(self.__config_section, "map_elements_above_spaces", self.__map_elements_above_spaces)
            set_config_property(self.__config_section, "above_allowed_distance", self.__above_allowed_distance)
            set_config_property(self.__config_section, "use_proximity_mapping", self.__use_proximity_mapping)
            set_config_property(self.__config_section, "nearest_allowed_distance", self.__nearest_allowed_distance)
            set_config_property(self.__config_section, "use_default_value_when_not_found", self.__use_default_value_when_not_found)
            set_config_property(self.__config_section, "default_value_when_not_found", self.__default_value_when_not_found)
            set_config_property(self.__config_section, "allow_multiple_values", self.__allow_multiple_values)
            set_config_property(self.__config_section, "separator_value", self.__separator_value)
            
            # Assignment Behavior Options
            set_config_property(self.__config_section, "override_existing_assignments", self.__override_existing_assignments)
            set_config_property(self.__config_section, "log_unmapped_elements", self.__log_unmapped_elements)
            
            Debug.WriteLine("Advanced options config saved successfully")
            
        except Exception as ex:
            Debug.WriteLine("Error saving advanced options config: {}".format(str(ex)))

    # Spatial Relationship Properties
    @property
    def map_elements_above_spaces(self):
        return self.__map_elements_above_spaces

    @map_elements_above_spaces.setter
    def map_elements_above_spaces(self, value):
        if self.__map_elements_above_spaces != value:
            self.__map_elements_above_spaces = value
            self.RaisePropertyChanged("map_elements_above_spaces")

    @property
    def above_allowed_distance(self):
        return self.__above_allowed_distance

    @above_allowed_distance.setter
    def above_allowed_distance(self, value):
        if self.__above_allowed_distance != value:
            self.__above_allowed_distance = value
            self.RaisePropertyChanged("above_allowed_distance")

    @property
    def use_proximity_mapping(self):
        return self.__use_proximity_mapping

    @use_proximity_mapping.setter
    def use_proximity_mapping(self, value):
        if self.__use_proximity_mapping != value:
            self.__use_proximity_mapping = value
            self.RaisePropertyChanged("use_proximity_mapping")

    @property
    def nearest_allowed_distance(self):
        return self.__nearest_allowed_distance

    @nearest_allowed_distance.setter
    def nearest_allowed_distance(self, value):
        if self.__nearest_allowed_distance != value:
            self.__nearest_allowed_distance = value
            self.RaisePropertyChanged("nearest_allowed_distance")

    @property
    def use_default_value_when_not_found(self):
        return self.__use_default_value_when_not_found

    @use_default_value_when_not_found.setter
    def use_default_value_when_not_found(self, value):
        if self.__use_default_value_when_not_found != value:
            self.__use_default_value_when_not_found = value
            self.RaisePropertyChanged("use_default_value_when_not_found")

    @property
    def default_value_when_not_found(self):
        return self.__default_value_when_not_found

    @default_value_when_not_found.setter
    def default_value_when_not_found(self, value):
        if self.__default_value_when_not_found != value:
            self.__default_value_when_not_found = value
            self.RaisePropertyChanged("default_value_when_not_found")

    @property
    def allow_multiple_values(self):
        return self.__allow_multiple_values

    @allow_multiple_values.setter
    def allow_multiple_values(self, value):
        if self.__allow_multiple_values != value:
            self.__allow_multiple_values = value
            self.RaisePropertyChanged("allow_multiple_values")

    @property
    def separator_value(self):
        return self.__separator_value

    @separator_value.setter
    def separator_value(self, value):
        if self.__separator_value != value:
            self.__separator_value = value
            self.RaisePropertyChanged("separator_value")

    # Assignment Behavior Properties
    @property
    def override_existing_assignments(self):
        return self.__override_existing_assignments

    @override_existing_assignments.setter
    def override_existing_assignments(self, value):
        if self.__override_existing_assignments != value:
            self.__override_existing_assignments = value
            self.RaisePropertyChanged("override_existing_assignments")

    @property
    def log_unmapped_elements(self):
        return self.__log_unmapped_elements

    @log_unmapped_elements.setter
    def log_unmapped_elements(self, value):
        if self.__log_unmapped_elements != value:
            self.__log_unmapped_elements = value
            self.RaisePropertyChanged("log_unmapped_elements")