# coding: utf-8
import clr

from DCMvn.core import DB, HOST_APP
from DCMvn.core.framework import List, System
from ..models import TargetCategory, ParameterWrapper
from pyrevit.compat import get_elementid_value_func

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)  # noqa


def get_ifc_space_type_filter():
    # type: () -> DB.ElementParameterFilter
    """
    Creates an ElementParameterFilter for filtering elements by IFC export type "IfcSpace".
        
    Returns:
        ElementParameterFilter configured to filter by IfcSpace export type
    """
    # Rule Arguments
    p_type_id = DB.ElementId(DB.BuiltInParameter.IFC_EXPORT_ELEMENT_AS)
    f_param = DB.ParameterValueProvider(p_type_id)
    evaluator = DB.FilterStringEquals()
    value = "IfcSpaceType"
    
    if HOST_APP.version >= 2023:
        f_rule = DB.FilterStringRule(f_param, evaluator, value)
    else:
        f_rule = DB.FilterStringRule(f_param, evaluator, value, True)  # noqa
    
    filter_type_name = DB.ElementParameterFilter(f_rule)
    return filter_type_name


def get_element_physical_filter(document):
    # type: (DB.Document) -> DB.ElementMulticategoryFilter
    """
    Creates a filter for physical model elements, excluding HVAC Zones, Lines, 
    Detail Components, and System categories.
    """
    get_elementid_value = get_elementid_value_func()
    categories = document.Settings.Categories
    hvac_zone_id = DB.ElementId(DB.BuiltInCategory.OST_HVAC_Zones)
    line_id = DB.ElementId(DB.BuiltInCategory.OST_Lines)
    detail_id = DB.ElementId(DB.BuiltInCategory.OST_DetailComponents)

    model_category_ids = List[DB.ElementId](
        [
            cat.Id
            for cat in categories  # noqa
            if cat.CategoryType == DB.CategoryType.Model
            and cat.CanAddSubcategory
            and cat.Id != hvac_zone_id
            and cat.Id != line_id
            and cat.Id != detail_id
            and "System" not in DB.BuiltInCategory(get_elementid_value(cat.Id)).ToString()  # noqa
        ]
    )

    return DB.ElementMulticategoryFilter(model_category_ids)


def get_loaded_link_documents(document):
    # type: (DB.Document) -> List[DB.Document]
    """Get all loaded Revit link documents."""
    return (
        DB.FilteredElementCollector(document)  # noqa
        .OfClass(DB.RevitLinkInstance)
        .WhereElementIsNotElementType()
        .Where(lambda x: DB.RevitLinkType.IsLoaded(document, x.GetTypeId()))
        .Select(lambda x: x.GetLinkDocument())
        .ToList()
    )


def get_physical_elements(document, active_view_only=False):
    # type: (DB.Document, bool) -> List[DB.Element]
    """Get physical elements from document or active view only."""
    if active_view_only and document.ActiveView:
        return (
            DB.FilteredElementCollector(document, document.ActiveView.Id)  # noqa
            .WhereElementIsNotElementType()
            .WherePasses(get_element_physical_filter(document))
            .ToList()
        )
    
    return (
        DB.FilteredElementCollector(document)  # noqa
        .WhereElementIsNotElementType()
        .WherePasses(get_element_physical_filter(document))
        .ToList()
    )


def get_spatial_categories(document):
    # type: (DB.Document) -> List[DB.Category]
    """Get spatial categories: Spaces, Rooms, and GenericModel."""
    categories = document.Settings.Categories
    return [
        categories.get_Item(DB.BuiltInCategory.OST_MEPSpaces),  # noqa
        categories.get_Item(DB.BuiltInCategory.OST_Rooms),  # noqa
        categories.get_Item(DB.BuiltInCategory.OST_GenericModel),  # noqa
    ]


def group_elements_by_category(elements):
    # type: (List[DB.Element]) -> List[TargetCategory]
    """Group elements by category and return sorted TargetCategory objects."""
    elements_with_category = elements.Where(lambda e: e.Category is not None).ToList()

    get_elementid = get_elementid_value_func()
    grouped_elements = elements_with_category.GroupBy(
        lambda element: get_elementid(element.Category.Id)
    ).ToList()

    target_categories = []
    for group in grouped_elements:
        try:
            first_element = group.First()
            target_category = TargetCategory(
                category=first_element.Category, 
                elements=group.ToList()
            )
            target_categories.append(target_category)
        except:  # noqa
            continue

    return target_categories.AsQueryable().OrderBy(lambda tc: tc.name).ToList()  # noqa


def to_builtin_parameter(element_id):
    # type: (DB.ElementId) -> DB.BuiltInParameter
    """
    Converts the input ElementId to a BuiltInParameter
    
    Args:
        element_id: The ElementId to convert
        
    Returns:
        The BuiltInParameter equivalent of the input ElementId
    """
    # Check Revit version to use appropriate property
    if HOST_APP.version >= 2024:
        id_value = element_id.Value
    else:
        id_value = element_id.IntegerValue
    
    # Built-in parameters have negative values, positive values are custom parameters
    if id_value > 0:
        return DB.BuiltInParameter.INVALID
    else:
        try:
            return DB.BuiltInParameter(id_value) # noqa
        except:  # noqa
            return DB.BuiltInParameter.INVALID


def get_filterable_parameters_items(document, category_ids):
    # type: (DB.Document, List[DB.ElementId]) -> List[ParameterWrapper]
    """
    Retrieves a list of filterable parameter items for the specified categories in the document.
    
    This method identifies filterable parameters for the given categories and returns them as a
    list of ParameterWrapper objects. If a parameter corresponds to a built-in parameter, it is included
    with its name and built-in parameter value. Otherwise, the parameter is included with its name and
    definition.
    """
    parameters_map = List[ParameterWrapper]()
    
    # Get filterable parameters that are common to all specified categories
    parameter_ids = DB.ParameterFilterUtilities.GetFilterableParametersInCommon(document, category_ids)
    
    for param_id in parameter_ids:
        try:
            # Try to convert to built-in parameter
            builtin_parameter = to_builtin_parameter(param_id)
            
            if builtin_parameter != DB.BuiltInParameter.INVALID:
                # This is a built-in parameter
                param_name = DB.LabelUtils.GetLabelFor(builtin_parameter)
                parameter_item = ParameterWrapper(
                    name=param_name,
                    element_id=param_id,
                    definition=None,
                    builtin_parameter=builtin_parameter
                )
                parameters_map.Add(parameter_item)
            else:
                # This is a custom parameter - get the parameter element
                parameter_element = document.GetElement(param_id)
                
                if isinstance(parameter_element, DB.ParameterElement):
                    definition = parameter_element.GetDefinition()
                    if definition is not None:
                        parameter_item = ParameterWrapper(
                            name=definition.Name,
                            element_id=param_id,
                            definition=definition,
                            builtin_parameter=DB.BuiltInParameter.INVALID
                        )
                        parameters_map.Add(parameter_item)
        except:  # noqa
            # Skip parameters that can't be processed
            continue
    
    return parameters_map


def get_applicable_parameters(document, elements):
    # type: (DB.Document, List[DB.Element]) -> List[ParameterWrapper]
    """
    Retrieves a list of parameters that are applicable to the specified elements within the given document.
    
    This method filters parameters based on their association with the specified elements and
    ensures that only writable parameters are included in the result.
    """
    if not elements:
        return List[ParameterWrapper]()
    
    # Group elements by category and get unique category IDs
    categories = List[DB.ElementId](elements
                 .Where(lambda e: e.Category is not None)
                 .GroupBy(lambda e: e.Category.Id)
                 .Select(lambda g: g.Key)
                 .ToList())

    if not categories:
        return List[ParameterWrapper]()
    
    # Get filterable parameters for these categories
    filterable_parameters = get_filterable_parameters_items(document, categories)
    
    # Filter to only parameters that are writable on at least one element using LINQ
    applicable_parameters = (filterable_parameters
                           .Where(lambda param_wrapper: elements.Any(lambda element: param_wrapper.is_writable_on_element(element)))
                           .ToList())
    
    return applicable_parameters
