# WPF UI and MVVM Implementation

You are an expert in WPF/XAML development with IronPython and DCMvn MVVM patterns. Generate complete UI solutions following established architecture.

## Reference Architecture
Study these patterns for implementation:
- [WPF IronPython](#file:../.cursor/rules/wpf-ironpython.mdc)
- [XAML Preview](#file:../.cursor/rules/wpf-xaml-preview.mdc)
- [ViewModel Patterns](#file:../.cursor/rules/viewmodel/viewmodel-patterns.mdc)
- [Main ViewModel](#file:../.cursor/rules/viewmodel/main-viewmodel-pattern.py)

## MVVM Architecture Requirements

### ViewModel Base Implementation
```python
from DCMvn.forms.mvvm import ViewModelBase, RelayCommand
from DCMvn.core.framework import ObservableCollection

class MainViewModel(ViewModelBase):
    def __init__(self, document):
        super(MainViewModel, self).__init__()
        self.document = document
        self._initialize_services()
        self._initialize_commands()
        self._initialize_properties()
    
    def _initialize_services(self):
        # Initialize service layer
        pass
    
    def _initialize_commands(self):
        self.execute_command = RelayCommand(self.execute, self.can_execute)
        self.cancel_command = RelayCommand(self.cancel, lambda p: True)
    
    def _initialize_properties(self):
        self._items = ObservableCollection[object]()
        self._selected_item = None
        self._is_busy = False
```

### Property Pattern
```python
@property
def selected_item(self):
    return self._selected_item

@selected_item.setter
def selected_item(self, value):
    if self._selected_item != value:
        self._selected_item = value
        self.RaisePropertyChanged("selected_item")
        # Update dependent commands
        self.execute_command.RaiseCanExecuteChanged()
```

### Command Implementation
```python
def execute(self, parameter):
    """Execute main operation with external events."""
    try:
        self.is_busy = True
        # Use external events for Revit API operations
        self._event_handler.Raise(self._perform_revit_operation)
    except Exception as e:
        alert("Operation failed: {}".format(str(e)), "Error")
    finally:
        self.is_busy = False

def can_execute(self, parameter):
    return not self.is_busy and self.selected_item is not None
```

## XAML UI Structure

### Main Window Template
```xaml
<Window x:Class="MainView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="{Binding Title}" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <!-- Define styles and templates here -->
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="10">
            <TextBlock Text="{Binding Title}" Foreground="White" FontSize="16" FontWeight="Bold"/>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="10">
            <!-- Content implementation based on requirements -->
        </Grid>
        
        <!-- Footer -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="10">
            <Button Content="Execute" Command="{Binding ExecuteCommand}" 
                    Margin="5" Padding="15,5" MinWidth="80"/>
            <Button Content="Cancel" Command="{Binding CancelCommand}" 
                    Margin="5" Padding="15,5" MinWidth="80"/>
        </StackPanel>
    </Grid>
</Window>
```

### Code-Behind Pattern
```python
import clr
clr.AddReference("PresentationFramework")
clr.AddReference("PresentationCore")
clr.AddReference("WindowsBase")

from System.Windows import Window
from System.Windows.Markup import XamlReader

class MainView(Window):
    def __init__(self, view_model):
        # Load XAML
        xaml_file = os.path.join(os.path.dirname(__file__), "MainView.xaml")
        with open(xaml_file, 'r') as f:
            self.ui = XamlReader.Parse(f.read())
        
        # Set up view
        self.DataContext = view_model
        self.view_model = view_model
        
        # Wire up events if needed
        self._setup_event_handlers()
    
    def _setup_event_handlers(self):
        # Set up any additional event handlers
        pass
    
    def show(self, modal=True):
        if modal:
            self.ShowDialog()
        else:
            self.Show()
```

## UI Generation Requirements

Generate complete WPF solutions including:
1. **ViewModel Implementation**
   - Inherit from ViewModelBase
   - Implement property change notification
   - Use RelayCommand for actions
   - Handle external events properly

2. **XAML Layout**
   - Responsive grid-based layout
   - Proper data binding syntax
   - Consistent styling
   - Accessibility considerations

3. **Code-Behind Integration**
   - Minimal code-behind logic
   - Proper XAML loading
   - Event handler setup
   - Resource management

4. **Threading and Events**
   - External event handlers for Revit API
   - Progress indication for long operations
   - Proper exception handling
   - UI responsiveness

## Quality Standards
- Follow DCMvn MVVM patterns
- Use ObservableCollection for dynamic data
- Implement proper command patterns
- Handle threading correctly
- Provide user feedback
- Include comprehensive error handling
- Use consistent styling
- Ensure proper resource disposal
