# coding: utf-8
"""
Validation constants for TabooZone QA (DTO-based system)

Note: This co-exists with the legacy constant.py used by older validators.
New modules should import from this constants.py.
"""

# Default names
DEFAULT_ARC_NAME = "ARC"
DEFAULT_MASS_NAME = "Mass"

# Excel column headers (DTO-based)
GUID = "GUID"
ARC_ID = "Arc ID"
MASS_ID = "Mass ID"
ARC_INFO = "Arc Info"
MASS_INFO = "Mass Info"
GUID_VALIDATION = "Guid Validation"
CENTROID_DISTANCE_MM = "Centroid Distance (mm)"
CENTROID_RESULT_COLUMN = "Centroid Result"
CONCLUDED = "Concluded"
APPROVER = "Approver"
REASON = "Reason"
RULE_NAME = "Rule"

# Validation results
MATCHED = "Matched"
MISSING = "Missing"
EXTRA = "Extra"
DUPLICATE_ARC_GUID = "Duplicate ARC GUID"
DUPLICATE_MASS_GUID = "Duplicate Mass GUID"
PASSED = "Passed"
FAILED = "Failed"

# Sheet names
TOTAL_SHEET = "Total"


