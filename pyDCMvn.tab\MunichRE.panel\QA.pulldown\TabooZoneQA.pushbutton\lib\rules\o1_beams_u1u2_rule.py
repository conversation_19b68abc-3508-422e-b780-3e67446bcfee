# coding: utf-8
"""
Plugin rule for O1 Existing Beams above U1/U2
Wraps existing O1Rules element collectors in a TabooRule.
"""
from DCMvn.core import DB, HOST_APP
from DCMvn.core.framework import Debug, List

from .base import TabooRule, register_rule


def create_shared_param_filter(param_name, param_value, doc):
    # type: (str, str, DB.Document) -> DB.ElementParameterFilter
    """Create ElementParameterFilter for shared parameter by name"""
    # Get all shared parameters in the document
    shared_params = doc.ParameterBindings.ForwardIterator()
    param_definition = None
    
    # Find the parameter definition by name
    while shared_params.MoveNext():
        definition = shared_params.Key
        if hasattr(definition, 'Name') and definition.Name == param_name:
            param_definition = definition
            break
    
    if param_definition is None:
        # If parameter not found, return a filter that matches nothing
        # This prevents errors but won't match any elements
        dummy_param_id = DB.ElementId(DB.BuiltInParameter.INVALID)
        dummy_provider = DB.ParameterValueProvider(dummy_param_id)
        dummy_evaluator = DB.FilterStringEquals()
        if HOST_APP.version >= 2023:
            dummy_rule = DB.FilterStringRule(dummy_provider, dummy_evaluator, "NEVER_MATCH")
        else:
            dummy_rule = DB.FilterStringRule(dummy_provider, dummy_evaluator, "NEVER_MATCH", True)
        return DB.ElementParameterFilter(dummy_rule)
    
    # Create parameter provider using the found definition
    param_id = param_definition.Id if hasattr(param_definition, 'Id') else DB.ElementId(DB.BuiltInParameter.INVALID)
    param_provider = DB.ParameterValueProvider(param_id)
    evaluator = DB.FilterStringEquals()
    
    if HOST_APP.version >= 2023:
        rule = DB.FilterStringRule(param_provider, evaluator, param_value)
    else:
        rule = DB.FilterStringRule(param_provider, evaluator, param_value, True)
    
    return DB.ElementParameterFilter(rule)


@register_rule
class O1BeamsU1U2Rule(TabooRule):
    rule_id = "O1_BEAMS_U1U2"
    display_name = "O1 Existing Beams above U1/U2"

    def __init__(self):
        pass

    def collect_arc_elements(self, arc_doc):
        try:
            if isinstance(arc_doc, DB.RevitLinkInstance):
                arc_doc = arc_doc.GetLinkDocument()
            return O1BeamsU1U2Rule.get_o1_arc_existing_beams_above_u1_u2(arc_doc)
        except Exception as ex:
            Debug.WriteLine("O1BeamsU1U2Rule.collect_arc_elements error: {}".format(str(ex)))
            return []

    def collect_mass_elements(self, mass_doc):
        try:
            return O1BeamsU1U2Rule.get_o1_arc_existing_beams_above_u1_u2_mass(mass_doc)
        except Exception as ex:
            Debug.WriteLine("O1BeamsU1U2Rule.collect_mass_elements error: {}".format(str(ex)))
            return []

    @property
    def mass_guid_param_spec(self):
        return "SI_TE_DM_IfcGUID"

    @property
    def tolerance_mm(self):
        return 10.0

    @property
    def extra_excel_columns(self):
        return {"Guid Validation": "Matched", "Centroid Result": "Passed"}
    
    @staticmethod
    def o1_arc_existing_beam_above_u1_u2(document):
        # type: (DB.Document) -> DB.ElementFilter
        """
        Creates comprehensive ElementParameterFilter for O1 ARC existing beams above U1/U2.
        Filters for beams that are:
        - Phase: "Bestand" (existing)
        - IFC Type: "IfcBeamType" 
        - Location: "U1-O1" OR "U2-O1"
        
        Args:
            document (DB.Document): Revit document to get parameter definitions from
        
        Returns:
            DB.ElementFilter: Combined filter using LogicalAndFilter
        """
        
        # 1. Phase Parameter Filter (MRE_ARC.Bauphase = "Bestand")
        phase_param_name = "MRE_ARC.Bauphase"
        phase_value = "Bestand"
        phase_filter = create_shared_param_filter(phase_param_name, phase_value, document)
        
        # 2. IFC Export Type Filter (IFC_EXPORT_ELEMENT_AS = "IfcBeamType")
        ifc_type_param_id = DB.ElementId(DB.BuiltInParameter.IFC_EXPORT_ELEMENT_AS)
        ifc_type_param_provider = DB.ParameterValueProvider(ifc_type_param_id)
        ifc_type_evaluator = DB.FilterStringEquals()
        ifc_type_value = "IfcBeamType"
        
        if HOST_APP.version >= 2023:
            ifc_type_rule = DB.FilterStringRule(ifc_type_param_provider, ifc_type_evaluator, ifc_type_value)
        else:
            ifc_type_rule = DB.FilterStringRule(ifc_type_param_provider, ifc_type_evaluator, ifc_type_value, True)
        
        ifc_type_filter = DB.ElementParameterFilter(ifc_type_rule)
        
        # 3. Location Parameter Filters (IfcSpatialContainer = "U1-O1" OR "U2-O1")
        location_param_name = "IfcSpatialContainer"
        location_values = ["U1-O1", "U2-O1"]
        
        # Create location filters for each value
        location_filters = []
        for location_value in location_values:
            location_filter = create_shared_param_filter(location_param_name, location_value, document)
            location_filters.append(location_filter)
        
        # Combine location filters with OR logic
        location_or_filter = DB.LogicalOrFilter(List[DB.ElementFilter](location_filters))
        
        # 4. Category Filter for Structural Framing (beams)
        # beam_category_filter = DB.ElementCategoryFilter(DB.BuiltInCategory.OST_StructuralFraming)
        
        # 5. Combine all filters with AND logic
        all_filters = [
            phase_filter,
            ifc_type_filter, 
            location_or_filter,
            # beam_category_filter
        ]
        
        combined_filter = DB.LogicalAndFilter(List[DB.ElementFilter](all_filters))
        
        return combined_filter
    
    @staticmethod
    def get_o1_arc_existing_beams_above_u1_u2(document):
        # type: (DB.Document) -> list[DB.Element]
        """
        Convenience method to get all O1 ARC existing beams above U1/U2 using the filter.
        
        Args:
            document (DB.Document): Revit document to search in
            
        Returns:
            list[DB.Element]: List of beam elements matching the criteria
        """
        try:
            filter_combined = O1BeamsU1U2Rule.o1_arc_existing_beam_above_u1_u2(document)
            
            # Use FilteredElementCollector with the combined filter
            collector = DB.FilteredElementCollector(document)
            elements = collector.WherePasses(filter_combined).WhereElementIsNotElementType().ToElements()
            
            return elements
            
        except Exception as ex:
            Debug.WriteLine("Error collecting O1 ARC existing beams: {}".format(str(ex)))
            return []
    
    @staticmethod
    def get_o1_arc_existing_beams_above_u1_u2_mass(document):
        # type: (DB.Document) -> list[DB.Element]
        """
        Convenience method to get all O1 ARC existing beams above U1/U2 using the filter.
        
        Args:
            document (DB.Document): Revit document to search in
            
        Returns:
            list[DB.Element]: List of beam elements matching the criteria
        """
        family_and_type_param_id = DB.ElementId(DB.BuiltInParameter.ELEM_FAMILY_AND_TYPE_PARAM)
        family_and_type_param_provider = DB.ParameterValueProvider(family_and_type_param_id)
        family_and_type_evaluator = DB.FilterStringContains()
        family_and_type_value = "Existing_Beam_AboveU1&U2"
        
        if HOST_APP.version >= 2023:
            family_and_type_rule = DB.FilterStringRule(family_and_type_param_provider, family_and_type_evaluator, family_and_type_value)
        else:
            family_and_type_rule = DB.FilterStringRule(family_and_type_param_provider, family_and_type_evaluator, family_and_type_value, True)
        
        family_and_type_filter = DB.ElementParameterFilter(family_and_type_rule)
        
        collector = DB.FilteredElementCollector(document)
        elements = collector.WherePasses(family_and_type_filter).WhereElementIsNotElementType().ToElements()
        
        return elements

