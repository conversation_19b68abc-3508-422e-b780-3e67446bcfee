# coding: utf-8
import ast

from DCMvn.core import HOST_APP, DB
from DCMvn.revit.query import get_name
from MunichRE.constants import *
from . import MEPCurveGeometry


class PipeSegment(MEPCurveGeometry):
    def __init__(self, expando_object):
        """
        Pipe Segment
        Args:
            expando_object: ExpandedObject
        """
        MEPCurveGeometry.__init__(self, expando_object)

    @property
    def length(self):
        # type: () -> float
        if isinstance(PIPE_LENGTH, list):
            length = PipeSegment.get_value(param_list=PIPE_LENGTH, expando_object=self._object,
                                           error_message="Length is not defined")
        else:
            length = getattr(self._object, PIPE_LENGTH)
        if not length:
            raise Exception("Length is not defined")
        if isinstance(length, str):
            try:
                length = ast.literal_eval(length)[0]
            except ValueError:
                raise ValueError("Invalid Length Value")
        return DB.UnitUtils.ConvertToInternalUnits(length, DB.UnitTypeId.Millimeters)

    @property
    def nominal_diameter(self):
        # type: () -> float
        if isinstance(PIPE_NOMINAL_DIAMETER, list):
            nominal_diameter = PipeSegment.get_value(expando_object=self._object, param_list=PIPE_NOMINAL_DIAMETER,
                                                     error_message="Nominal Diameter is not defined")
        else:
            nominal_diameter = getattr(self._object, PIPE_NOMINAL_DIAMETER)
        if not nominal_diameter:
            raise Exception("Nominal Diameter is not defined")
        if isinstance(nominal_diameter, str):
            try:
                nominal_diameter = ast.literal_eval(nominal_diameter)[0]
            except ValueError:
                raise ValueError("Invalid Diameter Value")
        return DB.UnitUtils.ConvertToInternalUnits(nominal_diameter, DB.UnitTypeId.Millimeters)

    def create_pipe(self, system_type_id, pipe_type_id, nominal_diameter_by_type, level_id):
        if self.nominal_diameter not in nominal_diameter_by_type:
            raise ValueError("Pipe Type {} does not have DN {}".
                             format(get_name(HOST_APP.doc.GetElement(pipe_type_id)),
                                    "%.0f" % (DB.UnitUtils.ConvertFromInternalUnits(self.nominal_diameter,
                                                                                    DB.UnitTypeId.Millimeters))))

        cross_points, direction_edge, side_edges = self._get_side_edges(self.length)
        start_point, end_point = self._get_points(self.length, cross_points, direction_edge)
        pipe_ = DB.Plumbing.Pipe.Create(HOST_APP.doc, system_type_id, pipe_type_id, level_id,  # noqa
                                        start_point, end_point)

        pipe_.get_Parameter(DB.BuiltInParameter.RBS_PIPE_DIAMETER_PARAM).Set(self.nominal_diameter)
        pipe_.get_Parameter(DB.BuiltInParameter.IFC_GUID).Set(self.id)
