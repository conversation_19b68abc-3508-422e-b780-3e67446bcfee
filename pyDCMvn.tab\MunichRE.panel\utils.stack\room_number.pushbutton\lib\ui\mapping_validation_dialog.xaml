<mah:MetroWindow
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Mapping Validation"
    Width="370"
    MinWidth="350"
    MinHeight="300"
    ShowMaxRestoreButton="False"
    ShowMinButton="False"
    SizeToContent="Height"
    WindowStartupLocation="CenterOwner"
    ResizeMode="CanResizeWithGrip"
    mc:Ignorable="d">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <TextBlock
            Grid.Row="0"
            Margin="0,0,0,15"
            FontSize="16"
            FontWeight="SemiBold"
            Foreground="#2E74B5"
            Text="📋 Mapping Configuration Preview"
            TextWrapping="Wrap" />

        <!--  Content Area  -->
        <ScrollViewer
            Grid.Row="1"
            Margin="0,0,0,20"
            VerticalScrollBarVisibility="Auto"
            HorizontalScrollBarVisibility="Auto"
            MaxHeight="400">
            <TextBlock
                x:Name="ContentTextBlock"
                FontSize="12"
                TextWrapping="Wrap"
                VerticalAlignment="Top"
                Text="{Binding PreviewContent}" />
        </ScrollViewer>

        <!--  Button Panel  -->
        <StackPanel
            Grid.Row="2"
            HorizontalAlignment="Right"
            Orientation="Horizontal">
            
            <Button
                x:Name="OkButton"
                Width="100"
                Height="35"
                Margin="0,0,10,0"
                Background="#28A745"
                BorderBrush="#28A745"
                Content="OK"
                Foreground="White"
                IsDefault="True"
                Click="OnOkClick"
                ToolTip="Confirm and proceed with mapping" />

            <Button
                x:Name="CancelButton"
                Width="100"
                Height="35"
                Content="Cancel"
                IsCancel="True"
                Click="OnCancelClick"
                Style="{DynamicResource MahApps.Styles.Button.Square}"
                ToolTip="Cancel the operation" />
        </StackPanel>
    </Grid>
</mah:MetroWindow> 