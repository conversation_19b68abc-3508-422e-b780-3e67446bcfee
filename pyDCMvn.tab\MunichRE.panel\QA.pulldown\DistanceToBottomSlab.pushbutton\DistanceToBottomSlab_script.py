# -*- coding: utf-8 -*-
import clr
import os
from MunichRE.excel_reader import MiniExcel
clr.AddReference("System","System.Collections.Specialized")
from System.Collections.Specialized import OrderedDictionary
from Autodesk.Revit.UI import *
from Autodesk.Revit.DB import (
    FilteredElementCollector, BuiltInCategory, FamilyInstance,
    UnitUtils, UnitTypeId, Level, View3D,
    ElementCategoryFilter, ReferenceIntersector,
    FindReferenceTarget, XYZ, StorageType, CategoryType,
    RevitLinkInstance, ElementId
)
from pyrevit import script, forms
from DCMvn.forms.wpfforms import SelectFromList
from DCMvn.forms import alert
from DCMvn.revit.geometry import are_elements_intersect
from DCMvn.io import save_excel_file
from collections import defaultdict

def get_ifc_guid(elem):
    """Read IfcGUID parameter if present, else fall back to Element.Id."""
    p = elem.LookupParameter("IfcGUID")
    if p and p.StorageType == StorageType.String:
        val = p.AsString()
        if val:
            return val
    return str(elem.Id.IntegerValue)


def analyze_sprinkler(inst, ri, inspect_doc, view, tolerance, link_id, link_xform):
    '''Return data for a sprinkler instance: level, slab GUID, offset mm, and placement status from the linked model.'''
    loc = inst.Location
    if not loc or not hasattr(loc, 'Point'):
        return None
    pt = loc.Point

    # Ray-based offset detection setup
    origin = XYZ(pt.X, pt.Y, pt.Z)
    direction = XYZ(0, 0, 1)
    # Try to retrieve one or multiple references; normalize to list
    try:
        raw = ri.FindNearest(origin, direction)
    except:
        raw = None
    refs = []
    if raw:
        try:
            refs.extend(raw)
        except:
            refs.append(raw)

    # Find first hit on selected link instance

    nearest = None
    for r in refs:
        ref = r.GetReference()
        if ref.ElementId == link_id and ref.LinkedElementId != ElementId.InvalidElementId:
            nearest = r
            break

    slab_elem = None
    offset_mm = None

    if nearest:
        slab_elem = inspect_doc.GetElement(nearest.GetReference().LinkedElementId)
        hit_pt = origin.Add(direction.Multiply(nearest.Proximity))
        offset_ft = hit_pt.Z - pt.Z
        offset_mm = UnitUtils.ConvertFromInternalUnits(offset_ft, UnitTypeId.Millimeters)
        if offset_mm is not None:
            offset_mm = int(round(offset_mm))
    # Intersection-based check first
    status = None
    if slab_elem:
        # Set up geometry options for solids
        intersect = False
        try:
            intersect = are_elements_intersect(inst, slab_elem)
        except:
            pass
        if intersect:
            status = 'Inside Slab'
        else:
            # Fallback to offset test
            if offset_mm is None:
                status = 'No Slab above'
            elif offset_mm == tolerance:
                status = 'Correct Elevation'
            else:
                status = 'Incorrect Elevation'
    else:
        status = 'No Slab above'

    # Get level name
    lvl_name = 'Unknown Level'
    try:
        if inst.LevelId:
            lvl = inst.Document.GetElement(inst.LevelId)
            if isinstance(lvl, Level):
                lvl_name = lvl.Name
    except:
        pass

    return {
        'level': lvl_name,
        'slab_guid': get_ifc_guid(slab_elem) if slab_elem else None,
        'offset': offset_mm,
        'status': status,
        'inst_guid': get_ifc_guid(inst),
        'inst_id': inst.Id
    }

def save_failed_offset_report(grouping):
    """
    Generate an Excel report for sprinkler with failed offset.
    Args:
        grouping: defaultdict cấu trúc như script, group theo level > slab_guid > category > list sprinkler
    """
    try:
        # Select file path to save
        file_path = save_excel_file("Save Sprinkler Failed Offset Report")
        if not file_path:
            script.get_output().print_md('Report not saved.')
            return

        report_data = []

        for lvl_name, slabs in grouping.items():
            for slab_guid, data in slabs.items():
                for cat_name, heads in data['categories'].items():
                    error_heads = [h for h in heads if h['status'] in ['Inside Slab', 'Incorrect Elevation','No Slab above','Correct Elevation']]
                    for h in error_heads:
                        row = OrderedDictionary()
                        row["Level"] = lvl_name
                        row["Slab GUID"] = slab_guid or ""
                        row["Category"] = cat_name
                        row["Sprinkler GUID"] = h['inst_guid']
                        row["Offset (mm)"] = h['offset'] if h['offset'] is not None else ""
                        row["Status"] = h['status']
                        row["Approver"] = ""
                        row["Reason"] = ""
                        report_data.append(row)

        if os.path.exists(file_path):
            os.remove(file_path)
        MiniExcel.SaveAs(file_path, report_data)
        script.get_output().print_md('# Sprinkler Failed Offset report has been saved: `{}`'.format(file_path))
        return file_path

    except Exception as e:
        script.get_output().print_md('# Failed to save report: {}'.format(str(e)))
        return None

def main():
    host_doc = __revit__.ActiveUIDocument.Document
    view = __revit__.ActiveUIDocument.ActiveView
    out = script.get_output()

    # Ensure running in a 3D view
    if not isinstance(view, View3D):
        out.print_html(
            '<strong style="color:red;">Error:</strong> Please run this tool in a 3D view.'
        )
        return

    # 1. List all loaded Revit links and let user pick one
    links = FilteredElementCollector(host_doc).OfClass(RevitLinkInstance).ToElements()
    selected_link = SelectFromList.show(links, name_attr='Name', multiselect=False)
    if not selected_link:
        out.print_html(
            '<strong style="color:red;">Please select a valid AR model link.</strong>'
        )
        return

    # Get the linked document
    link_doc = selected_link.GetLinkDocument()
    if not link_doc:
        out.print_html('<strong style="color:red;">Invalid link document.</strong>')
        return
    link_inst_id = selected_link.Id

    # Transform for solids
    link_xform = selected_link.GetTotalTransform()

    # Choose categories
    cats = [c for c in host_doc.Settings.Categories if c.CategoryType == CategoryType.Model]
    model_cats = sorted(cats, key=lambda c: c.Name)
    selected_categories = SelectFromList.show(model_cats, name_attr='Name', multiselect=True)
    if not selected_categories:
        out.print_html('<strong>No categories selected.</strong>'); return

    # Tolerance input
    tol_str = forms.ask_for_string(prompt='Enter tolerance (mm)', default='100')
    tolerance = 0.0
    try:
        tolerance = float(tol_str) if tol_str is not None else 0.0
    except:
        pass

    # 3. Prepare ReferenceIntersector for slab detection in links
    floor_filter = ElementCategoryFilter(BuiltInCategory.OST_Floors)
    ri = ReferenceIntersector(floor_filter, FindReferenceTarget.Face, view)
    ri.FindReferencesInRevitLinks = True

    # Collect sprinklers
    sprinklers = []
    for cat in selected_categories:
        elems = FilteredElementCollector(host_doc, view.Id) \
            .OfCategoryId(cat.Id) \
            .WhereElementIsNotElementType() \
            .OfClass(FamilyInstance) \
            .ToElements()
        sprinklers.extend(elems)

    # Grouping and counters
    grouping = defaultdict(lambda: defaultdict(lambda: {'slab': None, 'categories': defaultdict(list)}))
    total_heads = 0
    correct_list = []
    no_slab_list = []
    inside_slab_list = []
    incorrect_list = []

    for inst in sprinklers:
        res = analyze_sprinkler(inst, ri, link_doc, view, tolerance, link_inst_id, link_xform)
        if not res:
            continue
        lvl = res['level']
        slab_key = res['slab_guid']
        grouping[lvl][slab_key]['slab'] = None
        cat_name = inst.Category.Name if inst.Category else 'Unknown'
        grouping[lvl][slab_key]['categories'][cat_name].append(res)
        total_heads += 1
        status = res['status']
        guid = res['inst_guid']
        if status == 'No Slab above':
            no_slab_list.append(guid)
        elif status == 'Inside Slab':
            inside_slab_list.append(guid)
        elif status == 'Correct Elevation':
            correct_list.append(guid)
        else:
            incorrect_list.append(guid)

    # Output reports

    out.print_html('<strong>Failed Offset Report</strong>')
    for lvl_name, slabs in grouping.items():
        out.insert_divider()
        out.print_html('<p>Level: <strong>{0}</strong></p>'.format(lvl_name))
        for slab_key, data in slabs.items():
            for cat_name in sorted(data['categories']):
                heads = data['categories'][cat_name]
                # Filtered failed sprinkler
                error_heads = [h for h in heads if h['status'] in ['Inside Slab', 'Incorrect Elevation']]
                if not error_heads:
                    continue  # Skip report for slab/category for other status

                # Print Group Slab GUID
                if slab_key:
                    out.print_html('<div style="display: flex; align-items: center; width: 100%;"><p>&nbsp;&nbsp;Slab GUID: <strong>{0}</strong></p><span style="border-bottom: 1px solid #ccc; flex-grow: 1; margin-left: 10px;"></span></div>'.format(slab_key))
                else:
                    out.print_html('<p>&nbsp;&nbsp;<em>No Slab Hit</em></p>')

                out.print_html('<p>&nbsp;&nbsp;&nbsp;Category: <strong>{0}</strong> ({1} heads)</p>'.format(cat_name, len(error_heads)))
                for h in error_heads:
                    off = 'N/A' if h['offset'] is None else '{0:+d} mm'.format(h['offset'])
                    guid_link = out.linkify(h['inst_id'], h['inst_guid'])
                    out.print_html('<p>&nbsp;&nbsp;&nbsp;&nbsp;<strong>{0}</strong> ; Offset: <strong>{1}</strong> ; {2}</p>'.format(guid_link, off, h['status']))

    out.insert_divider()
    out.print_html('<strong>Summary Report:</strong>')
    out.print_html('Total Sprinkler Heads: <strong>{0}</strong>'.format(total_heads))
    out.print_html('- Correct Elevation: <strong style="color: green">{0}</strong>'.format(len(correct_list)))
    out.print_html('- No Slab above: <strong>{0}</strong>'.format(len(no_slab_list)))
    out.print_html('- Inside Slab: <strong style="color: red">{0}</strong>'.format(len(inside_slab_list)))
    out.print_html('- Incorrect Elevation: <strong style="color: red">{0}</strong>'.format(len(incorrect_list)))

    for lvl in sorted(grouping.keys()):
        count_incorrect = 0
        for slabs in grouping[lvl].values():
            for cat_heads in slabs['categories'].values():
                for h in cat_heads:
                    if h['status'] == 'Incorrect Elevation':
                        count_incorrect += 1
        out.print_html('<p>&nbsp;&nbsp;+ Level {0}: <strong style="color: red">{1}</strong> incorrect</p>'.format(lvl, count_incorrect))
    if alert("Do you want to save a report?", yes=True, no=True):
        save_failed_offset_report(grouping)
    out.close_others()


if __name__ == '__main__':
    main()

