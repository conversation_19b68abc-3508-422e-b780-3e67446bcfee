# coding: utf-8
"""
GUID Validation Class for TabooZone QA
Validates GUIDs between two sets of Revit elements (e.g., beams and masses).

Validation Cases:
- Correct: GUIDs match between sets
- Missing: GUIDs in reference set but not in target set
- Extra: GUIDs in target set but not in reference set  
- Duplicate: Same GUID appears multiple times in a set
- Swapped: GUIDs exist in both sets but assigned to wrong elements

Usage Example:
    from validation.guid_validator import GuidValidator
    
    validator = GuidValidator()
    result = validator.validate_guid_sets(beams, masses, "Beams", "Masses")
    report = validator.generate_report(result)
    print(report)
"""
import clr
import os

from DCMvn.core import DB
from DCMvn.core.framework import Debug
from DCMvn.forms import alert
from DCMvn.coreutils.assemblyhandler import load_miniexcel

from . import constant

# Load MiniExcel at module level per DCMvn patterns
try:
    load_miniexcel()
    from MiniExcelLibs import MiniExcel
    from MiniExcelLibs.OpenXml import OpenXmlConfiguration  # noqa: F401
    MINIEXCEL_AVAILABLE = True
except Exception as e:
    Debug.WriteLine("Failed to load MiniExcel: {}".format(str(e)))
    MINIEXCEL_AVAILABLE = False

# .NET collections for ordered rows and multi-sheet containers
clr.AddReference("System.Collections.Specialized")
from System.Collections.Specialized import OrderedDictionary  # noqa: E402
from System.Collections.Generic import Dictionary  # noqa: E402
from collections import defaultdict


class GuidValidationResult(object):
    """Container for GUID validation results"""
    
    def __init__(self, arc_name, mass_name):
        # type: (str, str) -> None
        self.arc_name = arc_name          # e.g., "ARC"
        self.mass_name = mass_name        # e.g., "Mass"
        
        # Validation results
        self.correct_matches = []      # type: list[tuple[DB.Element, DB.Element, str]]
        self.missing_in_mass = []      # type: list[tuple[DB.Element, str]]
        self.extra_in_mass = []        # type: list[tuple[DB.Element, str]]
        self.duplicate_in_arc = defaultdict(list)  # type: dict[str, list[DB.Element]]
        self.duplicate_in_mass = defaultdict(list) # type: dict[str, list[DB.Element]]
        self.swapped_pairs = []        # type: list[tuple[DB.Element, DB.Element, str]]
        
        # Statistics
        self.total_arc_elements = 0
        self.total_mass_elements = 0
        self.total_arc_guids = 0
        self.total_mass_guids = 0
        
    @property
    def has_issues(self):
        """Check if there are any validation issues"""
        return (len(self.missing_in_mass) > 0 or 
                len(self.extra_in_mass) > 0 or
                len(self.duplicate_in_arc) > 0 or
                len(self.duplicate_in_mass) > 0 or
                len(self.swapped_pairs) > 0)
    
    @property
    def summary(self):
        """Get validation summary statistics"""
        return {
            constant.CORRECT_MATCHED: len(self.correct_matches),
            constant.MISSING_IN_MASS: len(self.missing_in_mass),
            constant.EXTRA_IN_MASS: len(self.extra_in_mass),
            constant.DUPLICATE_IN_ARC: len(self.duplicate_in_arc),
            constant.DUPLICATE_IN_MASS: len(self.duplicate_in_mass),
            constant.SWAPPED_PAIRS: len(self.swapped_pairs),
            constant.TOTAL_ARC_ELEMENTS: self.total_arc_elements,
            constant.TOTAL_MASS_ELEMENTS: self.total_mass_elements
        }


class GuidValidator(object):
    """GUID validation class for comparing two sets of Revit elements"""
    
    def __init__(self):
        pass
    
    def get_element_guid(self, element, guid_param_spec=None):
        # type: (DB.Element, object) -> str
        """
        Get GUID from a Revit element using a configurable parameter source.

        Args:
            element (DB.Element): Revit element
            guid_param_spec (object): One of:
                - DB.BuiltInParameter (enum)
                - str (parameter name, e.g., shared parameter)
                - DB.ParameterDefinition / DB.Definition
                - None (defaults to DB.BuiltInParameter.IFC_GUID)

        Returns:
            str: GUID string or empty string if not found
        """
        try:
            if not element or not element.IsValidObject:
                return ""

            param_obj = None
            # Default to IFC_GUID for backward compatibility
            if guid_param_spec is None:
                try:
                    param_obj = element.get_Parameter(DB.BuiltInParameter.IFC_GUID)
                except Exception:
                    param_obj = None
            else:
                # Handle different specification types
                try:
                    if isinstance(guid_param_spec, str):
                        # Named parameter (shared/instance)
                        param_obj = element.LookupParameter(guid_param_spec)
                    else:
                        # BuiltInParameter enum or Definition/ParameterDefinition
                        param_obj = element.get_Parameter(guid_param_spec)
                except Exception:
                    param_obj = None

            if param_obj and param_obj.HasValue:
                try:
                    value = param_obj.AsString()
                except Exception:
                    try:
                        value = param_obj.AsValueString()
                    except Exception:
                        value = None
                return value.strip() if value else ""
            return ""
        except Exception as ex:
            Debug.WriteLine("Error getting GUID from element {}: {}".format(
                element.Id.Value if element else "None", str(ex)))
            return ""

    def build_guid_element_map(self, elements, guid_param_spec=None):
        # type: (list[DB.Element], object) -> tuple[dict[str, list[DB.Element]], dict[str, DB.Element]]
        """
        Build mapping of GUIDs to elements, tracking duplicates

        Args:
            elements (list[DB.Element]): List of Revit elements
            guid_param_spec (object): Parameter spec for extracting GUIDs (see get_element_guid)

        Returns:
            tuple: (guid_to_elements_map, guid_to_first_element_map)
        """
        guid_to_elements = defaultdict(list)
        guid_to_first_element = {}

        for element in elements:
            guid = self.get_element_guid(element, guid_param_spec)
            if guid:  # Only process elements with valid GUIDs
                guid_to_elements[guid].append(element)
                if guid not in guid_to_first_element:
                    guid_to_first_element[guid] = element

        return dict(guid_to_elements), guid_to_first_element

    def find_duplicates(self, guid_to_elements_map):
        # type: (dict[str, list[DB.Element]]) -> dict[str, list[DB.Element]]
        """
        Find GUIDs that appear multiple times
        
        Args:
            guid_to_elements_map (dict): Mapping of GUIDs to element lists
            
        Returns:
            dict: GUIDs with duplicate elements
        """
        duplicates = {}
        for guid, elements in guid_to_elements_map.items():
            if len(elements) > 1:
                duplicates[guid] = elements
        return duplicates
    
    def validate_guid_sets(self, arc_elements, mass_elements, arc_name=constant.DEFAULT_ARC_NAME, mass_name=constant.DEFAULT_MASS_NAME,
                           arc_guid_param=None, mass_guid_param=None):
        # type: (list[DB.Element], list[DB.Element], str, str, object, object) -> GuidValidationResult
        """
        Validate GUIDs between two sets of elements

        Args:
            arc_elements (list[DB.Element]): ARC elements (e.g., beams from link)
            mass_elements (list[DB.Element]): Mass elements in current document
            arc_name (str): Name for ARC set
            mass_name (str): Name for Mass set
            arc_guid_param (object): Param spec for ARC set GUIDs (default IFC_GUID)
            mass_guid_param (object): Param spec for Mass set GUIDs (default IFC_GUID)

        Returns:
            GuidValidationResult: Comprehensive validation results
        """
        result = GuidValidationResult(arc_name, mass_name)

        # Build GUID mappings with configurable parameter specs
        arc_guid_to_elements, arc_guid_to_first = self.build_guid_element_map(arc_elements, arc_guid_param)
        mass_guid_to_elements, mass_guid_to_first = self.build_guid_element_map(mass_elements, mass_guid_param)

        # Set statistics
        result.total_arc_elements = len(arc_elements)
        result.total_mass_elements = len(mass_elements)
        result.total_arc_guids = len(arc_guid_to_first)
        result.total_mass_guids = len(mass_guid_to_first)

        # Find duplicates
        result.duplicate_in_arc = self.find_duplicates(arc_guid_to_elements)
        result.duplicate_in_mass = self.find_duplicates(mass_guid_to_elements)
        
        # Get all unique GUIDs
        arc_guids = set(arc_guid_to_first.keys())
        mass_guids = set(mass_guid_to_first.keys())
        
        # Find correct matches (GUIDs present in both sets)
        common_guids = arc_guids.intersection(mass_guids)
        for guid in common_guids:
            arc_element = arc_guid_to_first[guid]
            mass_element = mass_guid_to_first[guid]
            result.correct_matches.append((arc_element, mass_element, guid))
        
        # Find missing in mass (GUIDs in ARC but not in Mass)
        missing_guids = arc_guids - mass_guids
        for guid in missing_guids:
            arc_element = arc_guid_to_first[guid]
            result.missing_in_mass.append((arc_element, guid))
        
        # Find extra in mass (GUIDs in Mass but not in ARC)
        extra_guids = mass_guids - arc_guids
        for guid in extra_guids:
            mass_element = mass_guid_to_first[guid]
            result.extra_in_mass.append((mass_element, guid))
        
        # Note: Swapped pairs would require additional logic based on element properties
        # For now, we consider elements with matching GUIDs as correct matches
        # Swapped detection could be added based on element type, location, or other properties
        
        return result
    
    def detect_potential_swaps(self, validation_result, arc_elements, mass_elements):
        # type: (GuidValidationResult, list[DB.Element], list[DB.Element]) -> None
        """
        Detect potential GUID swaps based on element properties
        This is an advanced feature that could compare element locations, types, etc.
        
        Args:
            validation_result (GuidValidationResult): Current validation result to update
            arc_elements (list[DB.Element]): ARC elements
            mass_elements (list[DB.Element]): Mass elements
        """
        # This is a placeholder for advanced swap detection
        # Could be implemented by comparing:
        # - Element locations (if elements are in same coordinate system)
        # - Element types/categories
        # - Element parameters
        # - Geometric properties
        
        # For now, we'll leave this as a future enhancement
        pass
    
    def generate_report(self, validation_result, include_details=True):
        # type: (GuidValidationResult, bool) -> str
        """
        Generate comprehensive validation report
        
        Args:
            validation_result (GuidValidationResult): Validation results
            include_details (bool): Include detailed element information
            
        Returns:
            str: Formatted validation report
        """
        lines = []
        lines.append("=" * 60)
        lines.append("GUID VALIDATION REPORT")
        lines.append("=" * 60)
        lines.append("")
        
        # Summary section
        lines.append("SUMMARY:")
        lines.append("-" * 20)
        lines.append("Reference Set ({}): {} elements ({} unique GUIDs)".format(
            validation_result.arc_name,
            validation_result.total_arc_elements,
            validation_result.total_arc_guids
        ))
        lines.append("Target Set ({}): {} elements ({} unique GUIDs)".format(
            validation_result.mass_name,
            validation_result.total_mass_elements,
            validation_result.total_mass_guids
        ))
        lines.append("")
        
        summary = validation_result.summary
        lines.append("VALIDATION RESULTS:")
        lines.append("-" * 20)
        lines.append("✓ Correct Matches: {}".format(summary[constant.CORRECT_MATCHED]))
        lines.append("✗ Missing in {}: {}".format(validation_result.mass_name, summary[constant.MISSING_IN_MASS]))
        lines.append("✗ Extra in {}: {}".format(validation_result.mass_name, summary[constant.EXTRA_IN_MASS]))
        lines.append("✗ Duplicates in {}: {}".format(validation_result.arc_name, summary[constant.DUPLICATE_IN_ARC]))
        lines.append("✗ Duplicates in {}: {}".format(validation_result.mass_name, summary[constant.DUPLICATE_IN_MASS]))
        lines.append("✗ Potential Swaps: {}".format(summary[constant.SWAPPED_PAIRS]))
        lines.append("")
        
        # Status
        if validation_result.has_issues:
            lines.append("STATUS: ❌ VALIDATION FAILED - Issues found")
        else:
            lines.append("STATUS: ✅ VALIDATION PASSED - All GUIDs match correctly")
        lines.append("")
        
        if include_details:
            # Detailed results
            if validation_result.missing_in_mass:
                lines.append("MISSING IN {} ({}):".format(validation_result.mass_name.upper(), len(validation_result.missing_in_mass)))
                lines.append("-" * 30)
                for element, guid in validation_result.missing_in_mass:
                    element_info = self._get_element_info(element)
                    lines.append("  • GUID: {} | Element: {}".format(guid, element_info))
                lines.append("")
            
            if validation_result.extra_in_mass:
                lines.append("EXTRA IN {} ({}):".format(validation_result.mass_name.upper(), len(validation_result.extra_in_mass)))
                lines.append("-" * 30)
                for element, guid in validation_result.extra_in_mass:
                    element_info = self._get_element_info(element)
                    lines.append("  • GUID: {} | Element: {}".format(guid, element_info))
                lines.append("")
            
            if validation_result.duplicate_in_arc:
                lines.append("DUPLICATES IN {} ({}):".format(validation_result.arc_name.upper(), len(validation_result.duplicate_in_arc)))
                lines.append("-" * 30)
                for guid, elements in validation_result.duplicate_in_arc.items():
                    lines.append("  • GUID: {} ({} duplicates)".format(guid, len(elements)))
                    for element in elements:
                        element_info = self._get_element_info(element)
                        lines.append("    - Element: {}".format(element_info))
                lines.append("")
            
            if validation_result.duplicate_in_mass:
                lines.append("DUPLICATES IN {} ({}):".format(validation_result.mass_name.upper(), len(validation_result.duplicate_in_mass)))
                lines.append("-" * 30)
                for guid, elements in validation_result.duplicate_in_mass.items():
                    lines.append("  • GUID: {} ({} duplicates)".format(guid, len(elements)))
                    for element in elements:
                        element_info = self._get_element_info(element)
                        lines.append("    - Element: {}".format(element_info))
                lines.append("")
            
            if validation_result.correct_matches:
                lines.append("CORRECT MATCHES ({}):".format(len(validation_result.correct_matches)))
                lines.append("-" * 30)
                for ref_element, target_element, guid in validation_result.correct_matches[:10]:  # Show first 10
                    ref_info = self._get_element_info(ref_element)
                    target_info = self._get_element_info(target_element)
                    lines.append("  • GUID: {}".format(guid))
                    lines.append("    {}: {}".format(validation_result.arc_name, ref_info))
                    lines.append("    {} : {}".format(validation_result.mass_name, target_info))
                
                if len(validation_result.correct_matches) > 10:
                    lines.append("  ... and {} more matches".format(len(validation_result.correct_matches) - 10))
                lines.append("")
        
        lines.append("=" * 60)
        return "\n".join(lines)
    
    def _get_element_info(self, element):
        # type: (DB.Element) -> str
        """
        Get descriptive information about an element
        
        Args:
            element (DB.Element): Revit element
            
        Returns:
            str: Element description
        """
        try:
            if not element or not element.IsValidObject:
                return "Invalid Element"
            
            element_id = element.Id.Value
            category_name = element.Category.Name if element.Category else "Unknown Category"
            
            # Try to get family and type
            family_and_type = ""
            try:
                family_param = element.get_Parameter(DB.BuiltInParameter.ELEM_FAMILY_AND_TYPE_PARAM)
                if family_param and family_param.HasValue:
                    family_and_type = family_param.AsValueString()
            except Exception:
                pass
            
            return "ID:{} | {} | {}".format(element_id, category_name, family_and_type)
            
        except Exception as ex:
            Debug.WriteLine("Error getting element info: {}".format(str(ex)))
            return "ID:{} | Error getting info".format(element.Id.Value if element else "Unknown")
    
    def export_validation_results_to_dict(self, validation_result):
        # type: (GuidValidationResult) -> dict
        """
        Export validation results to dictionary for further processing
        
        Args:
            validation_result (GuidValidationResult): Validation results
            
        Returns:
            dict: Structured validation data
        """
        return {
            constant.SUMMARY: validation_result.summary,
            constant.ARC_SET_NAME: validation_result.arc_name,
            constant.MASS_SET_NAME: validation_result.mass_name,
            constant.HAS_ISSUES: validation_result.has_issues,
            constant.CORRECT_MATCHED: [
                {
                    constant.GUID_KEY: guid,
                    constant.ARC_ELEMENT_ID_KEY: ref_elem.Id.Value,
                    constant.MASS_ELEMENT_ID_KEY: target_elem.Id.Value,
                    constant.ARC_INFO_KEY: self._get_element_info(ref_elem),
                    constant.MASS_INFO_KEY: self._get_element_info(target_elem)
                }
                for ref_elem, target_elem, guid in validation_result.correct_matches
            ],
            constant.MISSING_IN_MASS: [
                {
                    constant.GUID_KEY: guid,
                    constant.ELEMENT_ID_KEY: elem.Id.Value,
                    constant.ELEMENT_INFO_KEY: self._get_element_info(elem)
                }
                for elem, guid in validation_result.missing_in_mass
            ],
            constant.EXTRA_IN_MASS: [
                {
                    constant.GUID_KEY: guid,
                    constant.ELEMENT_ID_KEY: elem.Id.Value,
                    constant.ELEMENT_INFO_KEY: self._get_element_info(elem)
                }
                for elem, guid in validation_result.extra_in_mass
            ],
            constant.DUPLICATE_IN_ARC: {
                guid: [
                    {
                        constant.ELEMENT_ID_KEY: elem.Id.Value,
                        constant.ELEMENT_INFO_KEY: self._get_element_info(elem)
                    }
                    for elem in elements
                ]
                for guid, elements in validation_result.duplicate_in_arc.items()
            },
            constant.DUPLICATE_IN_MASS: {
                guid: [
                    {
                        constant.ELEMENT_ID_KEY: elem.Id.Value,
                        constant.ELEMENT_INFO_KEY: self._get_element_info(elem)
                    }
                    for elem in elements
                ]
                for guid, elements in validation_result.duplicate_in_mass.items()
            }
        }

    # ---------- Excel export helpers (MiniExcel) ----------
    def _ordered_row(self, pairs):
        """Create OrderedDictionary row from (key, value) pairs."""
        row = OrderedDictionary()
        for k, v in pairs:
            row[k] = v
        return row

    def _build_missing_sheet(self, validation_result):
        """Build rows for Missing sheet (Mass missing for existing ARC)."""
        rows = []
        for elem, guid in validation_result.missing_in_mass:
            rows.append(self._ordered_row([
                (constant.ARC_GUID_HEADER, guid),
                (constant.ARC_ELEMENT_ID, elem.Id.Value if elem else ""),
                (constant.ARC_INFO_HEADER, self._get_element_info(elem)),
            ]))
        return rows

    def _build_extra_sheet(self, validation_result):
        """Build rows for Extra sheet (Mass exists without corresponding ARC)."""
        rows = []
        for elem, guid in validation_result.extra_in_mass:
            rows.append(self._ordered_row([
                (constant.MASS_GUID_HEADER, guid),
                (constant.MASS_ELEMENT_ID, elem.Id.Value if elem else ""),
                (constant.MASS_INFO_HEADER, self._get_element_info(elem)),
            ]))
        return rows

    def _build_duplicates_and_swapped_sheet(self, validation_result):
        """Build rows combining duplicates (both sets) and swapped pairs into one sheet."""
        rows = []
        # Duplicate in ARC
        for guid, elements in validation_result.duplicate_in_arc.items():
            for elem in elements:
                rows.append(self._ordered_row([
                    (constant.TYPE, constant.DUPLICATE),
                    (constant.SOURCE, validation_result.arc_name),
                    (constant.GUID, guid),
                    (constant.ELEMENT_ID, elem.Id.Value if elem else ""),
                    (constant.ELEMENT_INFO, self._get_element_info(elem)),
                ]))
        # Duplicate in Mass
        for guid, elements in validation_result.duplicate_in_mass.items():
            for elem in elements:
                rows.append(self._ordered_row([
                    (constant.TYPE, constant.DUPLICATE),
                    (constant.SOURCE, validation_result.mass_name),
                    (constant.GUID, guid),
                    (constant.ELEMENT_ID, elem.Id.Value if elem else ""),
                    (constant.ELEMENT_INFO, self._get_element_info(elem)),
                ]))
        # Swapped pairs (if any provided by future detection)
        for ref_elem, tgt_elem, guid in validation_result.swapped_pairs:
            rows.append(self._ordered_row([
                (constant.TYPE, constant.SWAPPED),
                (constant.GUID, guid),
                (constant.ARC_ELEMENT_ID_KEY, ref_elem.Id.Value if ref_elem else ""),
                (constant.ARC_INFO_KEY, self._get_element_info(ref_elem)),
                (constant.MASS_ELEMENT_ID_KEY, tgt_elem.Id.Value if tgt_elem else ""),
                (constant.MASS_INFO_KEY, self._get_element_info(tgt_elem)),
            ]))
        return rows

    def _build_match_mass_sheet(self, validation_result):
        """Build rows for Match Mass sheet: matched ARC↔Mass pairs."""
        rows = []
        for ref_elem, tgt_elem, guid in validation_result.correct_matches:
            rows.append(self._ordered_row([
                (constant.GUID, guid),
                (constant.MASS_ID, tgt_elem.Id.Value if tgt_elem else ""),
                (constant.ARC_ID, ref_elem.Id.Value if ref_elem else ""),
                (constant.MASS_INFO, self._get_element_info(tgt_elem)),
                (constant.ARC_INFO, self._get_element_info(ref_elem)),
            ]))
        return rows

    def _build_total_sheet(self, validation_result):
        """Build rows for Total sheet with all GUIDs and validation issue label."""
        rows = []
        # Helper to add a row with safe empty values
        def add_row(guid, arc_elem, mass_elem, issue):
            rows.append(self._ordered_row([
                (constant.ARC_ID, arc_elem.Id.Value if arc_elem else ""),
                (constant.MASS_ID, mass_elem.Id.Value if mass_elem else ""),
                (constant.GUID, guid or ""),
                (constant.ARC_INFO, self._get_element_info(arc_elem) if arc_elem else ""),
                (constant.MASS_INFO, self._get_element_info(mass_elem) if mass_elem else ""),
                (constant.GUID_VALIDATION, issue or ""),
            ]))

        # Correct matches
        for ref_elem, tgt_elem, guid in validation_result.correct_matches:
            add_row(guid, ref_elem, tgt_elem, constant.MATCHED)

        # Missing masses (ARC present, Mass missing)
        for ref_elem, guid in validation_result.missing_in_mass:
            add_row(guid, ref_elem, None, constant.MISSING)

        # Extra masses (Mass present, ARC missing)
        for tgt_elem, guid in validation_result.extra_in_mass:
            add_row(guid, None, tgt_elem, constant.EXTRA)

        # Duplicate ARC GUIDs
        for guid, elements in validation_result.duplicate_in_arc.items():
            for elem in elements:
                add_row(guid, elem, None, constant.DUPLICATE_ARC_GUID)

        # Duplicate Mass GUIDs
        for guid, elements in validation_result.duplicate_in_mass.items():
            for elem in elements:
                add_row(guid, None, elem, constant.DUPLICATE_MASS_GUID)

        # Swapped (if any supplied)
        for ref_elem, tgt_elem, guid in validation_result.swapped_pairs:
            add_row(guid, ref_elem, tgt_elem, constant.POTENTIAL_SWAPPED)

        return rows

    def build_excel_data(self, validation_result):
        """Build a multi-sheet Dictionary[str, object] for MiniExcel SaveAs.

        Sheets:
            - Missing
            - Extra
            - DuplicatesAndSwapped (combined in one sheet as requested)
            - Match Mass (matched ARC↔Mass pairs)
            - Total (all GUIDs with ARC/Mass and issue)
        """
        sheets = Dictionary[str, object]()
        # sheets["Mass Missing"] = self._build_missing_sheet(validation_result)
        # sheets["Mass Extra"] = self._build_extra_sheet(validation_result)
        # sheets["Match Mass"] = self._build_match_mass_sheet(validation_result)
        # sheets["DuplicatesAndSwapped"] = self._build_duplicates_and_swapped_sheet(validation_result)
        sheets[constant.TOTAL_SHEET] = self._build_total_sheet(validation_result)
        return sheets

    def export_validation_to_excel(self, validation_result, file_path=None):
        """Export validation results to an Excel file with three sheets.

        If file_path is None, a save dialog will be shown via DCMvn.
        Returns True on success, False otherwise.
        """
        try:
            if not MINIEXCEL_AVAILABLE:
                alert("MiniExcel is not available. Cannot export to Excel.", warn_icon=True)
                return False

            # Build data
            sheets = self.build_excel_data(validation_result)

            # Get a path if not provided
            if not file_path:
                try:
                    from DCMvn.io import save_excel_file
                    file_path = save_excel_file(title="Save GUID Validation Report")
                except Exception as ex:
                    Debug.WriteLine("Failed to open save dialog: {}".format(str(ex)))
                    file_path = None

            if not file_path:
                return False

            # Overwrite if exists
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as ex:
                Debug.WriteLine("Failed to remove existing file: {}".format(str(ex)))

            # Save
            MiniExcel.SaveAs(file_path, sheets)
            return True
        except Exception as ex:
            Debug.WriteLine("Export to Excel failed: {}".format(str(ex)))
            return False

    # ---------- pyRevit HTML report ----------
    def _html_escape(self, text):
        try:
            if text is None:
                return ""
            # Minimal HTML escaping
            return (str(text)
                    .replace("&", "&amp;")
                    .replace("<", "&lt;")
                    .replace(">", "&gt;"))
        except Exception:
            return ""

    def print_html_report(self, validation_result, output, current_document):
        """Print a polished HTML report in pyRevit output with linkify zoom.

        current_document is used to linkify only elements that belong to it
        (typically the Mass set). Reference set (e.g., beams in link) is
        displayed without linkify.
        """
        try:
            # Header
            output.print_html("<h2>GUID Validation Report</h2>")

            # Summary cards
            summary = validation_result.summary
            summary_html = (
                "<div style='display:flex;gap:16px;margin:8px 0 16px 0;'>"
                "<div style='padding:8px 12px;border:1px solid #ddd;border-radius:6px;'>"
                "<b>Correct</b><br/>{}</div>"
                "<div style='padding:8px 12px;border:1px solid #ddd;border-radius:6px;'>"
                "<b>Missing (Mass)</b><br/>{}</div>"
                "<div style='padding:8px 12px;border:1px solid #ddd;border-radius:6px;'>"
                "<b>Extra (Mass)</b><br/>{}</div>"
                "<div style='padding:8px 12px;border:1px solid #ddd;border-radius:6px;'>"
                "<b>Dup Arcs</b><br/>{}</div>"
                "<div style='padding:8px 12px;border:1px solid #ddd;border-radius:6px;'>"
                "<b>Dup Masses</b><br/>{}</div>"
                "</div>"
            ).format(
                summary[constant.CORRECT_MATCHED],
                summary[constant.MISSING_IN_MASS],
                summary[constant.EXTRA_IN_MASS],
                len(validation_result.duplicate_in_arc),
                len(validation_result.duplicate_in_mass)
            )
            output.print_html(summary_html)

            # Mass Missing (ARC base)
            output.print_html("<h3>Mass Missing for Existing ARCs</h3>")
            if validation_result.missing_in_mass:
                rows = [
                    "<tr><th style='text-align:left'>Arc GUID</th><th style='text-align:left'>Arc Element Id</th><th style='text-align:left'>Arc Info</th></tr>"
                ]
                for elem, guid in validation_result.missing_in_mass:
                    beam_id = elem.Id.Value if elem else ""
                    rows.append(
                        "<tr><td>{}</td><td>{}</td><td>{}</td></tr>".format(
                            self._html_escape(guid),
                            self._html_escape(beam_id),
                            self._html_escape(self._get_element_info(elem))
                        )
                    )
                table = "<table style='border-collapse:collapse;width:100%'>{}".format("".join(rows)) + "</table>"
                output.print_html(table)
            else:
                output.print_html("<i>No missing masses detected.</i>")

            # Mass Extra (orphan masses)
            output.print_html("<h3>Mass Extra (No Corresponding Arc)</h3>")
            if validation_result.extra_in_mass:
                rows = [
                    "<tr><th style='text-align:left'>Mass GUID</th><th style='text-align:left'>Mass Element</th><th style='text-align:left'>Mass Info</th></tr>"
                ]
                for elem, guid in validation_result.extra_in_mass:
                    if elem and elem.Document.Equals(current_document):
                        link = output.linkify(elem.Id)
                    else:
                        link = self._html_escape(elem.Id.Value if elem else "")
                    rows.append(
                        "<tr><td>{}</td><td>{}</td><td>{}</td></tr>".format(
                            self._html_escape(guid),
                            link,
                            self._html_escape(self._get_element_info(elem))
                        )
                    )
                table = "<table style='border-collapse:collapse;width:100%'>{}".format("".join(rows)) + "</table>"
                output.print_html(table)
            else:
                output.print_html("<i>No extra masses detected.</i>")

            # Matched Mass (ARC ↔ Mass)
            output.print_html("<h3>Matched Mass (ARC ↔ Mass)</h3>")
            if validation_result.correct_matches:
                rows = [
                    "<tr><th style='text-align:left'>GUID</th><th style='text-align:left'>ARC</th><th style='text-align:left'>Mass</th></tr>"
                ]
                # Limit to first 50 rows for readability
                limit = 50
                for idx, (ref_elem, tgt_elem, guid) in enumerate(validation_result.correct_matches):
                    if idx >= limit:
                        break
                    if tgt_elem and tgt_elem.Document.Equals(current_document):
                        mass_link = output.linkify(tgt_elem.Id)
                    else:
                        mass_link = self._html_escape(tgt_elem.Id.Value if tgt_elem else "")
                    rows.append(
                        "<tr><td>{}</td><td>{}</td><td>{}</td></tr>".format(
                            self._html_escape(guid),
                            self._html_escape(self._get_element_info(ref_elem)),
                            mass_link
                        )
                    )
                table = "<table style='border-collapse:collapse;width:100%'>{}".format("".join(rows)) + "</table>"
                output.print_html(table)
                if len(validation_result.correct_matches) > limit:
                    output.print_html("<i>Showing first {} matches out of {}.</i>".format(limit, len(validation_result.correct_matches)))
            else:
                output.print_html("<i>No matched pairs detected.</i>")

            # Duplicates & Swapped
            output.print_html("<h3>Duplicates and Swapped</h3>")

            # Duplicate Masses
            if validation_result.duplicate_in_mass:
                output.print_html("<h4>Duplicate Mass GUIDs</h4>")
                rows = [
                    "<tr><th style='text-align:left'>GUID</th><th style='text-align:left'>Mass Element</th><th style='text-align:left'>Mass Info</th></tr>"
                ]
                for guid, elements in validation_result.duplicate_in_mass.items():
                    for elem in elements:
                        if elem and elem.Document.Equals(current_document):
                            link = output.linkify(elem.Id)
                        else:
                            link = self._html_escape(elem.Id.Value if elem else "")
                        rows.append(
                            "<tr><td>{}</td><td>{}</td><td>{}</td></tr>".format(
                                self._html_escape(guid),
                                link,
                                self._html_escape(self._get_element_info(elem))
                            )
                        )
                table = "<table style='border-collapse:collapse;width:100%'>{}".format("".join(rows)) + "</table>"
                output.print_html(table)

            # Duplicate ARCs (not linkable)
            if validation_result.duplicate_in_arc:
                output.print_html("<h4>Duplicate Arc GUIDs</h4>")
                rows = [
                    "<tr><th style='text-align:left'>GUID</th><th style='text-align:left'>ARC Element Id</th><th style='text-align:left'>ARC Info</th></tr>"
                ]
                for guid, elements in validation_result.duplicate_in_arc.items():
                    for elem in elements:
                        rows.append(
                            "<tr><td>{}</td><td>{}</td><td>{}</td></tr>".format(
                                self._html_escape(guid),
                                self._html_escape(elem.Id.Value if elem else ""),
                                self._html_escape(self._get_element_info(elem))
                            )
                        )
                table = "<table style='border-collapse:collapse;width:100%'>{}".format("".join(rows)) + "</table>"
                output.print_html(table)

            # Swapped (if any)
            if validation_result.swapped_pairs:
                output.print_html("<h4>Potential Swapped GUIDs</h4>")
                rows = [
                    "<tr><th style='text-align:left'>GUID</th><th style='text-align:left'>ARC</th><th style='text-align:left'>Mass</th></tr>"
                ]
                for ref_elem, tgt_elem, guid in validation_result.swapped_pairs:
                    if tgt_elem and tgt_elem.Document.Equals(current_document):
                        mass_link = output.linkify(tgt_elem.Id)
                    else:
                        mass_link = self._html_escape(tgt_elem.Id.Value if tgt_elem else "")
                    rows.append(
                        "<tr><td>{}</td><td>{}</td><td>{}</td></tr>".format(
                            self._html_escape(guid),
                            self._html_escape(self._get_element_info(ref_elem)),
                            mass_link
                        )
                    )
                table = "<table style='border-collapse:collapse;width:100%'>{}".format("".join(rows)) + "</table>"
                output.print_html(table)

        except Exception as ex:
            Debug.WriteLine("print_html_report error: {}".format(str(ex)))
