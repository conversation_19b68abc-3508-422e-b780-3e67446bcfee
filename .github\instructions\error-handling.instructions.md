---
applyTo: "**/*.py"
---

# Error <PERSON>ling and Validation Patterns

## Document Validation Pattern
Always validate document requirements before proceeding:
```python
def validate_document_requirements():
    """Validate document has required elements before proceeding."""
    try:
        physical_filter = get_element_physical_filter(doc)
        elements = (DB.FilteredElementCollector(doc)
                   .WhereElementIsNotElementType()
                   .WherePasses(physical_filter)
                   .ToElements())
        
        if not elements or len(elements) == 0:
            alert("Document validation failed.\n\nPlease ensure the document contains required elements.", 
                  "Validation Error")
            return False
        return True
    except Exception as e:
        alert("An error occurred during validation:\n\n" + str(e), "Validation Error")
        return False
```

## Exception Handling Hierarchy
Use this hierarchy for exception handling:
1. **Specific Revit exceptions**: Catch Autodesk.Revit.Exceptions first
2. **General exceptions**: Catch System.Exception for .NET operations
3. **Python exceptions**: Catch generic Exception last

```python
try:
    # Revit API operations
    pass
except DB.Exceptions.ArgumentException as e:
    # Handle specific Revit errors
    alert("Invalid parameter: {}".format(str(e)), "Parameter Error")
except System.Exception as e:
    # Handle .NET exceptions
    Debug.WriteLine("System error: {}".format(str(e)))
    alert("A system error occurred.", "Error")
except Exception as e:
    # Handle Python exceptions
    Debug.WriteLine("Unexpected error: {}".format(str(e)))
    alert("An unexpected error occurred: {}".format(str(e)), "Error")
```

## Element Validation
Always validate elements before operations:
```python
def validate_element(element):
    """Validate element is valid and accessible."""
    if not element:
        return False, "Element is None"
    
    if not element.IsValidObject:
        return False, "Element is not valid"
    
    return True, None

# Usage
is_valid, error_msg = validate_element(element)
if not is_valid:
    Debug.WriteLine("Element validation failed: {}".format(error_msg))
    return
```

## Transaction Management
Always wrap modifications in transactions:
```python
def safe_modify_elements(doc, modify_action):
    """Safely modify elements with proper transaction handling."""
    with DB.Transaction(doc, "Modify Elements") as t:
        t.Start()
        try:
            modify_action()
            t.Commit()
            return True
        except Exception as e:
            t.RollBack()
            Debug.WriteLine("Transaction failed: {}".format(str(e)))
            alert("Failed to modify elements: {}".format(str(e)), "Error")
            return False
```

## Logging and Debug Output
Use consistent logging patterns:
```python
# For development debugging
Debug.WriteLine("Operation started: {}".format(operation_name))

# For tracing execution flow
Trace.WriteLine("Processing {} elements".format(len(elements)))

# For user notifications
alert("Operation completed successfully", "Success")
```

## Resource Management
Properly dispose of resources:
```python
def process_geometry(element):
    """Process element geometry with proper resource management."""
    options = DB.Options()
    geometry = element.get_Geometry(options)
    
    try:
        # Process geometry
        return result
    finally:
        # Cleanup if needed
        if geometry:
            geometry.Dispose()
```
