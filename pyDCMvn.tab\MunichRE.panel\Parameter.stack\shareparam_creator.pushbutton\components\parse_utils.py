# coding: utf-8
"""Parser for parameters"""
from collections import namedtuple

from DCMvn.core import DB, ApplicationServices, REVIT_VERSION
from DCMvn.core.framework import Enum
from MunichRE.excel_reader import RegisterExcelReader

from .constant import SP_NAME
from .register_parameter import RegisterParameter


ParamDef = namedtuple("ParamDef", ["name", "categories", "varies_across_groups", "group",
                                   "param_type", "unit_type", "is_visible", "param_element", "guid",
                                   "is_instance", "built_in_categories"])

SharedParamDef = namedtuple("SharedParamDef", ["name", "description", "guid", "group", "unit_type", "user_modifiable"])


def project_parameters_paser(app, shared_parameters_only=False):
    # type: (ApplicationServices.Application, bool) -> list[ParamDef]
    """Get all project parameters
    
    Args:
        shared_parameters_only: only get shared parameters
        app: Revit application
    
    Returns:
        list: list of namedtuples containing parameter name, categories, varies across groups, parameter group,
        parameter type, unit type, visibility, parameter element, guid value, is instance, and built-in categories
    """
    document = app.ActiveUIDocument.Document

    param_defs = []
    iterator = document.ParameterBindings.ForwardIterator()
    while iterator.MoveNext():
        # Parmeter element
        elems = document.GetElement(iterator.Key.Id)  # type: DB.ParameterElement

        # Guid
        if elems.GetType().ToString() == "Autodesk.Revit.DB.SharedParameterElement":
            guid = elems.GuidValue
        else:
            guid = None

        if shared_parameters_only and not guid:
            continue

        # Get parameter information
        varies_across_group = iterator.Key.VariesAcrossGroups
        name = iterator.Key.Name
        
        if REVIT_VERSION <= 2023:
            parameter_group = iterator.Key.ParameterGroup  # type: DB.BuiltInParameterGroup
        else:
            parameter_group = iterator.Key.GetGroupTypeId()  # type: DB.ForgeTypeId 
            
        if REVIT_VERSION > 2021:
            param_type = iterator.Key.GetDataType()  # type: DB.ForgeTypeId
            unit_type = iterator.Key.GetDataType()  # type: DB.ForgeTypeId
        else:
            param_type = iterator.Key.ParameterType  # type: DB.ParameterType
            unit_type = iterator.Key.UnitType  # type: DB.UnitType

        is_visible = iterator.Key.Visible

        # instance or type
        if iterator.Current.GetType().ToString() == "Autodesk.Revit.DB.InstanceBinding":
            is_instance = True
        else:
            is_instance = False

        # Categories
        categories = []
        builtincats = []
        for cat in iterator.Current.Categories:
            categories.append(cat)
            if REVIT_VERSION >= 2024:
                builtincats.append(Enum.ToObject(DB.BuiltInCategory, cat.Id.Value))
            else:
                builtincats.append(Enum.ToObject(DB.BuiltInCategory, cat.Id.IntegerValue))

        param_defs.append(ParamDef(name=name, categories=categories, varies_across_groups=varies_across_group,
                                   group=parameter_group, param_type=param_type, unit_type=unit_type,
                                   is_visible=is_visible,
                                   param_element=elems, guid=guid, is_instance=is_instance,
                                   built_in_categories=builtincats))

    return param_defs


def shared_parameters_parser(app, shared_parameters_path):
    # type: (ApplicationServices.Application, str) -> list[SharedParamDef]
    """
    Parse shared parameters file
    Args:
        app: Revit application
        shared_parameters_path: path to the shared parameters file

    Returns:
        list: list of namedtuples containing parameter name, guid, group, and unit type

    """

    if app.SharedParametersFilename != shared_parameters_path:
        app.SharedParametersFilename = shared_parameters_path
    sp_file = app.OpenSharedParameterFile()

    param_defs = []
    sp_groups = sp_file.Groups
    for d_group in sp_groups:
        for p_def in d_group.Definitions:
            if REVIT_VERSION > 2021:
                unit_type = p_def.GetDataType()  # type: DB.ForgeTypeId
            else:
                unit_type = p_def.UnitType  # type: DB.UnitType
            param_defs.append(SharedParamDef(name=p_def.Name,
                                             description=p_def.Description,
                                             guid=p_def.GUID,
                                             group=d_group.Name,
                                             user_modifiable=p_def.UserModifiable,
                                             unit_type=unit_type))

    return param_defs


def register_parameters_parser(excel_file_path, sheet_name, start_cell):
    # type: (str, str, str) -> list[RegisterParameter]
    """
    Parse the register list file
    Args:
        start_cell: start cell in sheet
        excel_file_path: path to the Excel file
        sheet_name: name of the sheet

    Returns:
        list[SharedParameter]: list of shared parameters
    """
    regist_data = RegisterExcelReader(excel_file_path, sheet_name, start_cell).data

    return [RegisterParameter(oj) for oj in regist_data if getattr(oj, SP_NAME) is not None
            and getattr(oj, SP_NAME) != ""]
