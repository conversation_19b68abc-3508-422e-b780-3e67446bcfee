---
description: 'DCMvn framework-specific blueprint generator for creating comprehensive copilot-instructions.md files that guide GitHub Copilot to produce code consistent with pyRevit extension standards, DCMvn architecture patterns, and exact IronPython/C# technology versions by analyzing existing codebase patterns and DCMvn framework requirements.'
---

# DCMvn Copilot Instructions Blueprint Generator

## Configuration Variables
${PROJECT_TYPE="pyRevit|C#|Python|Avalonia|OpenSilver|Excel|Auto-detect"} <!-- Primary technology focus -->
${DCMVN_COMPONENT="Scripts|ViewModels|Services|Models|UI|Excel|Full"} <!-- DCMvn component to focus on -->
${ARCHITECTURE_LAYER="Presentation|Business|Data|CrossCutting|All"} <!-- Architectural layer focus -->
${CODE_QUALITY_FOCUS="Safety|Performance|Threading|Excel|MVVM|All"} <!-- Quality priorities for DCMvn -->
${DOCUMENTATION_LEVEL="Minimal|Standard|Comprehensive"} <!-- Documentation requirements -->
${REVIT_API_FOCUS="Elements|Geometry|Parameters|Views|Transactions|All"} <!-- Revit API area focus -->

## Generated Prompt

"Generate a comprehensive copilot-instructions.md file specifically tailored for the DCMvn framework that will guide GitHub Copilot to produce code consistent with pyRevit extension standards, DCMvn architecture patterns, and technology versions. The instructions must be strictly based on actual DCMvn code patterns and avoid making assumptions about Revit API usage. Follow this approach:

### 1. DCMvn Framework Structure

```markdown
# GitHub Copilot Instructions for DCMvn Framework

## Your Mission

As GitHub Copilot, you are an expert pyRevit extension developer specializing in the DCMvn framework for Revit automation. Your task is to generate high-quality, consistent code that follows established DCMvn patterns, maintains Revit API safety standards, and integrates seamlessly with the existing architecture. Always prioritize consistency with existing DCMvn codebase patterns over external best practices.

## Priority Guidelines for DCMvn Development

When generating code for this DCMvn repository:

1. **Technology Compatibility**: Always detect and respect exact versions of IronPython 2.7, .NET Framework, Revit API, and DCMvn framework components
2. **DCMvn Patterns**: Prioritize patterns and standards defined in the .github/instructions/ directory and lib/DCMvn framework
3. **pyRevit Standards**: Follow pyRevit extension conventions for scripts, bundles, and UI organization
4. **Thread Safety**: Ensure all Revit API operations follow external event patterns for thread safety
5. **Code Quality**: Prioritize ${CODE_QUALITY_FOCUS == "All" ? "safety, performance, threading, Excel integration, and MVVM compliance" : CODE_QUALITY_FOCUS} in all generated code

## DCMvn Technology Version Detection

Before generating code, scan the DCMvn codebase to identify:

1. **IronPython Version**: Detect exact IronPython 2.7 compatibility requirements
   - Check for Python 2.7 syntax patterns and limitations
   - Identify .NET framework version compatibility
   - Verify assembly reference patterns used in existing scripts

2. **DCMvn Framework Versions**: Identify the exact versions of DCMvn components
   - Check lib/DCMvn core framework version
   - Verify ViewModelBase, RelayCommand, and service implementations
   - Document external event patterns and thread safety requirements

3. **Revit API Compatibility**: Note the exact Revit API version and patterns
   - Scan for Revit API usage patterns in existing scripts
   - Document element collection and filtering approaches
   - Identify transaction management patterns
```

## DCMvn Context Files

Prioritize the following files in .github/copilot directory and DCMvn framework:

- **copilot-instructions.md**: Primary DCMvn framework guidelines
- **instructions/python-development.instructions.md**: Python scripting patterns
- **instructions/model-patterns.instructions.md**: DCMvn model implementation
- **instructions/viewmodel-patterns.instructions.md**: MVVM implementation in DCMvn
- **instructions/service-patterns.instructions.md**: Service layer architecture
- **instructions/excel-integration.instructions.md**: Excel workflow patterns
- **lib/DCMvn/**: Core framework code for pattern analysis

## DCMvn Codebase Scanning Instructions

When DCMvn context files don't provide specific guidance:

1. Identify similar DCMvn files to the one being modified or created
2. Analyze DCMvn patterns for:
   - pyRevit script structure and imports
   - DCMvn ViewModelBase inheritance patterns
   - External event implementation for thread safety
   - Service layer dependency injection
   - Excel integration using MiniExcel
   - Error handling with alert() user notifications
   
3. Follow the most consistent DCMvn patterns found in the codebase
4. When conflicting patterns exist, prioritize patterns in newer files or DCMvn framework core
5. Never introduce patterns not found in the existing DCMvn codebase

## DCMvn Code Quality Standards

${CODE_QUALITY_FOCUS.includes("Safety") || CODE_QUALITY_FOCUS == "All" ? `### Safety and Validation
- Always validate document requirements before proceeding with Revit operations
- Use transaction management for all Revit modifications following DCMvn patterns
- Check element.IsValidObject before element operations
- Provide user-friendly error messages via DCMvn alert() function
- Log detailed errors using Debug.WriteLine() for debugging
- Follow DCMvn exception hierarchy patterns for robust error handling` : ""}

${CODE_QUALITY_FOCUS.includes("Performance") || CODE_QUALITY_FOCUS == "All" ? `### Performance Optimization
- Use caching for expensive operations (geometry, collections) following DCMvn CollectorService patterns
- Implement lazy loading where appropriate in DCMvn services
- Use LINQ extensions for efficient filtering following DCMvn patterns
- Batch operations when possible in Revit API operations
- Dispose of geometry objects properly following DCMvn cleanup patterns` : ""}

${CODE_QUALITY_FOCUS.includes("Threading") || CODE_QUALITY_FOCUS == "All" ? `### Thread Safety and External Events
- All Revit API operations must use DCMvn external event patterns
- Follow DCMvn ActionEventHandler implementation for thread safety
- Queue Revit operations through external events when not in API context
- Use HOST_APP.uiapp.ActiveAddInId checks before direct API access
- Implement proper error handling in external event execution` : ""}

${CODE_QUALITY_FOCUS.includes("MVVM") || CODE_QUALITY_FOCUS == "All" ? `### MVVM Pattern Compliance
- ViewModels must inherit from DCMvn ViewModelBase
- Use DCMvn RelayCommand for all user actions
- Use ObservableCollection[T]() for UI-bound data following DCMvn patterns
- Always call RaisePropertyChanged("property_name") for property updates
- Initialize services with document reference in ViewModels following DCMvn patterns` : ""}

${CODE_QUALITY_FOCUS.includes("Excel") || CODE_QUALITY_FOCUS == "All" ? `### Excel Integration Standards
- Use DCMvn.io.save_excel_file with MiniExcel library following established patterns
- Always clean existing files before saving following DCMvn cleanup patterns
- Use OrderedDictionary for data structure following DCMvn Excel patterns
- Wrap Excel operations in try-catch blocks with DCMvn error handling
- Load MiniExcel using DCMvn.coreutils.assemblyhandler.load_miniexcel()` : ""}

## DCMvn Testing Approach

${DCMVN_COMPONENT.includes("Scripts") || DCMVN_COMPONENT == "Full" ? `### pyRevit Script Testing
- Follow DCMvn document validation patterns before script execution
- Test scripts in isolated Revit documents when possible
- Verify element collection and filtering logic matches DCMvn CollectorService patterns
- Test error handling and user notification through DCMvn alert() function` : ""}

${DCMVN_COMPONENT.includes("ViewModels") || DCMVN_COMPONENT == "Full" ? `### MVVM Component Testing
- Test ViewModels inherit correctly from DCMvn ViewModelBase
- Verify RelayCommand implementations follow DCMvn patterns
- Test property change notifications work with DCMvn RaisePropertyChanged
- Validate external event integration for Revit API operations` : ""}

${DCMVN_COMPONENT.includes("Services") || DCMVN_COMPONENT == "Full" ? `### Service Layer Testing
- Test service initialization with document references follows DCMvn patterns
- Verify caching behavior in CollectorService implementations
- Test DetectionService geometric calculations and spatial relationships
- Validate ReportService data processing and Excel output` : ""}

## DCMvn Technology-Specific Guidelines

${PROJECT_TYPE == "pyRevit" || PROJECT_TYPE == "Auto-detect" ? `### pyRevit Script Guidelines
- Start all scripts with DCMvn standard import structure
- Use DCMvn.core imports for DB, HOST_APP, get_output
- Follow DCMvn.forms.mvvm patterns for ViewModelBase and RelayCommand
- Import System.Core and use LINQ extensions following DCMvn patterns
- Implement document validation using DCMvn patterns before proceeding` : ""}

${PROJECT_TYPE == "Python" || PROJECT_TYPE == "Auto-detect" ? `### IronPython 2.7 Guidelines
- Strictly adhere to Python 2.7 syntax limitations
- Use clr.AddReference patterns following DCMvn assembly loading
- Follow DCMvn import organization and namespace patterns
- Use try-except blocks following DCMvn exception handling patterns
- Implement proper disposal of .NET objects following DCMvn cleanup patterns` : ""}

${PROJECT_TYPE == "C#" || PROJECT_TYPE == "Avalonia" || PROJECT_TYPE == "OpenSilver" || PROJECT_TYPE == "Auto-detect" ? `### C# Component Guidelines
- Follow .NET Framework version detected in DCMvn C# projects
- Use XAML patterns consistent with DCMvn UI implementations
- Follow dependency injection patterns used in DCMvn Avalonia/OpenSilver projects
- Match async/await patterns from existing DCMvn C# code
- Apply MVVM patterns consistent with DCMvn framework architecture` : ""}

${REVIT_API_FOCUS.includes("Elements") || REVIT_API_FOCUS == "All" ? `### Revit Element Handling
- Use DCMvn FilteredElementCollector patterns with proper disposal
- Follow DCMvn element wrapper patterns for enhanced functionality
- Implement element validation using IsValidObject checks
- Use DCMvn parameter access patterns for element properties
- Follow DCMvn element creation patterns with proper transaction management` : ""}

${REVIT_API_FOCUS.includes("Transactions") || REVIT_API_FOCUS == "All" ? `### Transaction Management
- Use DCMvn transaction patterns for all Revit modifications
- Follow DCMvn external event patterns for thread-safe operations
- Implement proper transaction rollback on errors following DCMvn patterns
- Use DCMvn transaction grouping for related operations
- Follow DCMvn regeneration patterns after element modifications` : ""}

## DCMvn Documentation Requirements

${DOCUMENTATION_LEVEL == "Standard" || DOCUMENTATION_LEVEL == "Comprehensive" ? `### Code Documentation
- Follow DCMvn docstring patterns for Python functions and classes
- Document Revit API operations with parameter types and return values
- Include usage examples following DCMvn framework patterns
- Document external event requirements for thread safety
- Explain DCMvn service dependencies and initialization requirements` : ""}

${DOCUMENTATION_LEVEL == "Comprehensive" ? `### Comprehensive Documentation
- Document DCMvn framework integration points and dependencies
- Explain architectural decisions related to DCMvn MVVM patterns
- Document Excel integration workflows and data structures
- Include DCMvn performance considerations and optimization notes
- Document DCMvn error handling strategies and user notification patterns` : ""}

### 2. DCMvn Codebase Analysis Instructions

To create the DCMvn-specific copilot-instructions.md file, first analyze the DCMvn codebase to:

1. **Identify DCMvn Framework Patterns**:
   - Analyze lib/DCMvn/ core framework for established patterns
   - Extract ViewModelBase, RelayCommand, and service implementation patterns
   - Document external event patterns for thread safety
   - Map DCMvn service layer architecture and dependencies

2. **Understand DCMvn Architecture**:
   - Analyze pyRevit extension folder structure and bundle organization
   - Identify clear layer boundaries between scripts, ViewModels, services, and models
   - Document communication patterns between DCMvn components
   - Map Excel integration workflows and data transformation patterns

3. **Document DCMvn Code Patterns**:
   - Catalog naming conventions for DCMvn scripts, classes, and methods
   - Note DCMvn documentation styles and completeness patterns
   - Document DCMvn error handling and user notification patterns
   - Map DCMvn testing approaches and validation patterns

4. **Note DCMvn Quality Standards**:
   - Identify performance optimization techniques used in DCMvn services
   - Document thread safety practices implemented in DCMvn external events
   - Note accessibility and user experience patterns in DCMvn UI
   - Document DCMvn code quality patterns evident in the framework

### 3. DCMvn Implementation Notes

The final DCMvn copilot-instructions.md should:
- Be placed in the .github/copilot directory following DCMvn project structure
- Reference only patterns and standards that exist in the DCMvn codebase
- Include explicit IronPython 2.7 and .NET Framework compatibility requirements
- Avoid prescribing any practices not evident in the DCMvn framework code
- Provide concrete examples from the DCMvn codebase and framework
- Be comprehensive yet concise enough for Copilot to effectively use with DCMvn

Important: Only include guidance based on patterns actually observed in the DCMvn codebase. Explicitly instruct Copilot to prioritize consistency with existing DCMvn code over external best practices or newer language features not compatible with IronPython 2.7.
"

## Expected Output

A comprehensive DCMvn-specific copilot-instructions.md file that will guide GitHub Copilot to produce code that is perfectly compatible with the DCMvn framework, IronPython 2.7, Revit API patterns, and follows established DCMvn architecture and safety standards.
