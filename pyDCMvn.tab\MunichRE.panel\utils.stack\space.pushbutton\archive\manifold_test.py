#! python3
import sys

# cpython
sys.path.append(r"F:\DIG_GiangVu\workspace\pyDCMvn\pyDCMvn.MunichRE.extension\.venv\Lib\site-packages")
from manifold3d import Manifold, Mesh
import numpy as np

# pythonnet/Revit API
from DCMvn.core import DB, HOST_APP
from DCMvn.revit.selection import DSelection
from DCMvn.revit.geometry import GetElementMeshes
from DCMvn.core.framework import Debug


def revit_mesh_to_manifold(revit_mesh: DB.Mesh, transform: DB.Transform = None) -> Manifold:
    """Convert Revit DB.Mesh to Manifold3D object."""
    vertices = []
    faces = []
    
    # Extract vertices
    for i in range(revit_mesh.Vertices.Count):
        vertex = revit_mesh.Vertices[i]
        if transform:
            vertex = transform.OfPoint(vertex)
        vertices.append([vertex.X, vertex.Y, vertex.Z])
    
    # Extract triangular faces
    for i in range(revit_mesh.NumTriangles):
        triangle = revit_mesh.get_Triangle(i)
        faces.append([
            triangle.get_Index(0),
            triangle.get_Index(1), 
            triangle.get_Index(2)
        ])
    
    Debug.WriteLine("Extracted {} vertices and {} faces from Revit mesh".format(len(vertices), len(faces)))
    
    # Convert to numpy arrays with proper data types for manifold3d
    vertices = np.array(vertices, dtype=np.float32, order='C')
    faces = np.array(faces, dtype=np.uint32, order='C')
    
    Debug.WriteLine("Vertices shape: {}, dtype: {}".format(vertices.shape, vertices.dtype))
    Debug.WriteLine("Faces shape: {}, dtype: {}".format(faces.shape, faces.dtype))
    
    # Create manifold3d.Mesh object with BOTH vertices and faces
    mesh = Mesh(vert_properties=vertices, tri_verts=faces)
    Debug.WriteLine("Created Mesh object with {} vertices and {} faces".format(len(vertices), len(faces)))
    
    # Create manifold from the properly constructed mesh
    manifold = Manifold(mesh)
    Debug.WriteLine("Created Manifold from mesh successfully")
    
    return manifold


# Main execution
if __name__ == "__main__":
    # Pre-selected linked element and transform
    link_transform = HOST_APP.doc.GetElement(DB.ElementId(1481883)).GetTotalTransform()
    link_space = DSelection(is_linked=True).pick()

	# Get element meshes
    meshes = GetElementMeshes(link_space)
    target_mesh = meshes[0]
    Debug.WriteLine("Using mesh with {} triangles".format(target_mesh.NumTriangles))

    # Convert to manifold
    manifold = revit_mesh_to_manifold(target_mesh, link_transform)
