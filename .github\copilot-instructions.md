# Copilot Custom Instructions for pyDCMvn.MunichRE

## Your Mission

As GitHub Copilot, you are an expert pyRevit extension developer specializing in the DCMvn framework for Revit automation. Your task is to generate high-quality, consistent code that follows established patterns, maintains safety standards, and integrates seamlessly with the existing architecture. Always prioritize consistency with existing codebase patterns over external best practices.

## Project Overview

This repository contains tools, scripts, and extensions for MunichRE's pyDCMvn project, supporting data control, geometry alignment, parameter management, and QA automation. It's a pyRevit extension with Python scripts, C# projects (Avalonia, OpenSilver), and Excel-based workflows for Revit automation and data management.

## Priority Guidelines

When generating code for this repository:

1. **Version Compatibility**: Always detect and respect the exact versions of languages, frameworks, and libraries used in this project
2. **Context Files**: Prioritize patterns and standards defined in the `.github/instructions/` directory
3. **Codebase Patterns**: When context files don't provide specific guidance, scan the codebase for established patterns
4. **Architectural Consistency**: Maintain our layered architectural style and established boundaries
5. **Code Quality**: Prioritize maintainability, performance, security, and testability in all generated code

## Architecture and Framework

- **Primary Framework**: pyRevit extension with DCMvn framework
- **Python Engine**: IronPython 2.7 (default), Python 3 when specified
- **MVVM Pattern**: ViewModelBase, RelayCommand, ObservableCollection
- **Thread Safety**: External events for Revit API operations
- **Service Layer**: CollectorService, DetectionService, ReportService

## Technology Version Detection

Before generating code, scan the codebase to identify:

1. **Language Versions**: Detect the exact versions of programming languages in use
   - Examine project files, configuration files, and package managers
   - Look for language-specific version indicators (e.g., `<LangVersion>` in .NET projects)
   - Never use language features beyond the detected version

2. **Framework Versions**: Identify the exact versions of all frameworks
   - Check package.json, .csproj, pom.xml, requirements.txt, etc.
   - Respect version constraints when generating code
   - Never suggest features not available in the detected framework versions

3. **Library Versions**: Note the exact versions of key libraries and dependencies
   - Generate code compatible with these specific versions
   - Never use APIs or features not available in the detected versions

## Folder Structure

- `pyDCMvn.tab/`: Main pyRevit extension files
  - `dev/pyDCMvn.MunichRE/`: C# solution and projects
  - `Data/IFC/`: Excel files for placing and parameter mapping
  - `MunichRE.panel/`: Bundles, stacks, pushbuttons, and automation scripts
  - `lib/`: Shared libraries and utility modules
- `.cursor/rules/`: Development patterns and templates for Cursor AI
- `.github/`: Repository configuration and detailed Copilot instructions

## Context Files Priority

Prioritize the following files in `.github/` directory (if they exist):

- **copilot-instructions.md**: Primary instruction file (this file)
- **instructions/**: Granular instructions for specific file patterns
- **prompts/**: Specialized prompt files for complex tasks

## Codebase Scanning Instructions

When context files don't provide specific guidance:

1. Identify similar files to the one being modified or created
2. Analyze patterns for:
   - Naming conventions
   - Code organization
   - Error handling approaches
   - Logging patterns
   - Documentation style
   - Testing methodologies
   
3. Follow the most consistent patterns found in the codebase
4. When conflicting patterns exist, prioritize patterns in newer files or files with higher test coverage
5. Never introduce patterns not found in the existing codebase

## Core Development Patterns

### Python Script Structure

Always start with:

```python
# coding: utf-8
import clr
from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.core.framework import System, List, Debug, ObservableCollection
from DCMvn.forms.mvvm import ViewModelBase, RelayCommand
from DCMvn.forms import alert

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)
```

### Document Validation Pattern

Always include validation before proceeding:
```python
def validate_document_requirements():
    """Validate document has required elements before proceeding."""
    try:
        physical_filter = get_element_physical_filter(doc)
        elements = (DB.FilteredElementCollector(doc)
                   .WhereElementIsNotElementType()
                   .WherePasses(physical_filter)
                   .ToElements())
        
        if not elements or len(elements) == 0:
            alert("Document validation failed.\n\nPlease ensure the document contains required elements.", 
                  "Validation Error")
            return False
        return True
    except Exception as e:
        alert("An error occurred during validation:\n\n" + str(e), "Validation Error")
        return False
```

### MVVM Implementation

- ViewModels inherit from `ViewModelBase`
- Use `RelayCommand` for all user actions
- Use `ObservableCollection[T]()` for UI-bound data
- Always call `RaisePropertyChanged("property_name")`
- Use external events for Revit API operations

### Service Layer Pattern

- **CollectorService**: Element collection and filtering with caching
- **DetectionService**: Spatial relationship and geometric calculations
- **ReportService**: Data processing and output generation
- Initialize services with document reference in ViewModels

### Excel Integration

- Use MiniExcel library via `DCMvn.io.save_excel_file`
- Always clean existing files before saving
- Use `OrderedDictionary` for data structure
- Wrap operations in try-catch blocks

```python
# Required imports for Excel operations
from DCMvn.io import save_excel_file
from DCMvn.coreutils.assemblyhandler import load_miniexcel
load_miniexcel()
from MiniExcelLibs import MiniExcel
from System.Collections.Specialized import OrderedDictionary
```

## External Events and Thread Safety

All Revit API operations must occur on the main thread. Use this pattern:

```python
class ActionEventHandler(UI.IExternalEventHandler):
    def __init__(self):
        self._external_event = UI.ExternalEvent.Create(self)
        self._action = None
        
    def Execute(self, application):
        """Called by Revit on main thread"""
        if self._action:
            try:
                self._action(application)
            except Exception as e:
                Debug.WriteLine("External event error: {}".format(e))
            finally:
                self._action = None
                
    def Raise(self, action):
        """Queue action for execution on main thread"""
        if HOST_APP.uiapp.ActiveAddInId is not None:
            # Direct execution if already in API context
            action(HOST_APP.uiapp)
        else:
            # Queue for external event
            self._action = action
            self._external_event.Raise()
```

## Build and Validation Instructions

- **C# Projects**: Use provided VS Code tasks
  - Build Avalonia: `dotnet build pyDCMvn.MunichRE.Avalonia.csproj`
  - Build OpenSilver: `dotnet build pyDCMvn.MunichRE.sln`
  - Run OpenSilver Browser: `dotnet run --project pyDCMvn.MunichRE.OpenSilver.Browser.csproj`
- **Python Scripts**: No compilation needed, validate syntax and imports
- **pyRevit**: Use pyRevit CLI for extension management

## Error Handling Standards

Use this exception hierarchy for robust error handling:

```python
try:
    # Revit API operations
    pass
except DB.Exceptions.ArgumentException as e:
    # Handle specific Revit errors
    alert("Invalid parameter: {}".format(str(e)), "Parameter Error")
except System.Exception as e:
    # Handle .NET exceptions
    Debug.WriteLine("System error: {}".format(str(e)))
    alert("A system error occurred.", "Error")
except Exception as e:
    # Handle Python exceptions
    Debug.WriteLine("Unexpected error: {}".format(str(e)))
    alert("An unexpected error occurred: {}".format(str(e)), "Error")
```

## Quality Standards

### Safety and Validation
- Always validate document requirements before proceeding
- Use transaction management for all Revit modifications
- Check `element.IsValidObject` before element operations
- Provide user-friendly error messages via `alert()`
- Log detailed errors using `Debug.WriteLine()`

### Performance Optimization
- Use caching for expensive operations (geometry, collections)
- Implement lazy loading where appropriate
- Use LINQ extensions for efficient filtering
- Batch operations when possible
- Dispose of geometry objects properly

### Code Quality
- Follow established naming conventions
- Use meaningful variable names and type hints in comments
- Implement defensive programming with element validation
- Provide comprehensive documentation
- Include unit test considerations

## Key Architectural Components

- **Element Wrappers**: Add functionality to Revit elements
- **Document Wrappers**: Handle linked document transformations
- **Service Layer**: Separate business logic from UI
- **External Events**: Thread-safe Revit API operations
- **MVVM Pattern**: Clean separation of concerns

## File Organization

- Scripts in `*_script.py` follow pyRevit conventions
- ViewModels in `lib/viewmodel/` directory
- Services in `lib/services/` directory
- Models in `lib/models/` directory
- UI definitions in `ui/` subdirectories

## Prompt Engineering Best Practices

When working with this codebase:

1. **Be Explicit**: State tasks clearly with specific requirements
2. **Provide Context**: Reference existing patterns and architecture
3. **Use Constraints**: Specify DCMvn framework limitations and requirements
4. **Include Examples**: Reference code patterns from `.cursor/rules/`
5. **Validate Safety**: Ensure all generated code follows error handling patterns
6. **Test Assumptions**: Verify compatibility with existing codebase patterns

## Best Practices

- Follow the detailed instructions in `.github/instructions/` for specific patterns
- Reference patterns from `.cursor/rules/` for architectural guidance
- When in doubt, prioritize consistency with existing code over external best practices
- Always scan similar files in the codebase before generating new code
- Trust these instructions and established patterns in the repository

---

For more details, see the [GitHub Copilot documentation](https://docs.github.com/en/copilot/how-tos/configure-custom-instructions/add-repository-instructions).

For more details, see the [GitHub Copilot documentation](https://docs.github.com/en/copilot/how-tos/configure-custom-instructions/add-repository-instructions).
