from DCMvn.core import DB, HOST_APP
from ..space_props import SpaceProperties
from ..generic_space import GenericSpace
from ..cached_collector import CachedCollector


class SpatialFactory:
    """
    Factory class for creating spatial space objects.
    """
    def __init__(self):
        pass
    
    @staticmethod
    def create_separators(generic_space, host_collector, create_space=True):
        # type: (GenericSpace, CachedCollector, bool) -> bool
        """ Create separators for a given generic space.

        Args:
            generic_space (GenericSpace): The generic space for which to create separators.
            host_collector (CachedCollector): The host collector.
            create_space (bool, optional): If True, create space separator. else, create room separator. Defaults to True.

        Returns:
            bool: True if boundary lines creation was successful, False otherwise.
        """
        # Validate inputs first
        if not generic_space or not generic_space.curve_arrays:
            return False
            
        level = generic_space.level
        if not level:
            return False
            
        level_view = host_collector.views_dicts[level.Name]
        if not level_view:
            return False
            
        sketch_plane = level_view.SketchPlane # type: DB.SketchPlane
        if not sketch_plane:
            return False

        current_attach_view = generic_space.attached_plan_view
        if not current_attach_view:
            return False
        
        # Try to create boundary lines for each curve array
        at_least_one_success = False
        
        for curve_array in generic_space.curve_arrays:
            if not curve_array or curve_array.Size == 0:
                continue
                
            try:
                if create_space:
                    result = HOST_APP.doc.Create.NewSpaceBoundaryLines(sketch_plane, curve_array, current_attach_view)
                else:
                    result = HOST_APP.doc.Create.NewRoomBoundaryLines(sketch_plane, curve_array, current_attach_view)
                
                # Check if boundary lines were actually created
                if result and not at_least_one_success:
                    at_least_one_success = True
                    
            except Exception as e:
                # Log the specific error for debugging but continue with other curve arrays
                print("{} Failed to create boundary lines for curve array: {}".format(generic_space.guid, str(e)))
        
        return at_least_one_success

    @staticmethod
    def create_space(generic_space, location):
        # type: (GenericSpace, DB.XYZ) -> DB.Element
        """
        Create a spatial space object based on the specified type.

        Args:
            generic_space (GenericSpace): The generic space to create a space for.
            location (DB.XYZ): The location where the space will be created.
        """
        return HOST_APP.doc.Create.NewSpace(generic_space.level, DB.UV(location.X, location.Y))


    @staticmethod
    def create_room(generic_space, location):
        # type: (GenericSpace, DB.XYZ) -> DB.Element
        """
        Create a spatial room object based on the specified type.

        Args:
            generic_space (GenericSpace): The generic space to create a room for.
            location (DB.UV): The location where the room will be created.
        """
        return HOST_APP.doc.Create.NewRoom(generic_space.level, DB.UV(location.X, location.Y))
        
    @staticmethod
    def map_properties(space, generic_space):
        # type: (DB.Element, GenericSpace) -> None
        """
        Map properties from a space object to a dictionary.

        Args:
            space: The space object to map properties from.
            generic_space: The generic space object to map properties to.
        """
        
        space.get_Parameter(SpaceProperties.Guid).Set(generic_space.guid)
        space.get_Parameter(SpaceProperties.Name).Set(generic_space.name)
        space.get_Parameter(SpaceProperties.Number).Set(generic_space.number)
        space.get_Parameter(SpaceProperties.BaseOffset).Set(generic_space.base_offset)
        space.get_Parameter(SpaceProperties.LimitOffset).Set(generic_space.limit_offset)