# coding: utf-8
import System
import clr
import ast
from DCMvn.core import DB
from MunichRE.constants import *

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)


class IfcEntity:
    def __init__(self, expando_object):
        """
        Ifc entity base on ExpandoObject
        Args:
            expando_object: ExpandoObject
        """
        self._object = expando_object

    @property
    def express_id(self):
        value = getattr(self._object, "ExpressId")
        if not value:
            raise ValueError("Express Id is not defined")
        return value
    
    @property
    def id(self):
        value = getattr(self._object, GLOBAL_ID)
        if not value:
            raise ValueError("Global Id is not defined")
        return value

    @property
    def ifc_class(self):
        value = getattr(self._object, IFC_ELEMENT)
        return value

    @property
    def rvt_category(self):
        value = getattr(self._object, RVT_CATEGORY)
        return value

    @property
    def rvt_type(self):
        value = getattr(self._object, RVT_TYPE_NAME)
        return value
    
    @property
    def rvt_family(self):
        value = getattr(self._object, RVT_FAMILY_NAME)
        return value

    @property
    def rvt_system(self):
        value = getattr(self._object, RVT_SYSTEM_TYPE)
        return value

    @property
    def ifc_level(self):
        value = getattr(self._object, STOREY_NAME)
        return value

    @property
    def centroid(self):
        centroid = getattr(self._object, CENTROID_MM)
        try:
            value = ast.literal_eval(centroid)
        except ValueError:
            return None
        return DB.XYZ(*[DB.UnitUtils.ConvertToInternalUnits(i, DB.UnitTypeId.Millimeters) for i in value])

    @property
    def half_distance(self):
        half_distance = getattr(self._object, HALF_DISTANCE_MM)
        try:
            value = ast.literal_eval(half_distance)
        except ValueError:
            return None
        return [DB.UnitUtils.ConvertToInternalUnits(i, DB.UnitTypeId.Millimeters) for i in value]

    @property
    def transform(self):
        # type: () -> DB.Transform
        if not self.centroid:
            raise ValueError("Centroid is not defined")
        if not self.half_distance:
            raise ValueError("Half distance is not defined")

        try:
            direction_x = ast.literal_eval(getattr(self._object, DIRECTION_X))
            direction_y = ast.literal_eval(getattr(self._object, DIRECTION_Y))
            direction_z = ast.literal_eval(getattr(self._object, DIRECTION_Z))
        except ValueError:
            raise ValueError("Invalid Direction Value")

        transfrom = DB.Transform.Identity  # type: DB.Transform
        transfrom.Origin = self.centroid
        transfrom.BasisX = DB.XYZ(*direction_x)
        transfrom.BasisY = DB.XYZ(*direction_y)
        transfrom.BasisZ = DB.XYZ(*direction_z)
        return transfrom

    def calculate_oriented_bbox(self):
        # type: () -> DB.BoundingBoxXYZ
        bbox = DB.BoundingBoxXYZ()
        bbox.Transform = self.transform
        bbox.Min = DB.XYZ(*[-i for i in self.half_distance])
        bbox.Max = DB.XYZ(*self.half_distance)
        return bbox
    
    @staticmethod
    def get_value(param_list, expando_object, error_message):
        # type: (list, object, str) -> object
        """ Check if the param_list has only one value in the expando_object

        Args:
            param_list (list): list of string
            expando_object (object): expando object in .NET
            error_message (str): error message when the param_list has no value

        Raises:
            ValueError: error when the param_list has more than 1 value
            ValueError: error when the param_list has no value
        """
        unique_value = set()
        param_has_value = []

        for param in param_list:
            value = getattr(expando_object, param, None)
            if value:
                param_has_value.append(param)

                # string
                try:
                    if isinstance(value, str):
                        value = ast.literal_eval(value)[0]
                        unique_value.add(value)
                except ValueError:
                    # print(getattr(expando_object, GLOBAL_ID))
                    continue

                # float
                if isinstance(value, float):
                    unique_value.add(value)

        if len(unique_value) > 1:
            raise ValueError("Have more than 1 input value: {}".format(", ".join(param_has_value)))
        elif not unique_value:
            # print(getattr(expando_object, GLOBAL_ID))
            raise ValueError(error_message)
        else:
            return unique_value.pop()
