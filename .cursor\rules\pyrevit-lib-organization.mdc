---
description: Efficient lib folder organization and import patterns for pyRevit tools
globs: ["**/lib/**/*.py", "**/__init__.py"]
alwaysApply: false
---

# pyRevit Lib Organization

## Standard Library Structure
```
tool_name.pushbutton/
├── tool_name_script.py         # Entry point
├── bundle.yaml                 # Configuration
└── lib/                        # All supporting code
    ├── __init__.py            # Export main classes
    ├── services/              # Business logic layer
    │   ├── __init__.py       # Service exports
    │   ├── collector_service.py
    │   ├── detection_service.py
    │   └── report_service.py
    ├── models/                # Data models
    │   ├── __init__.py       # Model exports
    │   ├── mep/              # MEP element wrappers
    │   ├── spatial/          # Spatial element wrappers
    │   └── document_wrapper.py
    ├── viewmodel/             # MVVM ViewModels
    │   ├── __init__.py       # ViewModel exports
    │   └── main_viewmodel.py
    ├── ui/                    # Views and XAML
    │   ├── __init__.py       # UI exports
    │   └── main_view.py
    ├── commands/              # Command pattern
    │   ├── __init__.py       # Command exports
    │   └── run_command.py
    ├── events/                # External events
    │   ├── __init__.py       # Event exports
    │   └── external_event_handler.py
    └── utils/                 # Utilities
        ├── __init__.py       # Utility exports
        └── geometry_utils.py
```

## Import Best Practices
- **Package-level imports**: `from ..models import BaseMep, RevitSpatial`
- **Avoid deep paths**: Don't use `from ..models.mep.base_mep import BaseMep`
- **Module-level imports**: Import at top of file, not in functions
- **Relative imports**: Use `from ..services import CollectorService` within lib
- **Export strategy**: Use `__init__.py` to expose public API

## Efficient Module Loading
- Export commonly used classes in `__init__.py` files
- Use `__all__` to control public interface
- Group related imports for better organization
- Minimize circular dependencies between modules
- Cache expensive imports when possible

## DCMvn Integration Patterns
- Leverage existing DCMvn components before creating new ones
- Use DCMvn's caching mechanisms in services
- Follow DCMvn naming conventions for consistency
- Integrate with DCMvn's error handling patterns

[efficient-init-template.py](mdc:efficient-init-template.py)