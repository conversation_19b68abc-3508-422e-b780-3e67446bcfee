# coding: utf-8
"""
DTO models for TabooZone QA refactor
"""
from DCMvn.core import DB
from DCMvn.core.framework import Debug


class ElementInfo(object):
    def __init__(self, element):  # type: (DB.Element) -> None
        self._element = element
        self._element_id = None
        self._display_string = None
        self._initialize()

    def _initialize(self):
        try:
            if self._element and self._element.IsValidObject:
                self._element_id = self._element.Id.Value
            else:
                self._element_id = None
        except Exception as ex:
            Debug.WriteLine("ElementInfo._initialize error: {}".format(str(ex)))
            self._element_id = None

    @property
    def element(self):  # type: () -> DB.Element
        return self._element

    @property
    def element_id(self):  # type: () -> int
        return self._element_id

    def _build_display_string(self):
        try:
            if not self._element or not self._element.IsValidObject:
                return "Invalid Element"
            category_name = self._element.Category.Name if self._element.Category else "Unknown Category"
            family_and_type = ""
            try:
                p = self._element.get_Parameter(DB.BuiltInParameter.ELEM_FAMILY_AND_TYPE_PARAM)
                if p and p.HasValue:
                    family_and_type = p.AsValueString()
            except Exception as e:
                Debug.WriteLine("ElementInfo._build_display_string family/type error: {}".format(str(e)))
                pass
            return "ID:{} | {} | {}".format(self._element_id, category_name, family_and_type)
        except Exception as ex:
            Debug.WriteLine("ElementInfo._build_display_string error: {}".format(str(ex)))
            return "ID:{} | Error".format(self._element_id or "-")

    @property
    def display_string(self):  # type: () -> str
        if self._display_string is None:
            self._display_string = self._build_display_string()
        return self._display_string

    @property
    def is_valid(self):  # type: () -> bool
        try:
            return bool(self._element and self._element.IsValidObject)
        except Exception as ex:
            Debug.WriteLine("ElementInfo.is_valid error: {}".format(str(ex)))
            return False


class GuidPair(object):
    def __init__(self, guid, element):
        # type: (str, DB.Element | list[DB.Element]) -> None
        self.guid = guid
        self.element_info = ElementInfo(element) if isinstance(element, DB.Element) else [ElementInfo(e) for e in element]


class MatchedPair(object):
    def __init__(self, guid, arc_element, mass_element):
        # type: (str, DB.Element | list[DB.Element], DB.Element | list[DB.Element]) -> None
        self.guid = guid
        self.arc_info = ElementInfo(arc_element) if isinstance(arc_element, DB.Element) else [ElementInfo(e) for e in arc_element]
        self.mass_info = ElementInfo(mass_element) if isinstance(mass_element, DB.Element) else [ElementInfo(e) for e in mass_element]


class CentroidPair(object):
    def __init__(self, guid, arc_element, mass_element, distance_mm, within_tolerance):
        self.guid = guid
        self.arc_info = ElementInfo(arc_element)
        self.mass_info = ElementInfo(mass_element)
        self.distance_mm = float(distance_mm) if distance_mm is not None else None
        self.within_tolerance = bool(within_tolerance)

    @property
    def distance_rounded(self):
        try:
            if self.distance_mm is None:
                return None
            return round(self.distance_mm, 1)
        except Exception as ex:
            Debug.WriteLine("CentroidPair.distance_rounded error: {}".format(str(ex)))
            return None

    @property
    def validation_result(self):
        return "Passed" if self.within_tolerance else "Failed"


class ValidationResult(object):
    def __init__(self, arc_name, mass_name, tolerance_mm):
        # type: (str, str, float) -> None
        self.arc_name = arc_name
        self.mass_name = mass_name
        self.tolerance_mm = tolerance_mm
        self.matched_pairs = []  # type: list[MatchedPair]
        self.missing_pairs = []  # type: list[GuidPair]
        self.extra_pairs = []    # type: list[GuidPair]
        self.duplicate_arc_pairs = []  # type: list[GuidPair]
        self.duplicate_mass_pairs = [] # type: list[GuidPair]
        self.centroid_pairs = []  # type: list[CentroidPair]
        self.total_arc_elements = 0
        self.total_mass_elements = 0

    @property
    def has_guid_issues(self):
        return any([
            len(self.missing_pairs) > 0,
            len(self.extra_pairs) > 0,
            len(self.duplicate_arc_pairs) > 0,
            len(self.duplicate_mass_pairs) > 0,
        ])

    @property
    def has_centroid_issues(self):
        return any([p.within_tolerance is False for p in self.centroid_pairs])

    @property
    def has_issues(self):
        return self.has_guid_issues or self.has_centroid_issues

