# coding: utf-8
"""
Centroid validation service using OBB centroids.
"""
from DCMvn.core import DB
from DCMvn.core.framework import Debug, Trace  # noqa: F401
from DCMvn.revit.geometry import (
    GetElementMergedSolid,
    GetElementMeshes,
    compute_obb_to_solid,
)

from .models import CentroidPair


class CentroidValidator(object):

    @staticmethod
    def get_obb_centroid(element):  # type: (DB.Element) -> DB.XYZ | None
        geometry = GetElementMeshes(element) or GetElementMergedSolid(element)
        if geometry:
            obb = compute_obb_to_solid(geometry, True)
            return obb.ComputeCentroid() if obb else None  # noqa
        return None

    @staticmethod
    def calculate_distance_mm(c1, c2):  # type: (DB.XYZ, DB.XYZ) -> float
        try:
            d_internal = c1.DistanceTo(c2)
            return DB.UnitUtils.ConvertFromInternalUnits(d_internal, DB.UnitTypeId.Millimeters)
        except Exception as ex:
            Debug.WriteLine("calculate_distance_mm error: {}".format(str(ex)))
            return 9999999.0  # large number to indicate error

    def validate_centroid_distance(self, matched_pairs, tolerance_mm):
        # type: (list, float) -> list
        pairs = []
        try:
            for mp in matched_pairs:
                arc_centroid = self.get_obb_centroid(mp.arc_info.element)
                mass_centroid = self.get_obb_centroid(mp.mass_info.element)
                Trace.Write(arc_centroid)
                Trace.Write(mass_centroid)
                if arc_centroid is None or mass_centroid is None:
                    continue
                d_mm = self.calculate_distance_mm(arc_centroid, mass_centroid)
                if d_mm is None:
                    continue
                within = d_mm <= tolerance_mm
                pairs.append(CentroidPair(mp.guid, mp.arc_info.element, mp.mass_info.element, d_mm, within))
        except Exception as ex:
            Debug.WriteLine("CentroidValidator error: {}".format(str(ex)))
        return pairs

