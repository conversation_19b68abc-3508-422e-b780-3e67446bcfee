# coding: utf-8
"""
Plugin rule for HG New Beams
Wraps HG new beam element collectors in a TabooRule.
"""
from DCMvn.core import DB
from DCMvn.core.framework import Debug
from DCMvn.revit.query.query import DQuery

from .base import TabooRule, register_rule


@register_rule
class HGNewBeamsRule(TabooRule):
    rule_id = "HG_NEW_BEAMS"
    display_name = "HG New Beams"

    def __init__(self, tolerance_mm=10.0):
        self.tolerance_mm = tolerance_mm

    def collect_arc_elements(self, arc_doc):
        try:
            if isinstance(arc_doc, DB.RevitLinkInstance):
                arc_doc = arc_doc.GetLinkDocument()
            return DQuery(arc_doc).get_by_workset("000_LinkIFC_HG-ARC_NewConstruction_Beam")
        except Exception as ex:
            Debug.WriteLine("HGNewBeamsRule.collect_arc_elements error: {}".format(str(ex)))
            return []

    def collect_mass_elements(self, mass_doc):
        try:
            return DQuery(mass_doc).get_by_workset("400_TBZ_NewConstruction_Beam")
        except Exception as ex:
            Debug.WriteLine("HGNewBeamsRule.collect_mass_elements error: {}".format(str(ex)))
            return []

    @property
    def mass_guid_param_spec(self):
        return "SI_TE_DM_IfcGUID"

    @property
    def tolerance_mm(self):
        return self.tolerance_mm

    @property
    def extra_excel_columns(self):
        return {"Guid Validation": "Matched", "Centroid Result": "Passed"}
