# coding: utf-8
from DCMvn.core import DB, HOST_APP
from .clashes.arc_curve import ArcCurve

def are_bbox_overlaps(bbox1, bbox2, tolerance=1e-9):
    # type: (DB.BoundingBoxXYZ, DB.BoundingBoxXYZ, float) -> bool

    source_min = bbox1.Transform.OfPoint(bbox1.Min) if not bbox1.Transform.IsIdentity else bbox1.Min
    source_max = bbox1.Transform.OfPoint(bbox1.Max) if not bbox1.Transform.IsIdentity else bbox1.Max
    other_min = bbox2.Transform.OfPoint(bbox2.Min) if not bbox2.Transform.IsIdentity else bbox2.Min
    other_max = bbox2.Transform.OfPoint(bbox2.Max) if not bbox2.Transform.IsIdentity else bbox2.Max

    overlap_x = not (source_max.X < other_min.X - tolerance or source_min.X > other_max.X + tolerance)
    overlap_y = not (source_max.Y < other_min.Y - tolerance or source_min.Y > other_max.Y + tolerance)
    overlap_z = not (source_max.Z < other_min.Z - tolerance or source_min.Z > other_max.Z + tolerance)

    return overlap_x and overlap_y and overlap_z


def get_element_from_ref(reference, current_doc):
    # type: (DB.Reference, DB.Document) -> DB.Element  # noqa
    """
    Get element from DB.Reference
    Args:
        reference (DB.Reference): Revit reference
        current_doc (DB.Document): current document

    Returns:
        DB.Element: Revit element
    """
    element = current_doc.GetElement(reference.ElementId)
    if isinstance(element, DB.RevitLinkInstance):
        return element.GetLinkDocument().GetElement(reference.LinkedElementId)
    return element


def get_workset_by_name(document, workset_name):
    # type: (DB.Document, str) -> DB.Workset
    """
    Get workset by name
    Args:
        document (DB.Document): Revit document
        workset_name (str): workset name

    Returns:
        DB.Workset: workset
    """
    worksets = DB.FilteredWorksetCollector(document).OfKind(DB.WorksetKind.UserWorkset).ToWorksets()
    workset = next((x for x in worksets if x.Name == workset_name), None)
    return workset


def get_arc_from_solid(solid):
    # type: (DB.Solid) -> list[ArcCurve]  # noqa
    """
    Get arc from solid
    Args:
        solid (DB.Solid): solid

    Returns:
        list[ArcCurve]: list of arc
    """
    arcs = []
    for edge in solid.Edges:  # noqa
        curve = edge.AsCurve() # type: DB.Curve
        if isinstance(curve, DB.Arc):
            arcs.append(curve)

    return [ArcCurve(arc) for arc in arcs]


def create_shared_param_filter(param_name, param_value, doc):
    # type: (str, str, DB.Document) -> DB.ElementParameterFilter
    """Create ElementParameterFilter for shared parameter by name"""
    # Get all shared parameters in the document
    shared_params = doc.ParameterBindings.ForwardIterator()
    param_definition = None
    
    # Find the parameter definition by name
    while shared_params.MoveNext():
        definition = shared_params.Key
        if hasattr(definition, 'Name') and definition.Name == param_name:
            param_definition = definition
            break
    
    if param_definition is None:
        # If parameter not found, return a filter that matches nothing
        # This prevents errors but won't match any elements
        dummy_param_id = DB.ElementId(DB.BuiltInParameter.INVALID)
        dummy_provider = DB.ParameterValueProvider(dummy_param_id)
        dummy_evaluator = DB.FilterStringEquals()
        if HOST_APP.version >= 2023:
            dummy_rule = DB.FilterStringRule(dummy_provider, dummy_evaluator, "NEVER_MATCH")
        else:
            dummy_rule = DB.FilterStringRule(dummy_provider, dummy_evaluator, "NEVER_MATCH", True)
        return DB.ElementParameterFilter(dummy_rule)
    
    # Create parameter provider using the found definition
    param_id = param_definition.Id if hasattr(param_definition, 'Id') else DB.ElementId(DB.BuiltInParameter.INVALID)
    param_provider = DB.ParameterValueProvider(param_id)
    evaluator = DB.FilterStringEquals()
    
    if HOST_APP.version >= 2023:
        rule = DB.FilterStringRule(param_provider, evaluator, param_value)
    else:
        rule = DB.FilterStringRule(param_provider, evaluator, param_value, True)
    
    return DB.ElementParameterFilter(rule)