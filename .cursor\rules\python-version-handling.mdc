---
description: Python version handling and headers for pyRevit development
globs: ["**/*.py"]
alwaysApply: false
---

# Python Version Handling

## File Headers
- **IronPython 2.7**: Use `# coding: utf-8` at the top of file
- **Python 3**: Use `#! python3` at the top (no need for `# coding: utf-8`)
- Follow pyRevit documentation standards for version specifications

## pyRevit engines
- Default engine: IronPython 2.7 (maximum compatibility with legacy tools)
- Optional engine: IronPython 3.4 (enable it in pyRevit Settings → Engines)
  - Supports many Python 3 features including f-strings (via ironpython3): see `ironpython3` project for details ([IronPython 3](https://github.com/IronLanguages/ironpython3))
- CPython 3.x engine: available but package compatibility and deployment vary by version; prefer 3.12 when targeting pyRevit’s current Python 3 engine guidance

## DCMvn version flags (preferred)
- DCMvn exposes runtime flags for Python and .NET environments:
```python
from DCMvn.core import PY2, <PERSON>Y3, IRONPY, IRONPY2, IRONPY3, NETCORE, NETFRAMEWORK

if PY3 and NETCORE:
    # Python 3 on .NET Core (pyRevit modern engine)
    pass
elif IRONPY2 and NETFRAMEWORK:
    # IronPython 2.7 on .NET Framework (legacy)
    pass
```
- Use these flags for compatibility branching instead of manual `sys.version_info` checks whenever possible. They centralize environment detection across tools.

## Version-Specific Patterns
### IronPython 2.7 (Default)
```python
# coding: utf-8
import clr
from DCMvn.core import DB
# Traditional string formatting
message = "Processing element {}".format(element.Id)
# Type hints in comments
def process_element(element):
    # type: (DB.Element) -> bool
    pass
```

### Python 3 (When Required)
```python
#! python3
import clr
from DCMvn.core import DB
# Can use f-strings
message = f"Processing element {element.Id}"
# Can use type annotations
def process_element(element: DB.Element) -> bool:
    pass
```

## External packages in Python 3
- You can extend `sys.path` at runtime to use packages from another Python 3.12 virtual environment (recommended for pyRevit’s Python 3 engine).
```python
#! python3
import sys
# Point to a Python 3.12 venv site-packages
sys.path.append(r"C:\path\to\py312-venv\Lib\site-packages")
```
- Avoid mixing binary wheels across different Python minor versions (e.g., 3.9 vs 3.12). Install packages into a 3.12 environment to ensure ABI compatibility (numpy, pandas, matplotlib, etc.).
- Optionally, discover user site-packages dynamically:
```python
import site, sys
user_site = site.getusersitepackages()
if user_site not in sys.path:
    sys.path.append(user_site)
```

## Dynamo Python 3 embedded packages (Revit 2023+)
- Dynamo’s Python 3 engine uses Python 3.9 and ships an embedded Lib folder that includes common packages (numpy, pandas, matplotlib, …). The path is user-specific; `<USERNAME>` denotes your Windows account name:
```
C:\Users\<USER>\AppData\Local\python-3.9.12-embed-amd64\Lib
```
- You can add this path to `sys.path` when running under Dynamo’s Python 3.9:
```python
import os, sys
embedded_lib = os.path.join(os.environ.get('LOCALAPPDATA', ''), 'python-3.9.12-embed-amd64', 'Lib')
if embedded_lib and embedded_lib not in sys.path:
    sys.path.append(embedded_lib)
```
- Caution when used from pyRevit’s Python 3.12: many of these packages include compiled extensions built for 3.9 and will not import under 3.12. Prefer installing 3.12-compatible wheels into a separate 3.12 venv and append that venv’s `site-packages` to `sys.path` when running in pyRevit.

## Compatibility Guidelines
- Prefer `DCMvn.core` flags (`PY3`, `IRONPY2`, `NETCORE`, etc.) for environment detection
- Default to IronPython 2.7 unless Python 3 features are specifically needed
- Test compatibility across both versions when possible
- Use version-appropriate syntax patterns

## When to Use Python 3
- When leveraging modern Python libraries
- For advanced type checking with mypy
- When using modern async/await patterns
- For performance-critical operations that benefit from Python 3