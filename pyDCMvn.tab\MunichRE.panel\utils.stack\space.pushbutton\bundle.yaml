title: "Space Generator"
tooltip:
  en_us: |
    Version = 2.0
    __________________________________________________________________
    Description:

    Automatically creates Revit spaces from linked IFC generic spaces 
    using advanced mesh boundary extraction and curve processing.
    Features intelligent handling of short curves and maintains 
    continuity for separator creation.
    __________________________________________________________________
    How-to:

    SHIFT + CLICK: Automatic Space Creation
    - Processes all linked generic spaces from IFC models
    - Extracts bottom boundary curves from mesh geometry
    - Creates room separators automatically
    - Generates spaces with proper locations inside boundaries
    - Maps properties from IFC spaces to Revit spaces
    
    REGULAR CLICK: Space Export
    - Exports data validation for space creation
    __________________________________________________________________
    Technical Features:

    • Mesh boundary curve extraction with Z-normal filtering
    • Short curve handling and merging for Revit compatibility
    • Intelligent point-in-polygon location finding
    • Automatic separator creation with curve validation
    • Property mapping between IFC and Revit spaces
    • Error handling for complex geometries
    __________________________________________________________________
    Requirements:

    • Linked IFC document with generic space models
    • Active phase in current document
    • Sufficient curve lengths for Revit separator creation
    __________________________________________________________________
    Last update:

    - [04.06.2025] - 2.0 Enhanced mesh processing and curve handling
    - [14.02.2025] - 1.0 Initial release
author: "Giang Vu"
engine:
  clean: true