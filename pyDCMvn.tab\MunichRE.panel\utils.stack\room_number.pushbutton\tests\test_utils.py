# coding: utf-8
"""
Shared test utilities and helper classes for room number mapping tests.
Provides common functionality to avoid code duplication across test files.
"""
import clr
from DCMvn.core import DB, UI, HOST_APP
from DCMvn.core.framework import Debug, Trace
from DCMvn.forms import alert

clr.AddReference("System.Core")


class TestOptions(object):
    """Centralized test options configuration."""
    
    def __init__(self):
        self.proximity_distance_mm = 300  # 300mm default proximity
        self.proximity_distance_internal = 300.0 / 304.8  # Convert to internal units
        self.debug_output = True
        self.show_alerts = True


class SpatialElementSelector(object):
    """Helper class for selecting linked spatial elements."""
    
    @staticmethod
    def pick_linked_spatial():
        # type: () -> tuple[DB.Element, DB.RevitLinkInstance, DocumentWrapper]
        """
        Select a linked spatial element and return element info.
        Returns:
            tuple: (spatial_element, link_instance, doc_wrapper) or (None, None, None) if failed
        """
        try:
            reference = HOST_APP.uidoc.Selection.PickObject(UI.Selection.ObjectType.LinkedElement)
            link_instance = HOST_APP.doc.GetElement(reference.ElementId)  # type: DB.RevitLinkInstance
            
            if not isinstance(link_instance, DB.RevitLinkInstance):
                if TestOptions().show_alerts:
                    alert("Selected element is not from a linked document")
                return None, None, None
                
            spatial_element = link_instance.GetLinkDocument().GetElement(reference.LinkedElementId)
            if not spatial_element:
                if TestOptions().show_alerts:
                    alert("Could not get spatial element from linked document")
                return None, None, None
                
            # Create document wrapper
            from lib.models.document_wrapper import DocumentWrapper
            doc_wrapper = DocumentWrapper(link_instance.GetLinkDocument(), link_instance)
            
            return spatial_element, link_instance, doc_wrapper
            
        except Exception as e:
            if TestOptions().show_alerts:
                alert("Error selecting linked element: {0}".format(str(e)))
            return None, None, None


class MepElementSelector(object):
    """Helper class for selecting MEP elements."""
    
    @staticmethod
    def pick_mep_element():
        """
        Select a MEP element from current document.
        Returns:
            element or None if failed
        """
        try:
            from DCMvn.revit.selection import DSelection
            mep = DSelection(is_linked=False).pick()
            if not mep:
                if TestOptions().show_alerts:
                    alert("No MEP element selected")
                return None
            return mep
        except Exception as e:
            if TestOptions().show_alerts:
                alert("Error selecting MEP element: {0}".format(str(e)))
            return None


class SpatialWrapperFactory(object):
    """Factory for creating spatial wrappers based on element type."""
    
    @staticmethod
    def create_spatial_wrapper(spatial_element, doc_wrapper):
        """
        Create appropriate spatial wrapper based on element type.
        Returns:
            BaseSpatial wrapper or None if failed
        """
        try:
            # Determine if it's a Room/Space or Generic spatial
            if (spatial_element.Category and 
                spatial_element.Category.Id.IntegerValue in [DB.BuiltInCategory.OST_Rooms.value__, 
                                                            DB.BuiltInCategory.OST_MEPSpaces.value__]):
                from lib.models.spatial.revit_spatial import RevitSpatial
                # Create spatial element geometry calculator
                options = DB.SpatialElementBoundaryOptions()
                options.SpatialElementBoundaryLocation = DB.SpatialElementBoundaryLocation.Finish
                calculator = DB.SpatialElementGeometryCalculator(doc_wrapper.document, options)
                return RevitSpatial(spatial_element, doc_wrapper, calculator)
            else:
                # Assume it's a Generic spatial (IFC space, etc.)
                from lib.models.spatial.generic_spatial import GenericSpatial
                return GenericSpatial(spatial_element, doc_wrapper)
                
        except Exception as e:
            Debug.WriteLine("Error creating spatial wrapper: {0}".format(str(e)))
            return None


class GeometryHelper(object):
    """Helper methods for geometry operations and transformations."""
    
    @staticmethod
    def create_expanded_outline(bbox, proximity_distance_internal):
        """
        Create expanded outline from bounding box.
        Args:
            bbox: BoundingBoxXYZ
            proximity_distance_internal: Distance in internal units
        Returns:
            DB.Outline: Expanded outline
        """
        if not bbox:
            return None
            
        expanded_outline = DB.Outline(
            DB.XYZ(
                bbox.Min.X - proximity_distance_internal,
                bbox.Min.Y - proximity_distance_internal,
                bbox.Min.Z
            ),
            DB.XYZ(
                bbox.Max.X + proximity_distance_internal,
                bbox.Max.Y + proximity_distance_internal,
                bbox.Max.Z + proximity_distance_internal
            )
        )
        return expanded_outline
    
    @staticmethod
    def get_element_center_point(spatial_wrapper):
        """Get center point of spatial element."""
        try:
            bbox = spatial_wrapper.bounding_box
            if bbox:
                center = DB.XYZ(
                    (bbox.Min.X + bbox.Max.X) / 2,
                    (bbox.Min.Y + bbox.Max.Y) / 2,
                    (bbox.Min.Z + bbox.Max.Z) / 2
                )
                return center
            return None
        except Exception as e:
            Debug.WriteLine("Error getting center point: {0}".format(str(e)))
            return None


class TestLogger(object):
    """Centralized logging for test results."""
    
    @staticmethod
    def log_test_start(test_name):
        """Log the start of a test."""
        Debug.WriteLine("=" * 50)
        Debug.WriteLine("Starting Test: {0}".format(test_name))
        Debug.WriteLine("=" * 50)
    
    @staticmethod
    def log_test_end(test_name, success=True):
        """Log the end of a test."""
        status = "COMPLETED" if success else "FAILED"
        Debug.WriteLine("Test {0}: {1}".format(test_name, status))
        Debug.WriteLine("=" * 50)
    
    @staticmethod
    def log_element_info(element, element_type="Element"):
        """Log basic element information."""
        try:
            element_name = element.Name if hasattr(element, 'Name') else 'Unknown'
            category_name = element.Category.Name if element.Category else 'Unknown Category'
            Debug.WriteLine("{0} Info:".format(element_type))
            Debug.WriteLine("  ID: {0}".format(element.Id))
            Debug.WriteLine("  Name: {0}".format(element_name))
            Debug.WriteLine("  Category: {0}".format(category_name))
        except Exception as e:
            Debug.WriteLine("Error logging {0} info: {1}".format(element_type, str(e)))
    
    @staticmethod
    def log_geometry_info(geometry, geometry_type="Geometry"):
        """Log geometry information and trace it."""
        Debug.WriteLine("{0} Information:".format(geometry_type))
        if geometry:
            Debug.WriteLine("  Type: {0}".format(type(geometry).__name__))
            Trace.Write(geometry)  # Visual trace
        else:
            Debug.WriteLine("  No geometry available")
    
    @staticmethod
    def log_bounding_box_info(bbox, bbox_type="Bounding Box"):
        # type: (DB.BoundingBoxXYZ, str) -> None
        """Log bounding box information."""
        Debug.WriteLine("{0} Information:".format(bbox_type))
        if bbox:
            Trace.Write(bbox)  # Visual trace
            Debug.WriteLine("  Min: {0}".format(bbox.Min))
            Debug.WriteLine("  Max: {0}".format(bbox.Max))
            Debug.WriteLine("  Size X: {0:.2f}".format(bbox.Max.X - bbox.Min.X))
            Debug.WriteLine("  Size Y: {0:.2f}".format(bbox.Max.Y - bbox.Min.Y))
            Debug.WriteLine("  Size Z: {0:.2f}".format(bbox.Max.Z - bbox.Min.Z))
        else:
            Debug.WriteLine("  No bounding box available")


class IntersectionTester(object):
    """Helper for testing MEP-spatial intersections."""
    
    def __init__(self, test_options=None):
        self.options = test_options or TestOptions()
    
    def test_mep_spatial_intersection(self, mep_element, spatial_wrapper, use_proximity=False, show_visual=False):
        """
        Test intersection between MEP element and spatial.
        Args:
            mep_element: Revit element
            spatial_wrapper: BaseSpatial wrapper
            use_proximity: Whether to use proximity detection
            show_visual: Whether to show visual debugging
        Returns:
            tuple: (is_intersecting, distance, details)
        """
        try:
            from lib.models.mep import BaseMep
            from lib.services.detection_service import DetectionService
            from lib.services.strategies import (RevitSpatialStrategy, GenericSpatialStrategy)
            from lib.viewmodel.advanced_options_viewmodel import AdvancedOptionsViewModel
            
            # Create MEP wrapper
            mep_wrapper = BaseMep(mep_element)
            
            # Setup advanced options
            advanced_options = AdvancedOptionsViewModel()
            if use_proximity:
                advanced_options.use_proximity_mapping = True
                advanced_options.nearest_allowed_distance = self.options.proximity_distance_mm
            
            # Create detection service
            detection_service = DetectionService(HOST_APP.doc, advanced_options)
            mep_type_info = detection_service.classify_mep_element(mep_wrapper)
            
            # Choose appropriate strategy
            from lib.models.spatial.revit_spatial import RevitSpatial
            from lib.models.spatial.generic_spatial import GenericSpatial
            
            if isinstance(spatial_wrapper, RevitSpatial):
                strategy = RevitSpatialStrategy(detection_service.geometry_detector)
            elif isinstance(spatial_wrapper, GenericSpatial):
                strategy = GenericSpatialStrategy(detection_service.geometry_detector)
            else:
                return False, 0, "Unknown spatial type"
            
            # Perform detection with visual option
            is_match, distance = strategy.detect(mep_wrapper, mep_type_info, spatial_wrapper, show_visual)
            
            details = {
                'mep_type_info': mep_type_info,
                'strategy_used': type(strategy).__name__ + (' (visual)' if show_visual else ''),
                'proximity_enabled': use_proximity,
                'visual_debugging': show_visual
            }
            
            return is_match, distance, details
            
        except Exception as e:
            Debug.WriteLine("Error in intersection test: {0}".format(str(e)))
            return False, 0, "Error: {0}".format(str(e))