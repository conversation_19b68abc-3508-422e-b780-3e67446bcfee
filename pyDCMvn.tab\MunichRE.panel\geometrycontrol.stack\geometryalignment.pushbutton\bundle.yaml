title: "Geometry Alignment"
tooltip:
  en_us: |
    Version = 1.2
    __________________________________________________________________
    Description:

    Use to check alignment between Revit elements and IFC elements
    __________________________________________________________________
    How-to:

    Step 1: Select IFC Excel data folder
    Step 2: Select IFC Excel file
    Step 3: Filter IFC elements on drop down conditions
    Step 4: Enter Alowable Offset
    Step 5: Run to check alignment
    __________________________________________________________________
    Last update:

    - [17.12.2024] - 1.0 RELEASE
    - [26.12.2024] - 1.1 RELEASE 
      - Fix Revit BoundingBox not found
      - Use IfcGuid instead of Mark
    - [17.02.2025] - 1.2 RELEASE
      - Add option to ignore IFC elements
      - Report group by IfcClass
author: "Giang Vu"
engine:
  clean: true