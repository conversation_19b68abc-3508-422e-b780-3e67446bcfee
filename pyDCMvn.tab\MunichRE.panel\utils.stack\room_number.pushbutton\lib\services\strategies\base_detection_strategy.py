# coding: utf-8

from DCMvn.core import DB

from ...models import Base<PERSON>ep, BaseSpatial
from ..detection import GeometryDetector
from ...utils import get_element_center_point

class BaseDetectionStrategy(object):
    """Base class for spatial detection strategies."""

    def __init__(self, geometry_detector):
        # type: (GeometryDetector) -> None
        self.geometry_detector = geometry_detector

    def detect(self, mep_element, mep_type_info, spatial_element, show_visual=False):  # noqa
        # type: (BaseMep, dict, BaseSpatial, bool) -> tuple[bool, float]
        raise NotImplementedError()

    @staticmethod
    def get_detection_points(mep_type_info):
        # type: (dict) -> list[DB.XYZ]
        """Get the best points for spatial detection, prioritizing room calculation points.
        
        Priority order:
        1. Room calculation points (most accurate)
        2. Location point
        3. Center point (fallback)
        
        Returns:
            list[DB.XYZ]: List of points to use for detection
        """
        points = []
        
        # Priority 1: Use room calculation points if available
        room_calc_points = mep_type_info.get('room_calc_points')
        if room_calc_points:
            if isinstance(room_calc_points, list):
                points.extend(room_calc_points)
            elif isinstance(room_calc_points, DB.XYZ):
                points.append(room_calc_points)
        
        if points:
            return points
        
        # Priority 2: Use location point if no room calc points
        if mep_type_info.get('point'):
            points.append(mep_type_info['point'])

        if points:
            return points
        
        # Priority 3: Use center point as fallback
        if mep_type_info.get('center_point'):
            points.append(mep_type_info['center_point'])
        
        if points:
            return points
        
        return points

    @staticmethod
    def check_geometry_spatial_intersection(mep_element, mep_type_info, spatial_element):
        # type: (BaseMep, dict, BaseSpatial) -> tuple[bool, float]
        """geometry intersection check using pre-classified element info."""
        try:
            if mep_type_info['center_point'] is not None:
                center_point = mep_type_info['center_point']
            elif mep_type_info['point'] is not None:
                center_point = mep_type_info['point']
            elif mep_type_info['curve'] is not None:
                center_point = mep_type_info['curve'].Evaluate(0.5, True)
            else:
                center_point = get_element_center_point(mep_element)
            
            if not center_point:
                return False, 0
                
            # Check if point is inside spatial element
            is_match = spatial_element.is_point_inside(center_point)
            return is_match, 0 # default distance see in geometry_detector.py
            
        except:  # noqa
            return False, 0