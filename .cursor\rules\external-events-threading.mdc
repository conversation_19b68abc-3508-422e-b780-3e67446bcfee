---
description: External event patterns and thread safety for Revit API operations
globs: ["**/events/*.py", "**/commands/*.py", "**/viewmodel/*.py"]
alwaysApply: false
---

# External Events and Thread Safety

## Core Principles
- **UI Thread Safety**: All Revit API operations must occur on the main thread
- **External Events**: Use `IExternalEventHandler` for safe API calls from UI
- **Transaction Management**: Wrap all document modifications in transactions
- **Error Isolation**: Prevent UI thread exceptions from crashing Revit

## External Event Handler Pattern
```python
class ActionEventHandler(UI.IExternalEventHandler):
    def __init__(self):
        self._external_event = UI.ExternalEvent.Create(self)
        self._action = None
        
    def Execute(self, application):
        """Called by Revit on main thread"""
        if self._action:
            try:
                self._action(application)
            except Exception as e:
                Debug.WriteLine("External event error: {}".format(e))
            finally:
                self._action = None
                
    def Raise(self, action):
        """Queue action for execution on main thread"""
        if HOST_APP.uiapp.ActiveAddInId is not None:
            # Direct execution if already in API context
            action(HOST_APP.uiapp)
        else:
            # Queue for external event
            self._action = action
            self._external_event.Raise()
```

## Command Pattern with External Events
```python
class ExecuteCommand(RelayCommand):
    def __init__(self, view_model):
        super().__init__(self.execute, self.can_execute)
        self.view_model = view_model
        
    def execute(self, parameter):
        def api_operation(ui_app):
            doc = ui_app.ActiveUIDocument.Document
            with DB.Transaction(doc, "Operation Name") as trans:
                trans.Start()
                try:
                    # Perform Revit API operations
                    trans.Commit()
                except Exception:
                    trans.RollBack()
                    raise
                    
        # Execute safely via external event
        self.view_model.action_event_handler.Raise(api_operation)
```

## Transaction Patterns
- **Single Operations**: Use `DB.Transaction(doc, "description")`
- **Multiple Operations**: Use `DB.TransactionGroup(doc, "group_name")`
- **Batch Operations**: Combine related operations in single transaction
- **Error Handling**: Always rollback on exceptions

## ViewModel Integration
- Store `ActionEventHandler` reference in main ViewModel
- Pass handler to commands that need API access
- Use external events for any operation that modifies the document
- Keep UI responsive during long operations

## Best Practices
- Never call Revit API directly from UI event handlers
- Use external events even for read-only operations from UI threads
- Implement proper disposal of external event handlers
- Handle API mode detection for direct execution scenarios

[external-event-template.py](mdc:external-event-template.py)