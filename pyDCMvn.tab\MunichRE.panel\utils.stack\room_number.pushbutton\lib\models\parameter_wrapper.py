# coding: utf-8
from DCMvn.core import DB


class ParameterWrapper(object):
    """
    Represents a parameter item with a name, definition, and built-in parameter information.
    
    This class encapsulates information about a parameter, including its name, optional definition,
    and whether it corresponds to a built-in parameter.
    """
    
    def __init__(self, name="", element_id=None, definition=None, builtin_parameter=None):
        # type: (str, DB.ElementId, DB.Definition, DB.BuiltInParameter) -> None
        self.__name = name or ""
        self.__element_id = element_id or DB.ElementId.InvalidElementId
        self.__definition = definition
        self.__builtin_parameter = builtin_parameter or DB.BuiltInParameter.INVALID
    
    @property
    def name(self):
        # type: () -> str
        """Get the parameter name"""
        return self.__name
    
    @property
    def element_id(self):
        # type: () -> DB.ElementId
        """Get the parameter element ID"""
        return self.__element_id
    
    @property
    def definition(self):
        # type: () -> DB.Definition
        """Get the parameter definition"""
        return self.__definition
    
    @property
    def builtin_parameter(self):
        # type: () -> DB.BuiltInParameter
        """Get the built-in parameter type"""
        return self.__builtin_parameter
    
    @property
    def is_builtin(self):
        # type: () -> bool
        """Check if this is a built-in parameter"""
        return self.__builtin_parameter != DB.BuiltInParameter.INVALID
    
    def get_parameter_from_element(self, element):
        # type: (DB.Element) -> DB.Parameter | None
        """Get the parameter from the specified element"""
        if self.is_builtin:
            return element.get_Parameter(self.__builtin_parameter)  # noqa
        elif self.__definition is not None:
            return element.get_Parameter(self.__definition)  # noqa
        elif self.__name:
            return element.LookupParameter(self.__name)
        return None
    
    def is_writable_on_element(self, element):
        # type: (DB.Element) -> bool
        """Check if this parameter is writable on the specified element"""
        param = self.get_parameter_from_element(element)
        return param is not None and not param.IsReadOnly
    
    def __str__(self):
        # type: () -> str
        param_type = "BuiltIn" if self.is_builtin else "Custom"
        return "ParameterWrapper(name='{}', type={})".format(self.__name, param_type)
    
    def __repr__(self):
        # type: () -> str
        return self.__str__()
    
    def __eq__(self, other):
        # type: (object) -> bool
        if not isinstance(other, ParameterWrapper):
            return False
        return (self.__name == other.__name and 
                self.__builtin_parameter == other.__builtin_parameter and
                self.__definition == other.__definition)
    
    def __hash__(self):
        # type: () -> int
        return hash((self.__name, self.__builtin_parameter)) 