---
description: Collection optimization patterns for DCMvn applications with large datasets
---

# DCMvn Collection Optimization Patterns

## Fast Lookup Dictionaries
Always build lookup dictionaries for large collections to improve performance:

```python
class OptimizedDataManager(object):
    def __init__(self):
        # Raw collections
        self._all_elements = ObservableCollection[System.Object]()
        
        # Fast lookup dictionaries
        self._elements_by_id = {}          # ID -> Element
        self._elements_by_category = {}    # Category -> set(IDs)
        self._elements_by_level = {}       # Level -> set(IDs)
        self._elements_by_system = {}      # System -> set(IDs)
        
    def build_indexes(self):
        """Build lookup indexes for fast filtering."""
        # Clear existing indexes
        self._elements_by_id.clear()
        self._elements_by_category.clear()
        self._elements_by_level.clear()
        self._elements_by_system.clear()
        
        # Build indexes
        for element in self._all_elements:
            element_id = element.id
            self._elements_by_id[element_id] = element
            
            # Index by category
            if element.category:
                if element.category not in self._elements_by_category:
                    self._elements_by_category[element.category] = set()
                self._elements_by_category[element.category].add(element_id)
            
            # Index by level
            if element.level:
                if element.level not in self._elements_by_level:
                    self._elements_by_level[element.level] = set()
                self._elements_by_level[element.level].add(element_id)
            
            # Index by system
            if hasattr(element, 'system') and element.system:
                if element.system not in self._elements_by_system:
                    self._elements_by_system[element.system] = set()
                self._elements_by_system[element.system].add(element_id)
```

## Set-Based Filtering for Performance
Use set operations for fast filtering of large collections:

```python
def apply_fast_filters(self, category_filter=None, level_filter=None, 
                      system_filter=None, guid_filter=None):
    """Apply filters using set intersection for performance."""
    # Start with all element IDs
    all_ids = set(self._elements_by_id.keys())
    filtered_ids = all_ids
    
    # Apply category filter
    if category_filter and category_filter != "All":
        category_ids = self._elements_by_category.get(category_filter, set())
        filtered_ids = filtered_ids.intersection(category_ids)
    
    # Apply level filter
    if level_filter and level_filter != "All":
        level_ids = self._elements_by_level.get(level_filter, set())
        filtered_ids = filtered_ids.intersection(level_ids)
    
    # Apply system filter
    if system_filter and system_filter != "All":
        system_ids = self._elements_by_system.get(system_filter, set())
        filtered_ids = filtered_ids.intersection(system_ids)
    
    # Apply GUID filter (for specific selections)
    if guid_filter:
        guid_set = set(guid_filter)
        filtered_ids = filtered_ids.intersection(guid_set)
    
    return filtered_ids

def update_filtered_collection(self, filtered_ids):
    """Update the filtered collection efficiently."""
    self._filtered_elements.Clear()
    
    # Add elements in batch for better performance
    for element_id in filtered_ids:
        element = self._elements_by_id.get(element_id)
        if element:
            self._filtered_elements.Add(element)
```

## Batch Processing Pattern
Process large collections in batches to maintain responsiveness:

```python
def process_elements_in_batches(self, elements, processor_func, 
                               batch_size=100, progress_callback=None):
    """Process elements in batches for better performance and feedback."""
    total_elements = len(elements)
    processed_results = []
    
    for i in range(0, total_elements, batch_size):
        batch = elements[i:i + batch_size]
        batch_results = []
        
        for element in batch:
            try:
                result = processor_func(element)
                if result:
                    batch_results.append(result)
            except Exception as e:
                from DCMvn.core.framework import Debug
                Debug.WriteLine("Error processing element: {}".format(str(e)))
        
        processed_results.extend(batch_results)
        
        # Report progress
        if progress_callback:
            progress_callback(min(i + batch_size, total_elements), total_elements)
    
    return processed_results
```

## Memory-Efficient Data Loading
Load and process data efficiently for large datasets:

```python
def load_data_efficiently(self, data_source):
    """Load data with memory efficiency in mind."""
    # Clear existing data
    self._all_elements.Clear()
    
    # Process in chunks to avoid memory spikes
    chunk_size = 1000
    processed_count = 0
    
    try:
        for chunk in self.get_data_chunks(data_source, chunk_size):
            processed_chunk = []
            
            for raw_item in chunk:
                try:
                    # Only keep essential data to reduce memory
                    processed_item = self.create_lightweight_item(raw_item)
                    if processed_item:
                        processed_chunk.append(processed_item)
                except Exception:
                    continue  # Skip invalid items
            
            # Add chunk to collection
            for item in processed_chunk:
                self._all_elements.Add(item)
            
            processed_count += len(processed_chunk)
            
            # Trigger UI update periodically
            if processed_count % (chunk_size * 5) == 0:
                self.RaisePropertyChanged("DataCount")
    
    finally:
        # Final update and index rebuild
        self.build_indexes()
        self.RaisePropertyChanged("DataCount")

def create_lightweight_item(self, raw_item):
    """Create a lightweight version of the item with only essential data."""
    # Only store what's needed for display and filtering
    return {
        'id': raw_item.id,
        'name': raw_item.name,
        'category': raw_item.category,
        'level': raw_item.level,
        'essential_data': raw_item.essential_field
        # Avoid storing large objects or computed properties
    }
```

## Lazy Loading for Expensive Properties
Implement lazy loading for expensive computations:

```python
class LazyDataElement(object):
    def __init__(self, raw_data):
        self._raw_data = raw_data
        self._geometry = None  # Expensive to compute
        self._detailed_info = None  # Expensive to retrieve
        
    @property
    def geometry(self):
        """Lazy-loaded geometry property."""
        if self._geometry is None:
            try:
                self._geometry = self.compute_geometry()
            except Exception:
                self._geometry = False  # Mark as failed
        return self._geometry if self._geometry is not False else None
    
    @property  
    def detailed_info(self):
        """Lazy-loaded detailed information."""
        if self._detailed_info is None:
            try:
                self._detailed_info = self.fetch_detailed_info()
            except Exception:
                self._detailed_info = False
        return self._detailed_info if self._detailed_info is not False else None
```

## Collection Synchronization Pattern
Keep multiple collections in sync efficiently:

```python
def synchronize_collections(self, source_collection, target_collection_map):
    """Synchronize multiple collections based on source changes."""
    # Get current source IDs
    source_ids = set(item.id for item in source_collection)
    
    # Update each target collection
    for collection_name, target_collection in target_collection_map.items():
        # Get current target IDs
        current_target_ids = set(item.id for item in target_collection)
        
        # Find items to add and remove
        to_add = source_ids - current_target_ids
        to_remove = current_target_ids - source_ids
        
        # Remove items that are no longer in source
        items_to_remove = [item for item in target_collection if item.id in to_remove]
        for item in items_to_remove:
            target_collection.Remove(item)
        
        # Add new items from source
        source_lookup = {item.id: item for item in source_collection}
        for item_id in to_add:
            if item_id in source_lookup:
                target_collection.Add(source_lookup[item_id])
```

## Performance Monitoring
Add performance monitoring for optimization:

```python
def monitor_performance(operation_name):
    """Decorator to monitor operation performance."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                end_time = time.time()
                duration = (end_time - start_time) * 1000  # Convert to ms
                
                from DCMvn.core.framework import Debug
                Debug.WriteLine("{} took {:.2f}ms".format(operation_name, duration))
                
                # Log slow operations
                if duration > 1000:  # Log operations > 1 second
                    Debug.WriteLine("SLOW OPERATION: {} took {:.2f}ms".format(
                        operation_name, duration
                    ))
        
        return wrapper
    return decorator

# Usage:
@monitor_performance("Data Loading")
def load_large_dataset(self):
    # Implementation here
    pass
```

## Memory Cleanup Pattern
Properly clean up collections to prevent memory leaks:

```python
def cleanup_collections(self):
    """Clean up collections and references."""
    try:
        # Clear ObservableCollections
        if hasattr(self, '_all_elements'):
            self._all_elements.Clear()
        
        if hasattr(self, '_filtered_elements'):
            self._filtered_elements.Clear()
        
        # Clear dictionaries
        if hasattr(self, '_elements_by_id'):
            self._elements_by_id.clear()
        
        if hasattr(self, '_elements_by_category'):
            self._elements_by_category.clear()
        
        # Force garbage collection for large cleanups
        import gc
        gc.collect()
        
    except Exception as e:
        from DCMvn.core.framework import Debug
        Debug.WriteLine("Error during cleanup: {}".format(str(e)))
```

## Best Practices for Collection Optimization
1. **Build lookup dictionaries** for frequently queried data
2. **Use set operations** for filtering instead of loops
3. **Process data in batches** for large collections
4. **Implement lazy loading** for expensive properties
5. **Monitor performance** of critical operations
6. **Clean up properly** to prevent memory leaks
7. **Cache computed values** when possible
8. **Use lightweight objects** for display collections
9. **Update UI periodically** during long operations
10. **Profile and measure** actual performance bottlenecks