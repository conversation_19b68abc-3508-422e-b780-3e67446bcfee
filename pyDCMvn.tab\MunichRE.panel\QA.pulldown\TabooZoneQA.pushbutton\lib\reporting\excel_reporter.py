# coding: utf-8
"""
Excel reporting for TabooZone QA DTO validation results.
Supports rule-provided dynamic columns and consolidated exports.
"""
import os
import clr
from DCMvn.core.framework import Debug
from DCMvn.coreutils.assemblyhandler import load_miniexcel

try:
    load_miniexcel()
    from MiniExcelLibs import MiniExcel
    MINIEXCEL_AVAILABLE = True
except Exception as e:
    Debug.WriteLine("ExcelReporter: Failed to load MiniExcel: {}".format(str(e)))
    MINIEXCEL_AVAILABLE = False

clr.AddReference("System.Collections.Specialized")
from System.Collections.Specialized import OrderedDictionary  # noqa: F401
from System.Collections.Generic import Dictionary  # noqa: F401

from ..validation import constants
from ..validation.models import ValidationResult, MatchedPair  # noqa: F401


class ExcelReporter(object):
    def __init__(self):
        pass

    @staticmethod
    def ordered_row(pairs):
        # type: (list[tuple[str, object]]) -> OrderedDictionary
        row = OrderedDictionary()
        for k, v in pairs:
            row[k] = v
        return row

    @staticmethod
    def base_row(mp, rule_name=None):
        # type: (MatchedPair, str | None) -> list[tuple[str, object]]
        """Create base row data for a matched pair.

        Args:
            rule_name (str | None): optional rule name to include in each row
            mp (MatchedPair): matched pair DTO

        Returns:
            list[tuple[str, object]]: base row data
        """
        row = [
            (constants.GUID, mp.guid),
            (constants.ARC_ID, mp.arc_info.element_id or ""),
            (constants.MASS_ID, mp.mass_info.element_id or ""),
            (constants.ARC_INFO, mp.arc_info.display_string),
            (constants.MASS_INFO, mp.mass_info.display_string),
        ]
        if rule_name:
            row.append((constants.RULE_NAME, rule_name))
        return row

    @staticmethod
    def get_from_row(row, key):
        # type: (dict, str) -> object
        """Safely get a value from an Excel row (OrderedDictionary).
        Returns None if not present.
        """
        try:
            return row[key]
        except Exception as ex:
            Debug.WriteLine("ExcelReporter: _get_from_row error: {}".format(str(ex)))
            return None

    @staticmethod
    def _generate_concluded_status(row, concluded_parameter_dependency):
        # type: (dict, dict[str, str] | None) -> str
        """Return 'Passed' only if all criteria key/value pairs match the row values.
        Each criterion is a dict of {column_name: expected_value}.
        Missing columns or mismatches yield 'Failed'.

        Args:
            row (dict): Excel row (OrderedDictionary)
            concluded_parameter_dependency (dict[str, str] | None): columns and their expected values to determine "Concluded" status
        Returns:
            str: "Passed" or "Failed"
        """
        try:
            if not concluded_parameter_dependency:
                return ""
            for col_name, expected in concluded_parameter_dependency.items():
                actual = ExcelReporter.get_from_row(row, col_name)
                if actual is None:
                    return constants.FAILED
                try:
                    if str(actual).strip().lower() != str(expected).strip().lower():
                        return constants.FAILED
                except Exception as ex:
                    Debug.WriteLine("ExcelReporter: concluded status comparison error: {}".format(str(ex)))
                    return constants.FAILED
            return constants.PASSED
        except Exception as ex:
            Debug.WriteLine("ExcelReporter: concluded status error: {}".format(str(ex)))
            return constants.FAILED

    def export_validation_result(self, validation_result, file_path=None, rule_name=None, concluded_parameter_dependency=None):
        # type: (ValidationResult, str | None, str | None, dict[str, str] | None) -> bool
        if not MINIEXCEL_AVAILABLE:
            return False
        try:
            sheets = self._build_excel_sheets(validation_result, rule_name=rule_name, concluded_parameter_dependency=concluded_parameter_dependency)
            if not file_path:
                try:
                    from DCMvn.io import save_excel_file
                    file_path = save_excel_file(title="Save TabooZone Validation Report")
                except Exception as ex:
                    Debug.WriteLine("ExcelReporter: save dialog failed: {}".format(str(ex)))
                    file_path = None
            if not file_path:
                return False
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as ex:
                Debug.WriteLine("ExcelReporter: remove existing failed: {}".format(str(ex)))
            MiniExcel.SaveAs(file_path, sheets)
            return True
        except Exception as ex:
            Debug.WriteLine("ExcelReporter: export failed: {}".format(str(ex)))
            return False

    def _build_excel_sheets(self, result, rule_name=None, concluded_parameter_dependency=None):
        # type: (ValidationResult, str | None, dict[str, str] | None) -> Dictionary[str, object]
        """Build a single Total sheet per"""
        sheets = Dictionary[str, object]()
        total_rows = self.build_total_sheet(result, rule_name=rule_name, concluded_parameter_dependency=concluded_parameter_dependency)
        sheets[constants.TOTAL_SHEET] = total_rows
        return sheets

    def build_total_sheet(self, result, rule_name=None, concluded_parameter_dependency=None):
        # type: (ValidationResult, str | None, dict[str, str] | None) -> list[OrderedDictionary]
        rows = []
        # Precompute centroid results per unique pair (guid, arc_id, mass_id)
        centroid_by_pair = {}
        for cp in getattr(result, "centroid_pairs", []) or []:
            try:
                key = (cp.guid, cp.arc_info.element_id, cp.mass_info.element_id)
                centroid_by_pair[key] = (cp.distance_rounded, cp.within_tolerance)
            except Exception as ex:
                Debug.WriteLine("ExcelReporter: centroid pair error: {}".format(str(ex)))
                continue
        # Matched rows first
        for mp in getattr(result, "matched_pairs", []) or []:
            base = self.base_row(mp, rule_name)  # Don't pass concluded_parameter_dependency to base_row
            issue = constants.MATCHED
            key = (mp.guid, mp.arc_info.element_id, mp.mass_info.element_id)
            d, passed = (None, None)
            if key in centroid_by_pair:
                d, passed = centroid_by_pair[key]
            c = "" if d is None else (constants.PASSED if passed else constants.FAILED)
            row_data = base + [
                (constants.GUID_VALIDATION, issue),
                (constants.CENTROID_DISTANCE_MM, d if d is not None else ""),
                (constants.CENTROID_RESULT_COLUMN, c),
                (constants.CONCLUDED, ""),
                (constants.APPROVER, ""),
                (constants.REASON, ""),
            ]
            row = self.ordered_row(row_data)

            if concluded_parameter_dependency:
                concluded_status = self._generate_concluded_status(row, concluded_parameter_dependency)
                row[constants.CONCLUDED] = concluded_status
            
            rows.append(row)

        # Missing
        for gp in getattr(result, "missing_pairs", []) or []:
            row = [
                (constants.GUID, gp.guid),
                (constants.ARC_ID, gp.element_info.element_id or ""),
                (constants.MASS_ID, ""),
                (constants.ARC_INFO, gp.element_info.display_string),
                (constants.MASS_INFO, ""),
                (constants.GUID_VALIDATION, constants.MISSING),
                (constants.CENTROID_DISTANCE_MM, ""),
                (constants.CENTROID_RESULT_COLUMN, ""),
                (constants.CONCLUDED, ""),
                (constants.APPROVER, ""),
                (constants.REASON, ""),
            ] + ([(constants.RULE_NAME, rule_name)] if rule_name else [])
            rows.append(self.ordered_row(row))
        # Extra
        for gp in getattr(result, "extra_pairs", []) or []:
            row = [
                (constants.GUID, gp.guid),
                (constants.ARC_ID, ""),
                (constants.MASS_ID, gp.element_info.element_id or ""),
                (constants.ARC_INFO, ""),
                (constants.MASS_INFO, gp.element_info.display_string),
                (constants.GUID_VALIDATION, constants.EXTRA),
                (constants.CENTROID_DISTANCE_MM, ""),
                (constants.CENTROID_RESULT_COLUMN, ""),
                (constants.CONCLUDED, ""),
                (constants.APPROVER, ""),
                (constants.REASON, ""),
            ] + ([(constants.RULE_NAME, rule_name)] if rule_name else [])
            rows.append(self.ordered_row(row))
        # Duplicates
        for gp in getattr(result, "duplicate_arc_pairs", []) or []:
            row = [
                (constants.GUID, gp.guid),
                (constants.ARC_ID, gp.element_info.element_id or ""),
                (constants.MASS_ID, ""),
                (constants.ARC_INFO, gp.element_info.display_string),
                (constants.MASS_INFO, ""),
                (constants.GUID_VALIDATION, constants.DUPLICATE_ARC_GUID),
                (constants.CENTROID_DISTANCE_MM, ""),
                (constants.CENTROID_RESULT_COLUMN, ""),
                (constants.CONCLUDED, ""),
                (constants.APPROVER, ""),
                (constants.REASON, ""),
            ] + ([(constants.RULE_NAME, rule_name)] if rule_name else [])
            rows.append(self.ordered_row(row))
        for gp in getattr(result, "duplicate_mass_pairs", []) or []:
            row = [
                (constants.GUID, gp.guid),
                (constants.ARC_ID, ""),
                (constants.MASS_ID, gp.element_info.element_id or ""),
                (constants.ARC_INFO, ""),
                (constants.MASS_INFO, gp.element_info.display_string),
                (constants.GUID_VALIDATION, constants.DUPLICATE_MASS_GUID),
                (constants.CENTROID_DISTANCE_MM, ""),
                (constants.CENTROID_RESULT_COLUMN, ""),
                (constants.CONCLUDED, ""),
                (constants.APPROVER, ""),
                (constants.REASON, ""),
            ] + ([(constants.RULE_NAME, rule_name)] if rule_name else [])
            rows.append(self.ordered_row(row))
        return rows


class ConsolidatedExcelReporter(object):
    """Aggregate results from multiple rules into one file/sheet."""
    def __init__(self):
        self._rows = []  # store rows across rules

    def add_result(self, validation_result, rule_name=None):
        # type: (ValidationResult, str | None) -> None
        reporter = ExcelReporter()
        rows = reporter.build_total_sheet(validation_result, rule_name=rule_name)
        self._rows.extend(rows)

    def export(self, file_path=None):
        # type: (str | None) -> bool
        if not MINIEXCEL_AVAILABLE:
            return False
        try:
            sheets = Dictionary[str, object]()
            sheets[constants.TOTAL_SHEET] = self._rows
            if not file_path:
                try:
                    from DCMvn.io import save_excel_file
                    file_path = save_excel_file(title="Save TabooZone Consolidated Report")
                except Exception as ex:
                    Debug.WriteLine("ConsolidatedExcelReporter: save dialog failed: {}".format(str(ex)))
                    file_path = None
            if not file_path:
                return False
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as ex:
                Debug.WriteLine("ConsolidatedExcelReporter: remove existing failed: {}".format(str(ex)))
            MiniExcel.SaveAs(file_path, sheets)
            return True
        except Exception as ex:
            Debug.WriteLine("ConsolidatedExcelReporter: export failed: {}".format(str(ex)))
            return False

