title: "Transfer Parameter From Excel"
tooltip:
  en_us: |
    Version = 1.0
    __________________________________________________________________
    Description:
    This script updates Revit element parameters based on an Excel data source. It matches elements using a primary key (defined in the second row of Mapping List) and maps Excel data fields to Revit parameters.
    It ensures that parameters are correctly updated while tracking unmatched primary key values and skipped parameters._______________________________________________________________
    How-to:
    Step 01: Select the Mapping List file (Excel) that defines parameter mappings between Excel and Revit.
    Step 02: Select the Data File (Excel) containing the parameter values to update in Revit.
    __________________________________________________________________
    Last update:

    - [15.03.2025] - 1.0 First release

author: "Long Dang"
engine:
  clean: true