# coding: utf-8
from DCMvn.core import get_output
from DCMvn.core.framework import Debug, List  # noqa: F401
from ..models import DetectionResult  # noqa: F401


class ReportService(object):
    """Service for generating HTML reports of mapping and detection results."""
    
    def __init__(self):
        self.output = get_output()
        self.output.close_others()
        
    def generate_mapping_report(self, detection_results, advanced_options, execution_time_ms=None):
        # type: (list[DetectionResult], object, str) -> None
        """Generate comprehensive mapping report with results and statistics."""
        
        self.output.print_html('<h2 style="color:orange; border-bottom: 2px solid #007acc; padding-bottom: 5px;"> :derelict_house: SPATIAL MAPPING REPORT</h2>')
        
        # Calculate statistics
        unique_results = set(detection_results)
        total_mep = len(unique_results)
        successful_mappings = [r for r in unique_results if r.has_spatial_match]
        failed_mappings = [r for r in unique_results if not r.has_spatial_match]
        multiple_mappings = [r for r in successful_mappings if r.has_multiple_matches]
        
        success_count = len(successful_mappings)
        failed_count = len(failed_mappings)
        multiple_count = len(multiple_mappings)
        
        # Summary section
        self._print_summary_section(total_mep, success_count, failed_count, multiple_count)
        
        # Performance section
        if execution_time_ms is not None:
            self._print_performance_section(execution_time_ms, total_mep)
        
        # Configuration section
        self._print_configuration_section(advanced_options)
        
        # Failed mappings section (only if logging enabled)
        if failed_mappings and advanced_options.log_unmapped_elements:  # noqa
            self._print_failed_mappings_section(failed_mappings)
        
        # Footer
        self.output.print_html('<hr style="margin-top: 20px; border: 1px solid #ccc;">')
        self.output.print_html('<p style="color: gray; font-size: 0.9em; text-align: center;">Report generated by pyDCMvn Room Number Mapping Tool</p>')
        
        Debug.WriteLine("Mapping report generated with {} total elements".format(total_mep))
        
    def _print_summary_section(self, total_mep, success_count, failed_count, multiple_count):
        # type: (int, int, int, int) -> None
        """Print the summary statistics section."""
        
        self.output.insert_divider()
        self.output.print_html('<h3 style="color:blue;"> :chart_decreasing: Summary Statistics</h3>')
        
        self.output.print_html('<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">')
        self.output.print_html('<table style="width: 100%; border-collapse: collapse;">')
        
        self.output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Total MEP Elements:</td><td style="padding: 5px;"><strong style="color: blue;">{}</strong></td></tr>'.format(total_mep))
        self.output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Successfully Mapped:</td><td style="padding: 5px;"><strong style="color: green;">{}</strong></td></tr>'.format(success_count))
        self.output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Failed to Map:</td><td style="padding: 5px;"><strong style="color: red;">{}</strong></td></tr>'.format(failed_count))
        
        if multiple_count > 0:
            self.output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Multiple Matches:</td><td style="padding: 5px;"><strong style="color: orange;">{}</strong></td></tr>'.format(multiple_count))
            
        success_rate = (success_count * 100.0 / total_mep) if total_mep > 0 else 0
        color = "green" if success_rate >= 80 else "orange" if success_rate >= 60 else "red"
        self.output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Success Rate:</td><td style="padding: 5px;"><strong style="color: {};">{:.1f}%</strong></td></tr>'.format(color, success_rate))
        
        self.output.print_html('</table>')
        self.output.print_html('</div>')
        
    def _print_performance_section(self, execution_time_ms, total_mep):
        # type: (float, int) -> None
        """Print the performance statistics section."""
        
        self.output.insert_divider()
        self.output.print_html('<h3 style="color:blue;"> :stopwatch: Performance Statistics</h3>')
        
        self.output.print_html('<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">')
        self.output.print_html('<table style="width: 100%; border-collapse: collapse;">')

        self.output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Execution Time:</td><td style="padding: 5px;"><strong style="color: purple;">{} H:M:S</strong></td></tr>'.format(execution_time_ms))

        self.output.print_html('</table>')
        self.output.print_html('</div>')
        
    def _print_configuration_section(self, advanced_options):
        # type: (object) -> None
        """Print the configuration settings section with colorful styling."""
        
        self.output.print_html('<h3 style="color:blue;"> :gear: Configuration Settings</h3>')
        
        self.output.print_html('<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px; margin: 15px 0; color: white; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">')
        
        settings = []
        if advanced_options.map_elements_above_spaces:  # noqa
            settings.append('<div style="background: rgba(255,255,255,0.15); padding: 8px; border-radius: 5px; margin: 5px 0;"><strong>📏 Elements Above Spaces:</strong> {} mm offset</div>'.format(advanced_options.above_allowed_distance))  # noqa
        if advanced_options.use_proximity_mapping:  # noqa
            settings.append('<div style="background: rgba(255,255,255,0.15); padding: 8px; border-radius: 5px; margin: 5px 0;"><strong>📐 Proximity Mapping:</strong> {} mm distance</div>'.format(advanced_options.nearest_allowed_distance))  # noqa
        if advanced_options.use_default_value_when_not_found:  # noqa
            settings.append('<div style="background: rgba(255,255,255,0.15); padding: 8px; border-radius: 5px; margin: 5px 0;"><strong>🏷️ Default Value:</strong> "{}"</div>'.format(advanced_options.default_value_when_not_found))  # noqa
        if advanced_options.allow_multiple_values:  # noqa
            settings.append('<div style="background: rgba(255,255,255,0.15); padding: 8px; border-radius: 5px; margin: 5px 0;"><strong>🔗 Multiple Values:</strong> Separator "{}"</div>'.format(advanced_options.separator_value))  # noqa
        if advanced_options.override_existing_assignments:  # noqa
            settings.append('<div style="background: rgba(255,255,255,0.15); padding: 8px; border-radius: 5px; margin: 5px 0;"><strong>🔄 Override Existing:</strong> Enabled</div>')
        if advanced_options.log_unmapped_elements:  # noqa
            settings.append('<div style="background: rgba(255,255,255,0.15); padding: 8px; border-radius: 5px; margin: 5px 0;"><strong>📝 Detailed Logging:</strong> Enabled</div>')
            
        if settings:
            for setting in settings:
                self.output.print_html(setting)
        else:
            self.output.print_html('<div style="background: rgba(255,255,255,0.15); padding: 8px; border-radius: 5px; margin: 5px 0;"><strong>⚙️ Standard Mapping:</strong> Default settings only</div>')
            
        self.output.print_html('</div>')
            
    def _print_successful_mappings_section(self, successful_mappings, advanced_options):
        # type: (List[object], object) -> None
        """Print the successful mappings section."""
        
        self.output.insert_divider()
        self.output.print_html('<h3 style="color:green;"> :white_check_mark: Successful Mappings ({} elements)</h3>'.format(len(successful_mappings)))
        
        # Group by detection method
        method_groups = {}
        for result in successful_mappings:
            method = result.detection_method
            if method not in method_groups:
                method_groups[method] = []
            method_groups[method].append(result)
            
        for method, results in method_groups.items():
            self.output.print_html('<h4 style="color: #2c5aa0;">Detection Method: {} ({} elements)</h4>'.format(method.title(), len(results)))
            
            for result in results[:10]:  # Show first 10 results per method
                mep_id = result.mep_element.id
                mep_category = result.mep_element.get_category_name()
                spatial_ids = [spatial.id for spatial in result.spatial_elements]
                
                value = result.get_spatial_values(advanced_options.separator_value) if advanced_options.allow_multiple_values else result.get_spatial_value()  # noqa
                
                self.output.print_html('<div style="margin-left: 20px; padding: 3px 0;">MEP {} ({}) → Spatial {} → Value: <strong>{}</strong></div>'.format(
                    self.output.linkify(mep_id, result.mep_element.guid),
                    mep_category,
                    spatial_ids,
                    value or "N/A"
                ))
                
            if len(results) > 10:
                self.output.print_html('<div style="margin-left: 20px; font-style: italic;">... and {} more elements</div>'.format(len(results) - 10))
                
    def _print_failed_mappings_section(self, failed_mappings):
        # type: (List[object]) -> None
        """Print the failed mappings section for unmapped elements."""
        
        self.output.insert_divider()
        self.output.print_html('<h3 style="color:red;"> :cross_mark: Failed Mappings ({} elements)</h3>'.format(len(failed_mappings)))
        
        # Group by category
        category_groups = {}
        for result in failed_mappings:
            category = result.mep_element.get_category_name()
            if category not in category_groups:
                category_groups[category] = []
            category_groups[category].append(result)
            
        for category, results in category_groups.items():
            self.output.print_html('<h4 style="color: #d32f2f;">Category: {} ({} elements)</h4>'.format(category, len(results)))
            
            for result in results[:20]:  # Show first 20 failed results per category
                mep_id = result.mep_element.id
                self.output.print_html('<div style="margin-left: 20px; padding: 3px 0;">MEP {} - No spatial element found</div>'.format(
                    self.output.linkify(mep_id, result.mep_element.guid)
                ))
                
            if len(results) > 20:
                self.output.print_html('<div style="margin-left: 20px; font-style: italic;">... and {} more elements</div>'.format(len(results) - 20))
                
    def _print_multiple_mappings_section(self, multiple_mappings, advanced_options):
        # type: (List[object], object) -> None
        """Print the multiple mappings section."""
        
        self.output.insert_divider()
        self.output.print_html('<h3 style="color:orange;"> :arrows_counterclockwise: Multiple Matches ({} elements)</h3>'.format(len(multiple_mappings)))
        
        for result in multiple_mappings[:15]:  # Show first 15 multiple match results
            mep_id = result.mep_element.id
            mep_category = result.mep_element.get_category_name()
            spatial_ids = [spatial.id for spatial in result.spatial_elements]
            combined_value = result.get_spatial_values(advanced_options.separator_value)  # noqa
            
            self.output.print_html('<div style="margin-left: 20px; padding: 3px 0;">MEP {} ({}) → {} spatial elements {} → Combined: <strong>{}</strong></div>'.format(
                self.output.linkify(mep_id, result.mep_element.guid),
                mep_category,
                len(result.spatial_elements),
                spatial_ids,
                combined_value or "N/A"
            ))
            
        if len(multiple_mappings) > 15:
            self.output.print_html('<div style="margin-left: 20px; font-style: italic;">... and {} more elements</div>'.format(len(multiple_mappings) - 15)) 