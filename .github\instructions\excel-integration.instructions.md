---
applyTo: "**/excel/**/*.py"
---

# Excel Integration with MiniExcel

## Required Imports for Excel Operations
Always use this import pattern:
```python
# coding: utf-8
import clr
import os

# DCMvn core imports
from DCMvn.io import save_excel_file
from DCMvn.coreutils.assemblyhandler import load_miniexcel
from DCMvn.core.framework import IDictionary, Debug
from DCMvn.forms import alert

# Load MiniExcel assembly at module level
load_miniexcel()
from MiniExcelLibs import MiniExcel
from MiniExcelLibs.OpenXml import OpenXmlConfiguration

# .NET collections support
clr.AddReference("System.Collections.Specialized")
from System.Collections.Specialized import OrderedDictionary
from System.Collections.Generic import Dictionary
```

## Safe Excel File Operations Pattern
Always follow this pattern:

### 1. File Path Selection
```python
file_path = save_excel_file(title="Save Report Name")
if not file_path:
    return None  # User cancelled
```

### 2. File Cleanup and Exception Handling
```python
try:
    # Clean existing file
    if os.path.exists(file_path):
        os.remove(file_path)
    
    # Create data structure
    data = OrderedDictionary()
    
    # Save with MiniExcel
    MiniExcel.SaveAs(file_path, data)
    
except Exception as e:
    alert("Excel export failed: {}".format(str(e)), "Export Error")
    Debug.WriteLine("Excel error: {}".format(str(e)))
    return None
```

## Data Structure Patterns
Use OrderedDictionary for worksheet data:
```python
# Multiple worksheets
data = OrderedDictionary()
data["Sheet1"] = create_sheet_data()
data["Sheet2"] = create_another_sheet()

# Single worksheet with list of dictionaries
sheet_data = [
    OrderedDictionary([("Column1", value1), ("Column2", value2)]),
    OrderedDictionary([("Column1", value3), ("Column2", value4)])
]
```

## Error Handling and Validation
- Always validate data before Excel operations
- Use try-catch blocks around all MiniExcel calls
- Provide user feedback for success/failure
- Clean up temporary files on errors
