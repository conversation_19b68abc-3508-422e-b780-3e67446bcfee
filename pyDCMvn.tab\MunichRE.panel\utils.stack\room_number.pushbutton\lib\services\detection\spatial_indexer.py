# coding: utf-8

from DCMvn.core import DB
from DCMvn.core.framework import List
from ...models import BaseSpatial, BaseMep
from ...utils import convert_mm_to_internal

class SpatialIndexer(object):
    """Indexes elements spatially to reduce the number of detailed intersection checks."""

    def __init__(self, document, advanced_options=None):
        # type: (DB.Document, object) -> None
        self.document = document
        self.advanced_options = advanced_options
        
        self._proximity_distance = 0.0
        self._z_offset = 0.0
        self._map_above = False
        self._use_proximity = False
        
        if advanced_options:
            self._proximity_distance = convert_mm_to_internal(advanced_options.nearest_allowed_distance)
            self._z_offset = -convert_mm_to_internal(advanced_options.above_allowed_distance)
            self._map_above = advanced_options.map_elements_above_spaces
            self._use_proximity = advanced_options.use_proximity_mapping

    def filter_mep_elements_by_spatial_bbox(self, spatial_element, mep_classifications):
        # type: (BaseSpatial, list[tuple[BaseMep, dict]]) -> list[tuple[BaseMep, dict]]
        """Filter MEP classifications that intersect with the spatial element's expanded bounding box.
        
        Returns candidates for detailed checking.
        """
        try:
            spatial_outline = self._get_spatial_element_expanded_outline(spatial_element)
            if not spatial_outline:
                # Fallback: return all as candidates if no outline available
                return self._get_all_as_candidates(mep_classifications)
            
            bbox_filter = DB.BoundingBoxIntersectsFilter(spatial_outline)
            
            # Get MEP element IDs for filtering
            mep_element_ids = List[DB.ElementId]([mep.element.Id for mep, _ in mep_classifications])
            
            intersecting_element_ids = set()
            try:
                intersecting_elements = (
                    DB.FilteredElementCollector(self.document, mep_element_ids)
                    .WhereElementIsNotElementType()
                    .WherePasses(bbox_filter)
                    .ToElements()
                )
                for elem in intersecting_elements:
                    intersecting_element_ids.add(elem.Id)
            except:  # noqa
                return self._get_all_as_candidates(mep_classifications)
            
            # Filter classifications to only those that passed bounding box test
            candidate_mep_elements = [
                (mep, info) for mep, info in mep_classifications
                if mep.element.Id in intersecting_element_ids
            ]
            
            return candidate_mep_elements
            
        except:  # noqa
            return self._get_all_as_candidates(mep_classifications)
    
    def _get_all_as_candidates(self, mep_classifications):
        # type: (list[tuple[BaseMep, dict]]) -> list[tuple[BaseMep, dict]]
        """Return all MEP classifications as candidates (fallback)."""
        return list(mep_classifications)
    
    def _get_spatial_element_expanded_outline(self, spatial_element):
        # type: (BaseSpatial) -> DB.Outline | None
        """Get expanded outline for spatial element to filter MEP elements.
        
        Creates an outline from the spatial element's bounding box, optionally
        expanded by proximity and vertical offset distances for advanced detection.
        Uses proper bounding box expansion that respects rotated coordinate systems.
        """
        try:    
            # Get base bounding box from spatial element (in its document coordinate space)
            bbox = spatial_element.bounding_box
            outline = DB.Outline(bbox.Min, bbox.Max)

            # Extend X-Y within proximity distance
            if self._use_proximity:
                new_min_x = bbox.Min.X - self._proximity_distance
                new_min_y = bbox.Min.Y - self._proximity_distance
                new_max_x = bbox.Max.X + self._proximity_distance
                new_max_y = bbox.Max.Y + self._proximity_distance
                outline = DB.Outline(DB.XYZ(new_min_x, new_min_y, outline.MinimumPoint.Z), DB.XYZ(new_max_x, new_max_y, outline.MaximumPoint.Z))

            # Extend top Z within above allowed distance
            if self._map_above:
                new_max_z = outline.MaximumPoint.Z - self._z_offset
                outline = DB.Outline(outline.MinimumPoint, DB.XYZ(outline.MaximumPoint.X, outline.MaximumPoint.Y, new_max_z))
            
            return outline
            
        except:  # noqa
            return None