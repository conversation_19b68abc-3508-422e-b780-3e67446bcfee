# category_mapping.py
from Autodesk.Revit.DB import BuiltInCategory

category_mapping = {
    "Air Terminals": BuiltInCategory.OST_DuctTerminal,
    "Analytical Nodes": BuiltInCategory.OST_AnalyticalNodes,
    "Area Tags": BuiltInCategory.OST_AreaTags,
    "Cable Tray Fittings": BuiltInCategory.OST_CableTrayFitting,
    "Cable Trays": BuiltInCategory.OST_CableTray,
    "Ceilings": BuiltInCategory.OST_Ceilings,
    "Columns": BuiltInCategory.OST_Columns,
    "Curtain Panels": BuiltInCategory.OST_CurtainWallPanels,
    "Curtain Wall Mullions": BuiltInCategory.OST_CurtainWallMullions,
    "Doors": BuiltInCategory.OST_Doors,
    "Duct Accessories": BuiltInCategory.OST_DuctAccessory,
    "Duct Fittings": BuiltInCategory.OST_DuctFitting,
    "Duct Insulations": BuiltInCategory.OST_DuctInsulations,
    "Duct Linings": BuiltInCategory.OST_DuctLinings,
    "Duct Systems": BuiltInCategory.OST_DuctSystem,
    "Ducts": BuiltInCategory.OST_DuctCurves,
    "Electrical Equipment": BuiltInCategory.OST_ElectricalEquipment,
    "Electrical Fixtures": BuiltInCategory.OST_ElectricalFixtures,
    "Communication Devices": BuiltInCategory.OST_CommunicationDevices,
    "Electrical Fixtures": BuiltInCategory.OST_ElectricalFixtures,
    "Fire Alarm Devices": BuiltInCategory.OST_FireAlarmDevices,
    "Floors": BuiltInCategory.OST_Floors,
    "Furniture": BuiltInCategory.OST_Furniture,
    "Furniture Systems": BuiltInCategory.OST_FurnitureSystems,
    "Generic Models": BuiltInCategory.OST_GenericModel,
    "Grids": BuiltInCategory.OST_Grids,
    "Lighting Fixtures": BuiltInCategory.OST_LightingFixtures,
    "Lighting Devices": BuiltInCategory.OST_LightingDevices,
    "Mass": BuiltInCategory.OST_Mass,
    "Mechanical Equipment": BuiltInCategory.OST_MechanicalEquipment,
    "Parking": BuiltInCategory.OST_Parking,
    "Piping Systems": BuiltInCategory.OST_PipingSystem,
    "Pipe Fittings": BuiltInCategory.OST_PipeFitting,
    "Pipe Accessories": BuiltInCategory.OST_PipeAccessory,
    "Plumbing Fixtures": BuiltInCategory.OST_PlumbingFixtures,
    "Railings": BuiltInCategory.OST_Railings,
    "Roofs": BuiltInCategory.OST_Roofs,
    "Rooms": BuiltInCategory.OST_Rooms,
    "Security Devices": BuiltInCategory.OST_SecurityDevices,
    "Spaces": BuiltInCategory.OST_MEPSpaces,
    "Sprinklers": BuiltInCategory.OST_Sprinklers,
    "Stairs": BuiltInCategory.OST_Stairs,
    "Structural Columns": BuiltInCategory.OST_StructuralColumns,
    "Structural Foundations": BuiltInCategory.OST_StructuralFoundation,
    "Structural Framing": BuiltInCategory.OST_StructuralFraming,
    "Structural Rebar": BuiltInCategory.OST_Rebar,
    "Walls": BuiltInCategory.OST_Walls,
    "Windows": BuiltInCategory.OST_Windows,
    "Project Information": BuiltInCategory.OST_ProjectInformation,
    "Materials": BuiltInCategory.OST_Materials,
    "HVAC Zones": BuiltInCategory.OST_HVAC_Zones,
    "Sheets": BuiltInCategory.OST_Sheets,
    "PipeSegments": BuiltInCategory.OST_PipeSegments,
    # Add more mappings as needed
}
