# coding: utf-8
from DCMvn.core import DB


class DocumentWrapper(object):
    """
    Wrapper for Revit documents that includes transformation information.
    
    For link documents, stores both the document and the RevitLinkInstance 
    with its transform to enable proper coordinate transformation.
    For the host document, stores just the document with identity transform.
    """
    
    def __init__(self, document, link_instance=None):
        # type: (DB.Document, DB.RevitLinkInstance) -> None
        """
        Initialize DocumentWrapper.
        
        Args:
            document: The Revit document (host or linked)
            link_instance: RevitLinkInstance for linked documents, None for host document
        """
        self.document = document
        self.link_instance = link_instance
        self._transform = None
        self._is_host = link_instance is None
        
    @property
    def transform(self):
        # type: () -> DB.Transform
        """Get the transform for this document relative to the host document"""
        if self._transform is None:
            if self._is_host:
                self._transform = DB.Transform.Identity
            elif self.link_instance:
                self._transform = self.link_instance.GetTotalTransform()
            else:
                self._transform = DB.Transform.Identity
        return self._transform
    
    @property
    def is_host(self):
        # type: () -> bool
        """Check if this is the host document"""
        return self._is_host
    
    @property
    def is_linked(self):
        # type: () -> bool
        """Check if this is a linked document"""
        return not self._is_host
    
    @property
    def title(self):
        # type: () -> str
        """Get the document title"""
        return self.document.Title if self.document else "Unknown"
    
    def transform_point(self, point):
        # type: (DB.XYZ) -> DB.XYZ
        """Transform a point from this document's coordinate system to the host document"""
        if self._is_host:
            return point
        return self.transform.OfPoint(point)
    
    def transform_bounding_box(self, bbox):
        # type: (DB.BoundingBoxXYZ) -> DB.BoundingBoxXYZ
        """Transform a bounding box from this document's coordinate system to the host document"""
        if not bbox or self._is_host:
            return bbox
            
        # Transform the min and max points
        transformed_min = self.transform_point(bbox.Min)
        transformed_max = self.transform_point(bbox.Max)
        
        # After transformation, ensure min/max are correct (handles rotation/scaling)
        actual_min_x = min(transformed_min.X, transformed_max.X)
        actual_max_x = max(transformed_min.X, transformed_max.X)
        actual_min_y = min(transformed_min.Y, transformed_max.Y)
        actual_max_y = max(transformed_min.Y, transformed_max.Y)
        actual_min_z = min(transformed_min.Z, transformed_max.Z)
        actual_max_z = max(transformed_min.Z, transformed_max.Z)
        
        # Create new bounding box with corrected coordinates
        result_bbox = DB.BoundingBoxXYZ()
        result_bbox.Min = DB.XYZ(actual_min_x, actual_min_y, actual_min_z)
        result_bbox.Max = DB.XYZ(actual_max_x, actual_max_y, actual_max_z)
        return result_bbox
    
    def __str__(self):
        # type: () -> str
        """String representation of the document wrapper"""
        doc_type = "Host" if self._is_host else "Link"
        return "{} Document: {}".format(doc_type, self.title)
    
    def __repr__(self):
        # type: () -> str
        return "DocumentWrapper(document='{}', is_host={})".format(self.title, self._is_host) 