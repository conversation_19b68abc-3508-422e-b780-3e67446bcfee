# coding: utf-8
from DCMvn.core import DB, HOST_APP
from DCMvn.core.framework import Debug, List
from DCMvn.revit.geometry import GetElementMergedSolid, GetElementMeshes
from pyrevit.compat import get_elementid_value_func
from ..base_geometry import BaseGeometry

door_window_filter = DB.ElementMulticategoryFilter(List[DB.BuiltInCategory]([DB.BuiltInCategory.OST_Doors, DB.BuiltInCategory.OST_Windows]))  # type: DB.ElementFilter

class BaseMep(BaseGeometry):
    """Base class for all MEP elements (Ducts, Pipes, Family Instances).

    This class provides common functionality for MEP elements in Revit
    for spatial mapping and clash checking operations.
    """

    def __init__(self, mep_element):
        # type: (DB.Element) -> None
        """Initialize a MEP element wrapper.

        Args:
            mep_element (DB.Element): The Revit MEP element to wrap
        """
        super(BaseMep, self).__init__(mep_element)
        self._location = None  # type: DB.Location | None
        self._room_calculation_points = None  # type: list[DB.XYZ] | DB.XYZ | None
        self._get_elementid_value = get_elementid_value_func()

    @property
    def element(self):
        # type: () -> DB.Element
        """Get the underlying Revit element.

        Returns:
            DB.Element: The wrapped Revit element
        """
        return self._element

    @property
    def id(self):
        # type: () -> DB.ElementId
        """Get the element ID.

        Returns:
            DB.ElementId: The element ID
        """
        return self._element.Id

    @property
    def guid(self):
        # type: () -> str
        """Get the IFC GUID of the MEP element.

        Returns:
            str: The IFC GUID or empty string if not available
        """
        param = self._element.get_Parameter(DB.BuiltInParameter.IFC_GUID)
        return param.AsString() if param and param.HasValue else ""

    @property
    def document(self):
        # type: () -> DB.Document
        """Get the document containing this element.

        Returns:
            DB.Document: The document containing this element
        """
        return self._element.Document

    @property
    def location(self):
        # type: () -> DB.Location
        """Get the location of the MEP element.

        Returns:
            DB.Location: The location or None if not available
        """
        if self._location is None:
            try:
                self._location = self._element.Location
            except Exception:
                pass
        return self._location

    @property
    def room_calculation_points(self):
        # type: () -> list[DB.XYZ] | DB.XYZ | None
        """Get room calculation points for the MEP element.

        Returns:
            list[DB.XYZ]: List of room calculation points, empty if none available
        """
        
        if self._room_calculation_points is None:
            self._room_calculation_points = []
            try:
                if isinstance(self._element, DB.FamilyInstance):
                    if door_window_filter.PassesFilter(self._element):
                        self._room_calculation_points = self._element.GetSpatialElementFromToCalculationPoints()  # type: list[DB.XYZ]
                    else:
                        self._room_calculation_points = self._element.GetSpatialElementCalculationPoint()  # type: DB.XYZ
            except Exception:
                pass
        return self._room_calculation_points

    def is_valid(self):
        # type: () -> bool
        """Check if the MEP element is valid.

        Returns:
            bool: True if the element is valid, False otherwise
        """
        try:
            return self._element.IsValidObject
        except Exception:
            return False

    def get_category(self):
        # type: () -> DB.Category
        """Get the category of the MEP element.

        Returns:
            DB.Category: The category or None if not available
        """
        try:
            return self._element.Category
        except Exception:
            return None

    def get_category_name(self):
        # type: () -> str
        """Get the category name of the MEP element.

        Returns:
            str: The category name or empty string if not available
        """
        try:
            category = self.get_category()
            return category.Name if category else ""
        except Exception:
            return ""

    def __str__(self):
        # type: () -> str
        """String representation of the MEP element.

        Returns:
            str: String representation
        """
        return "MEP (Id={}, GUID={}, Category={})".format(
            self._get_elementid_value(self.id), self.guid, self.get_category_name())

    def __repr__(self):
        # type: () -> str
        """Detailed string representation of the MEP element.

        Returns:
            str: Detailed string representation
        """
        return "MEP (Id={}, GUID={}, Category={})".format(
            self._get_elementid_value(self.id), self.guid, self.get_category_name())

    def __eq__(self, other):
        # type: (object) -> bool
        """Check equality based on element ID.

        Args:
            other (object): Other object to compare

        Returns:
            bool: True if elements have the same ID, False otherwise
        """
        if not isinstance(other, BaseMep):
            return False
        get_elementid_value = get_elementid_value_func()
        equal_id = get_elementid_value(self.id) == get_elementid_value(other.id)
        equal_doc = self.document.Equals(other.document)
        return equal_id and equal_doc

    def __hash__(self):
        # type: () -> int
        """Hash based on element ID.

        Returns:
            int: Hash value
        """
        return hash(self.element) 