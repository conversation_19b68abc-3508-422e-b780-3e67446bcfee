"""Constant values for Shared Parameter Creator."""
# Register
DEFAULT_REGISTERLIST_SHEETNAME = "Shared Parameter Name"
DEFAULT_REGISTERLIST_STARTCELL = "A3"
SHARED_PARAMETER_CREATOR_SECTION = "pyDCMvn.MunichRE.SharedParameterCreator"
REGISTER_LIST_PATH = "register_list_path"

# Register List Columns
SP_ITEM = "Item"
SP_PROPOSER = "Proposer"
SP_APPROVER = "Approver"
SP_GUID = "GUID"
SP_NAME = "Parameter Name"
SP_GROUP = "Parameter Group"
SP_DISCIPLINE = "Discipline"
SP_TYPE_OF_PARAMETER = "Type of Parameter"
SP_TOOLTIP = "Tooltip Description"
SP_GROUP_UNDER = "Group Parameter Under"
SP_TYPE = "Type"
SP_PROJECT_PREFIX = "Project"
SP_CATEGORIES = "Categories List"
SP_FAMILIES = "Families List"

ALL = "all"
NA = "n/a"

# Shared Parameter
TYPE_PARAMETER = "type"
INSTANCE_PARAMETER = "instance"
PARAM_TYPE = [TYPE_PARAMETER, INSTANCE_PARAMETER]
