# Excel Integration and Data Export

You are an expert in DCMvn Excel integration using MiniExcel. Generate comprehensive Excel export solutions following established patterns.

## Reference Documentation
Study these patterns before implementation:
- [Excel Patterns](#file:../.cursor/rules/excel/dcmvn-excel-patterns.mdc)
- [MiniExcel Integration](#file:../.cursor/rules/excel/dcmvn-miniexcel-integration.mdc)
- [Data Validation](#file:../.cursor/rules/excel/dcmvn-data-validation.mdc)

## Required Import Pattern
Always use this exact import structure:
```python
# coding: utf-8
import clr
import os

# DCMvn core imports
from DCMvn.io import save_excel_file
from DCMvn.coreutils.assemblyhandler import load_miniexcel
from DCMvn.core.framework import IDictionary, Debug
from DCMvn.forms import alert

# Load MiniExcel assembly at module level
load_miniexcel()
from MiniExcelLibs import MiniExcel
from MiniExcelLibs.OpenXml import OpenXmlConfiguration

# .NET collections support
clr.AddReference("System.Collections.Specialized")
from System.Collections.Specialized import OrderedDictionary
from System.Collections.Generic import Dictionary
```

## Safe Excel Operations Pattern

### 1. File Path Management
```python
def get_export_file_path(default_name="Report"):
    """Get file path using DCMvn file dialog."""
    file_path = save_excel_file(title="Save {} Report".format(default_name))
    if not file_path:
        return None  # User cancelled
    return file_path
```

### 2. File Cleanup and Safety
```python
def safe_file_cleanup(file_path):
    """Safely clean existing file before saving."""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
        return True
    except Exception as e:
        Debug.WriteLine("File cleanup error: {}".format(e))
        return False
```

### 3. Data Structure Requirements
- Use `OrderedDictionary` for worksheet data
- Support multiple worksheets
- Implement proper column ordering
- Handle data type conversion
- Validate data before export

### 4. Error Handling Pattern
```python
def export_to_excel(data, file_path):
    """Export data to Excel with comprehensive error handling."""
    try:
        if not safe_file_cleanup(file_path):
            return False
        
        # Create data structure
        excel_data = prepare_excel_data(data)
        
        # Save with MiniExcel
        MiniExcel.SaveAs(file_path, excel_data)
        
        alert("Export completed successfully!", "Export Success")
        return True
        
    except Exception as e:
        error_msg = "Excel export failed: {}".format(str(e))
        alert(error_msg, "Export Error")
        Debug.WriteLine(error_msg)
        return False
```

## Data Preparation Patterns

### Single Worksheet
```python
def create_single_worksheet_data(items):
    """Create data for single worksheet export."""
    data = []
    for item in items:
        row = OrderedDictionary()
        row["Column1"] = item.property1
        row["Column2"] = item.property2
        data.append(row)
    return data
```

### Multiple Worksheets
```python
def create_multi_worksheet_data(datasets):
    """Create data for multiple worksheet export."""
    excel_data = OrderedDictionary()
    
    for sheet_name, items in datasets.items():
        sheet_data = create_single_worksheet_data(items)
        excel_data[sheet_name] = sheet_data
    
    return excel_data
```

## Generation Requirements
Generate Excel export solutions that include:
1. File path selection using DCMvn dialogs
2. Safe file cleanup operations
3. Proper data structure creation
4. Comprehensive error handling
5. User feedback and progress indication
6. Data validation before export
7. Support for both single and multi-sheet exports
8. Performance optimization for large datasets

## Quality Checklist
- [ ] Uses DCMvn file dialogs
- [ ] Implements safe file cleanup
- [ ] Uses OrderedDictionary for data
- [ ] Has comprehensive error handling
- [ ] Provides user feedback
- [ ] Validates data before export
- [ ] Handles large datasets efficiently
- [ ] Follows MiniExcel best practices
