# coding: utf-8
import clr
import System
from DCMvn.core import DB
from DCMvn.forms.mvvm import ViewModelBase
from DCMvn.core.framework import ObservableCollection, List
from ..models import TargetCategory
from ..services.collector_service import CollectorService
from .advanced_options_viewmodel import AdvancedOptionsViewModel
from .collection_viewmodel import CollectionViewModel
from .mapping_viewmodel import MappingViewModel
from ..commands.run_mapping_command import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mand
from ..commands.show_advanced_options_command import ShowAdvancedOptionsCommand
from ..events import ActionEventHandler

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)  # noqa


class MainViewModel(ViewModelBase):
    def __init__(self, document):
        ViewModelBase.__init__(self)
        self.document = document
        self.__collector_service = CollectorService(document)

        self.__target_elements_by_category = ObservableCollection[TargetCategory]()
        self.__selected_target_elements = []
        self.__selected_source_document_index = 0
        self.__selected_source_category_index = 0
        self.__active_view_only_target = False
        self.__advanced_options = AdvancedOptionsViewModel()
        self.__collection_viewmodel = CollectionViewModel(self.__collector_service)
        self.__mapping_viewmodel = MappingViewModel(self.__collector_service)
        self.__is_advanced_options_flyout_open = False
        self.__owner_window = None  # Hold reference to the view window
        self.__selected_source_document = self.source_documents[
            self.__selected_source_document_index
        ]
        self.__selected_source_category = self.source_categories[
            self.__selected_source_category_index
        ]
        
        # Initialize commands directly
        self.__run_command = RunMappingCommand(self)
        self.__show_advanced_options_command = ShowAdvancedOptionsCommand(self)
        
        # Initialize action event handler for transaction management
        self.__action_event_handler = ActionEventHandler()
        
        # Initialize collection with default selection
        self.__collection_viewmodel.update_selection(
            self.__selected_source_document_index,
            self.__selected_source_category_index
        )
        # Ensure source parameters reflect current selection (empty when no spatial elements)
        self.__mapping_viewmodel.update_source_parameters(
            self.__collection_viewmodel.current_elements
        )

    def _on_target_category_property_changed(self, sender, e):
        """Handle property changes from TargetCategory objects"""
        if e.PropertyName == "is_selected":
            self._update_target_parameters()
            self.RaisePropertyChanged("target_element_count")
            self.__run_command.RaiseCanExecuteChanged()

    def _load_target_elements(self):
        """Load and group target elements with current filter settings"""
        groups = self.__collector_service.get_target_categories(
            active_view_only=self.__active_view_only_target
        )

        for group in groups:
            group.PropertyChanged += self._on_target_category_property_changed
            self.__target_elements_by_category.Add(group)

    def _update_target_parameters(self):
        """Update target parameters based on selected target categories"""
        selected_target_elements = List[DB.Element]()
        for tc in self.target_elements_by_category:
            if tc.is_selected:
                for element in tc.elements:
                    selected_target_elements.Add(element)
        self.__mapping_viewmodel.update_target_parameters(selected_target_elements)

    def _refresh_target_elements(self):
        """Clear and refresh target elements when filter settings change"""
        for item in self.__target_elements_by_category:
            item.PropertyChanged -= self._on_target_category_property_changed
        
        self.__target_elements_by_category.Clear()
        self._load_target_elements()
        # Update target parameters for mapping
        self._update_target_parameters()
        self.RaisePropertyChanged("target_elements_by_category")
        self.RaisePropertyChanged("target_element_count")
        self.__run_command.RaiseCanExecuteChanged()

    @property
    def source_documents(self):
        return self.__collector_service.get_document_wrappers()

    @property
    def source_categories(self):
        return self.__collector_service.get_spatial_categories()

    @property
    def target_elements_by_category(self):
        """Get TargetCategory objects grouped by category"""
        if not self.__target_elements_by_category:
            self._load_target_elements()
        return self.__target_elements_by_category

    @target_elements_by_category.setter
    def target_elements_by_category(self, value):
        if self.__target_elements_by_category != value:
            for item in self.__target_elements_by_category:
                item.PropertyChanged -= self._on_target_category_property_changed

            self.__target_elements_by_category = value

            for item in self.__target_elements_by_category:
                item.PropertyChanged += self._on_target_category_property_changed

            self.RaisePropertyChanged("target_elements_by_category")

    @property
    def selected_source_document_index(self):
        return self.__selected_source_document_index

    @selected_source_document_index.setter
    def selected_source_document_index(self, value):
        if self.__selected_source_document_index != value:
            self.__selected_source_document_index = value

            self.RaisePropertyChanged("selected_source_document_index")
            self.__selected_source_document = self.source_documents[
                self.__selected_source_document_index
            ]
            # Update collection when source selection changes
            self.__collection_viewmodel.update_selection(
                self.__selected_source_document_index,
                self.__selected_source_category_index
            )
            # Update source parameters for mapping
            source_elements = self.__collection_viewmodel.current_elements
            self.__mapping_viewmodel.update_source_parameters(source_elements)
            self.RaisePropertyChanged("source_element_count")
            self.__run_command.RaiseCanExecuteChanged()

    @property
    def selected_source_category_index(self):
        return self.__selected_source_category_index

    @selected_source_category_index.setter
    def selected_source_category_index(self, value):
        if self.__selected_source_category_index != value:
            self.__selected_source_category_index = value

            self.RaisePropertyChanged("selected_source_category_index")
            self.__selected_source_category = self.source_categories[
                self.__selected_source_category_index
            ]
            # Update collection when source selection changes
            self.__collection_viewmodel.update_selection(
                self.__selected_source_document_index,
                self.__selected_source_category_index
            )
            # Update source parameters for mapping
            source_elements = self.__collection_viewmodel.current_elements
            self.__mapping_viewmodel.update_source_parameters(source_elements)
            self.RaisePropertyChanged("source_element_count")
            self.__run_command.RaiseCanExecuteChanged()

    @property
    def selected_target_elements(self):
        return self.target_elements_by_category.Where(lambda x: x.is_selected).ToList()

    @property
    def active_view_only_target(self):
        return self.__active_view_only_target

    @active_view_only_target.setter
    def active_view_only_target(self, value):
        if self.__active_view_only_target != value:
            self.__active_view_only_target = value

            self.RaisePropertyChanged("active_view_only_target")
            self._refresh_target_elements()

    @property
    def advanced_options(self):
        return self.__advanced_options

    @property
    def owner_window(self):
        """Get the owner window (main_view)"""
        return self.__owner_window
    
    @owner_window.setter
    def owner_window(self, value):
        """Set the owner window (main_view)"""
        self.__owner_window = value

    @property
    def selected_source_document(self):
        """Get the currently selected source document"""
        return self.__selected_source_document
    
    @property
    def selected_source_category(self):
        """Get the currently selected source category"""
        return self.__selected_source_category

    @property
    def collection_viewmodel(self):
        return self.__collection_viewmodel

    @property
    def mapping_viewmodel(self):
        return self.__mapping_viewmodel

    @property
    def source_element_count(self):
        return self.__collection_viewmodel.current_element_count

    @property
    def target_element_count(self):
        total_count = 0
        for tc in self.target_elements_by_category:
            if tc.is_selected:
                total_count += tc.element_count
        return total_count

    @property
    def run_command(self):
        return self.__run_command
    
    @property
    def ShowAdvancedOptionsCommand(self):
        return self.__show_advanced_options_command
    
    @property
    def is_advanced_options_flyout_open(self):
        """Get the current state of the advanced options flyout"""
        return self.__is_advanced_options_flyout_open
    
    @is_advanced_options_flyout_open.setter
    def is_advanced_options_flyout_open(self, value):
        """Set the state of the advanced options flyout"""
        if self.__is_advanced_options_flyout_open != value:
            self.__is_advanced_options_flyout_open = value
            self.RaisePropertyChanged("is_advanced_options_flyout_open")
    
    @property
    def action_event_handler(self):
        """Get the action event handler for executing operations in UI thread"""
        return self.__action_event_handler
    

    def cleanup(self):
        """Unsubscribe from property change events and dispose resources"""
        for item in self.__target_elements_by_category:
            item.PropertyChanged -= self._on_target_category_property_changed
        self.__collection_viewmodel.clear_cache()
        
        # Save advanced options config before cleanup
        if self.__advanced_options:
            self.__advanced_options.save_config()
        
        # Dispose action event handler
        if self.__action_event_handler:
            self.__action_event_handler.Dispose()
            self.__action_event_handler = None
