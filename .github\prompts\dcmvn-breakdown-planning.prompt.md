---
description: 'DCMvn framework-specific breakdown planning prompt for managing pyRevit extension development projects using Epic>Feature>Story hierarchy with Revit API integration focus, external events threading, Excel workflows, and MVVM architecture patterns.'
---

# DCMvn Project Breakdown Planning

## Configuration Variables
${PROJECT_SCOPE="Extension|Scripts|Components|Integration|Full"} <!-- Scope of DCMvn development -->
${REVIT_VERSION="2024|2023|2022|Multiple"} <!-- Target Revit API versions -->
${DCMVN_FOCUS="MVVM|Services|Excel|Geometry|Parameters|QA|All"} <!-- DCMvn framework focus areas -->
${TIMELINE="Sprint|Monthly|Quarterly|Annual"} <!-- Development timeline approach -->
${TEAM_SIZE="Solo|Small(2-4)|Medium(5-8)|Large(9+)"} <!-- Development team size -->
${COMPLEXITY="Basic|Intermediate|Advanced|Enterprise"} <!-- Project complexity level -->

## Generated Prompt

"Create a comprehensive project breakdown structure for DCMvn framework development that organizes work into Epic > Feature > Story hierarchy specifically tailored for pyRevit extension development with Revit API integration. Follow this DCMvn-specific approach:

## DCMvn Epic Categories

### 1. Framework Core Development
**Epic Type**: DCMvn Core Framework Enhancement
**Duration**: ${TIMELINE == "Sprint" ? "2-4 sprints" : TIMELINE == "Monthly" ? "1-3 months" : TIMELINE == "Quarterly" ? "1 quarter" : "2-4 quarters"}
**Priority**: Critical for DCMvn foundation

**Epic Description**: 
Enhance and extend the DCMvn core framework components including ViewModelBase, RelayCommand, service layer architecture, and external event patterns for thread-safe Revit API operations.

**Features**:
- **F1.1**: ViewModelBase Enhancement
  - **Story 1.1.1**: Implement advanced property change notification patterns
  - **Story 1.1.2**: Add validation framework integration
  - **Story 1.1.3**: Enhance error handling and logging capabilities
  
- **F1.2**: External Event Framework
  - **Story 1.2.1**: Standardize external event patterns for thread safety
  - **Story 1.2.2**: Implement queue management for complex operations
  - **Story 1.2.3**: Add progress reporting for long-running operations
  
- **F1.3**: Service Layer Architecture
  - **Story 1.3.1**: Enhance CollectorService caching mechanisms
  - **Story 1.3.2**: Extend DetectionService geometric capabilities
  - **Story 1.3.3**: Improve ReportService Excel integration patterns

${DCMVN_FOCUS.includes("MVVM") || DCMVN_FOCUS == "All" ? `
### 2. MVVM Pattern Implementation
**Epic Type**: DCMvn MVVM Architecture
**Duration**: ${TIMELINE == "Sprint" ? "3-6 sprints" : TIMELINE == "Monthly" ? "2-4 months" : TIMELINE == "Quarterly" ? "1-2 quarters" : "1-2 quarters"}
**Priority**: High for UI consistency

**Epic Description**:
Implement comprehensive MVVM patterns throughout DCMvn framework with standardized ViewModels, Commands, and data binding for consistent user experience across all tools.

**Features**:
- **F2.1**: Standard ViewModel Patterns
  - **Story 2.1.1**: Create base ViewModel templates for common scenarios
  - **Story 2.1.2**: Implement command patterns for Revit API operations
  - **Story 2.1.3**: Add data validation and error handling to ViewModels
  
- **F2.2**: UI Data Binding
  - **Story 2.2.1**: Standardize ObservableCollection usage patterns
  - **Story 2.2.2**: Implement property change notification best practices
  - **Story 2.2.3**: Add two-way binding support for complex scenarios
  
- **F2.3**: Command Implementation
  - **Story 2.3.1**: Enhance RelayCommand for async operations
  - **Story 2.3.2**: Add parameter passing patterns
  - **Story 2.3.3**: Implement command validation and enablement logic` : ""}

${DCMVN_FOCUS.includes("Excel") || DCMVN_FOCUS == "All" ? `
### 3. Excel Integration Workflows
**Epic Type**: DCMvn Excel Integration
**Duration**: ${TIMELINE == "Sprint" ? "2-4 sprints" : TIMELINE == "Monthly" ? "1-3 months" : TIMELINE == "Quarterly" ? "1 quarter" : "1-2 quarters"}
**Priority**: High for data workflows

**Epic Description**:
Develop comprehensive Excel integration capabilities using MiniExcel for parameter mapping, element placement, QA reporting, and data exchange workflows.

**Features**:
- **F3.1**: Parameter Mapping Framework
  - **Story 3.1.1**: Implement Excel-to-Revit parameter mapping engine
  - **Story 3.1.2**: Add validation for parameter data types and values
  - **Story 3.1.3**: Create error reporting for mapping failures
  
- **F3.2**: Element Placement from Excel
  - **Story 3.2.1**: Develop IFC element placement from Excel coordinates
  - **Story 3.2.2**: Implement rotation and transformation handling
  - **Story 3.2.3**: Add placement validation and conflict detection
  
- **F3.3**: QA Reporting to Excel
  - **Story 3.3.1**: Generate comprehensive QA reports in Excel format
  - **Story 3.3.2**: Implement data visualization and charts
  - **Story 3.3.3**: Add customizable report templates` : ""}

${DCMVN_FOCUS.includes("Geometry") || DCMVN_FOCUS == "All" ? `
### 4. Geometry Control Operations
**Epic Type**: DCMvn Geometry Management
**Duration**: ${TIMELINE == "Sprint" ? "3-5 sprints" : TIMELINE == "Monthly" ? "2-4 months" : TIMELINE == "Quarterly" ? "1-2 quarters" : "1-3 quarters"}
**Priority**: Medium to High for spatial operations

**Epic Description**:
Implement advanced geometry control capabilities including alignment, placement, curve generation, and spatial relationship detection for complex Revit modeling workflows.

**Features**:
- **F4.1**: Geometry Alignment Tools
  - **Story 4.1.1**: Implement element-to-element alignment algorithms
  - **Story 4.1.2**: Add reference plane and grid alignment capabilities
  - **Story 4.1.3**: Create batch alignment operations for multiple elements
  
- **F4.2**: Curve and Line Placement
  - **Story 4.2.1**: Develop MEP curve placement from coordinates
  - **Story 4.2.2**: Implement curve fitting and optimization algorithms
  - **Story 4.2.3**: Add connection validation for MEP systems
  
- **F4.3**: Spatial Detection Services
  - **Story 4.3.1**: Enhance DetectionService for complex spatial relationships
  - **Story 4.3.2**: Implement distance and clearance calculations
  - **Story 4.3.3**: Add collision detection for element placement` : ""}

${DCMVN_FOCUS.includes("QA") || DCMVN_FOCUS == "All" ? `
### 5. Quality Assurance Automation
**Epic Type**: DCMvn QA Framework
**Duration**: ${TIMELINE == "Sprint" ? "2-4 sprints" : TIMELINE == "Monthly" ? "2-3 months" : TIMELINE == "Quarterly" ? "1 quarter" : "1-2 quarters"}
**Priority**: High for data integrity

**Epic Description**:
Develop automated quality assurance tools for data validation, geometric compliance, parameter consistency, and project standards enforcement.

**Features**:
- **F5.1**: Data Validation Framework
  - **Story 5.1.1**: Implement parameter value validation rules
  - **Story 5.1.2**: Add geometric constraint checking
  - **Story 5.1.3**: Create custom validation rule engine
  
- **F5.2**: Compliance Checking
  - **Story 5.2.1**: Develop standards compliance verification
  - **Story 5.2.2**: Implement naming convention validation
  - **Story 5.2.3**: Add level and elevation checking tools
  
- **F5.3**: Automated Reporting
  - **Story 5.3.1**: Generate comprehensive QA reports
  - **Story 5.3.2**: Implement issue tracking and resolution workflows
  - **Story 5.3.3**: Add trend analysis and metrics collection` : ""}

## DCMvn Story Template Structure

### Story Definition Template
**Story ID**: [Epic].[Feature].[Story] (e.g., E1.F1.S1)
**Title**: [Action] + [DCMvn Component] + [Expected Outcome]
**Priority**: Critical/High/Medium/Low
**Estimation**: [Story Points or Hours]
**DCMvn Component**: [Scripts/ViewModels/Services/Models/UI]

**User Story Format**:
As a [DCMvn developer/user/administrator]
I want to [specific functionality]
So that [business value for DCMvn workflows]

**Acceptance Criteria**:
- [ ] **Revit API Integration**: [Specific Revit API requirements]
- [ ] **Thread Safety**: External event implementation for UI operations
- [ ] **DCMvn Patterns**: Follows established DCMvn framework conventions
- [ ] **Error Handling**: Implements DCMvn error handling with user notifications
- [ ] **Documentation**: Includes code documentation and usage examples
- [ ] **Testing**: Unit tests for logic, integration tests for Revit API
- [ ] **Performance**: Meets DCMvn performance standards for operations

**Technical Requirements**:
- **IronPython 2.7 Compatibility**: [Specific syntax and library requirements]
- **Revit API Version**: ${REVIT_VERSION} compatibility
- **DCMvn Dependencies**: [Required DCMvn framework components]
- **External Libraries**: [MiniExcel, System assemblies, etc.]

**Definition of Done**:
- [ ] Code follows DCMvn coding standards and patterns
- [ ] External events implemented for thread safety
- [ ] Error handling includes user-friendly alerts
- [ ] Integration tests pass in Revit environment
- [ ] Documentation updated in .github/instructions/
- [ ] Code review completed by DCMvn framework team
- [ ] Performance benchmarks meet DCMvn standards

## DCMvn Development Workflow

### Sprint Planning (${TIMELINE} Approach)
1. **Epic Prioritization**: Based on DCMvn framework roadmap and business value
2. **Feature Breakdown**: Decompose features into implementable stories
3. **Story Estimation**: Use DCMvn complexity factors (Revit API, threading, Excel)
4. **Capacity Planning**: Account for Revit API learning curve and testing overhead
5. **Risk Assessment**: Identify DCMvn framework dependencies and integration points

### Development Standards
- **Code Reviews**: Mandatory for all DCMvn framework modifications
- **Testing Strategy**: Unit tests for logic, integration tests for Revit API
- **Documentation**: Update .github/instructions/ for new patterns
- **Performance**: Benchmark against DCMvn performance standards
- **Thread Safety**: Verify external event implementation for UI operations

### DCMvn Integration Points
- **Framework Dependencies**: Identify required DCMvn core components
- **Revit API Constraints**: Document version-specific limitations
- **Excel Workflows**: Validate MiniExcel integration patterns
- **UI Consistency**: Ensure MVVM compliance across components
- **Error Handling**: Implement standardized DCMvn error patterns

## Resource Planning for DCMvn Development

### Team Structure (${TEAM_SIZE} Team)
${TEAM_SIZE == "Solo" ? 
"- **Solo Developer**: Full-stack DCMvn development with focus on prioritized epics" : 
TEAM_SIZE == "Small(2-4)" ? 
"- **Lead Developer**: DCMvn framework architecture and complex Revit API work\n- **Developer**: Feature implementation and testing\n- **QA/Documentation**: Testing and documentation maintenance" :
TEAM_SIZE == "Medium(5-8)" ? 
"- **DCMvn Architect**: Framework design and technical leadership\n- **Senior Developers (2)**: Complex features and Revit API integration\n- **Developers (2-3)**: Feature implementation and bug fixes\n- **QA Engineer**: Testing and quality assurance\n- **Technical Writer**: Documentation and standards" :
"- **DCMvn Architecture Team**: Framework design and governance\n- **Senior Developers (3-4)**: Epic leadership and complex implementations\n- **Developers (4-5)**: Feature development and maintenance\n- **QA Team (2)**: Comprehensive testing and automation\n- **DevOps/Documentation**: CI/CD and documentation management"}

### Skill Requirements
- **DCMvn Framework**: Deep understanding of architecture and patterns
- **IronPython 2.7**: Proficiency with syntax limitations and .NET integration
- **Revit API**: Experience with elements, geometry, parameters, and transactions
- **MVVM Patterns**: Understanding of ViewModels, Commands, and data binding
- **Excel Integration**: Experience with MiniExcel and data transformation
- **Thread Safety**: Knowledge of external events and UI threading

### Risk Mitigation
- **DCMvn Dependencies**: Maintain backward compatibility with existing tools
- **Revit API Changes**: Plan for version-specific implementations
- **IronPython Limitations**: Design patterns that work within Python 2.7 constraints
- **Performance Issues**: Implement caching and optimization strategies
- **User Adoption**: Ensure consistent UI patterns and error handling

This DCMvn project breakdown structure provides a comprehensive framework for managing pyRevit extension development with proper epic-feature-story hierarchy, DCMvn-specific considerations, and integration with existing framework patterns."

## Expected Output

A detailed project breakdown that organizes DCMvn development work into manageable epics, features, and stories with clear acceptance criteria, technical requirements, and integration points specific to the DCMvn framework and pyRevit extension development.
