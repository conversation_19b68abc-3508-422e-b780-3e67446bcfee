# coding: utf-8
from DCMvn.core.framework import Debug
from DCMvn.forms.mvvm import RelayCommand


class ShowAdvancedOptionsCommand(RelayCommand):
    """Command to show/toggle the advanced options flyout"""
    
    def __init__(self, main_viewmodel):
        self.__main_viewmodel = main_viewmodel
        RelayCommand.__init__(self, self.execute, self.can_execute)
    
    def can_execute(self, parameter):
        """Can always execute this command"""
        return True
    
    def execute(self, parameter):
        """Toggle the advanced options flyout visibility"""
        try:
            vm = self.__main_viewmodel
            current_state = vm.is_advanced_options_flyout_open
            new_state = not current_state
            vm.is_advanced_options_flyout_open = new_state
        except Exception as ex:
            Debug.WriteLine("Error toggling advanced options: {}".format(str(ex)))