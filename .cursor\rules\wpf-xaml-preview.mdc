---
description: Minimal WPF preview project setup (C#) to open and preview external XAML used by IronPython/DCMvn tools
globs: *.csproj,*.xaml
---

### Goal
- Create a small WPF project to preview XAML outside Revit/IronPython runtime.
- No IronPython execution; only open XAML in Designer and at runtime via C# host.

### Project file
```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="MahApps.Metro" Version="2.4.10" />
  </ItemGroup>
</Project>
```

### App.xaml
```xml
<Application
  x:Class="PreviewHost.App"
  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <Application.Resources>
    <ResourceDictionary>
      <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Fonts.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Themes/Light.Blue.xaml" />
      </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
  </Application.Resources>
</Application>
```

### Link external XAML for Designer
```xml
<ItemGroup>
  <Page Include="..\\path\\to\\your\\ironpython\\view\\example_view.xaml">
    <Link>example_view.xaml</Link>
  </Page>
  <None Include="..\\path\\to\\your\\ironpython\\view\\example_view.xaml" />
</ItemGroup>
```

### Preview window (optional runtime host)
```xml
<mah:MetroWindow
  x:Class="PreviewHost.MainWindow"
  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
  xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
  Title="Preview Host" Width="520" Height="480">
  <Frame Source="example_view.xaml" NavigationUIVisibility="Hidden" />
</mah:MetroWindow>
```

### Notes
- This host is only for preview. In production, IronPython loads the same XAML via `WPFWindow` and sets the DataContext in Python.
- Keep linked paths relative for portability.