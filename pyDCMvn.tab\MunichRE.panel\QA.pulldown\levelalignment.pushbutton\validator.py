# coding: utf-8
import clr
from DCMvn.core import DB
from DCMvn.core.framework import List, System  # noqa

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq) # noqa

UNDEFINED_VALUE = "Undefined"

def format_level_name(level_value):
    # type: (DB.Level | List[DB.Level] | None) -> str
    """
    Format level value for display in reports and Excel exports.

    Args:
        level_value: Can be a single Level object, List[Level], or None

    Returns:
        str: Formatted level name(s) as comma-separated string or "Undefined"
    """
    try:
        if level_value is None:
            return UNDEFINED_VALUE

        if isinstance(level_value, DB.Level):
            return level_value.Name
        elif hasattr(level_value, '__iter__'):  # Python list
            if len(level_value) == 0:
                return UNDEFINED_VALUE
            elif len(level_value) == 1:
                return level_value[0].Name
            else:
                level_names = [level.Name for level in level_value]
                return ", ".join(sorted(level_names))
        else:
            return UNDEFINED_VALUE

    except Exception:
        return UNDEFINED_VALUE


def format_level_report(level_name):
    # type: (str) -> str
    """
    Format level value specifically for HTML display with styling.

    Args:
        level_name: Can be a single Level object, List[Level], or None

    Returns:
        str: Formatted level name(s) with HTML styling
    """
    try:
        if level_name == UNDEFINED_VALUE:
            return '<strong style="color: red">{}</strong>'.format(level_name)
        else:
            return '<strong style="color: blue">{}</strong>'.format(level_name)
    except: # noqa
        return '<strong style="color: red">{}</strong>'.format(UNDEFINED_VALUE)


def compare_levels(origin_level, right_level):
    # type: (DB.Level | None, DB.Level | List[DB.Level] | None) -> bool
    """
    Compare origin level with right level to determine if they match.

    Args:
        origin_level: The original level of the element (single Level or None)
        right_level: The calculated correct level (single Level, List[Level], or None)

    Returns:
        bool: True if levels match, False otherwise
    """
    try:
        if origin_level is None or right_level is None:
            return False

        if isinstance(right_level, DB.Level):
            return origin_level.Name == right_level.Name

        if hasattr(right_level, '__iter__'):
            if len(right_level) == 1:
                return origin_level.Name == right_level[0].Name
            else:
                return any(origin_level.Name == level.Name for level in right_level)

        return False
    except:
        return False