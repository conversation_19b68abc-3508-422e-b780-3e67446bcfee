from DCMvn.core import DB, REVIT_VERSION
from DCMvn.core.framework import List
from .. register_parameter import RegisterParameter
from .. regex_utils import extract_categories, extract_families
from .. import constant

class ProjectParamValidator:
    """Validator for register parameter"""
    def __init__(self, register_param, allow_categories, loadable_families, parameter_groups):
        # type: (RegisterParameter, List[DB.Category], List[DB.Family], list) -> ProjectParamValidator
        """Create RegisterParamValidator object

        Args:
            register_param (RegisterParameter): register parameter object
            allow_categories (List[DB.Category]): categories that be allowed to apply shared parameter
            loadable_families (List[DB.Family]): families that be allowed to apply shared parameter
            parameter_groups (list): Revit <= 2023 (DB.BuiltInParameterGroup) or Revit > 2023 (DB.ForgeTypeId)
        """
        self.__register_param = register_param  # type: RegisterParameter
        self.__allow_categories = allow_categories
        self.__loadable_families = loadable_families
        self.__groups = parameter_groups

        self.__allow_category_names = [cat.Name for cat in self.__allow_categories]
        self.__loadable_family_names = [family.FamilyName for family in self.__loadable_families]

    @property
    def register_param(self):
        # type: () -> RegisterParameter
        """Get register parameter object

        Returns:
            RegisterParameter: register parameter object
        """
        return self.__register_param

    @property
    def categories(self):
        # type: () -> tuple[List[DB.Category], List[str]] # noqa
        """Get categories that be applied this shared parameter

        Returns:
            List[DB.Category]: categories that be applied this shared parameter
        """
        cate_strings = extract_categories(self.__register_param.categories)

        if not cate_strings:
            return [], []

        is_not_available = cate_strings[0].lower() == constant.NA
        is_get_all = len(cate_strings) == 1 and cate_strings[0].lower() == constant.ALL
        is_exclude_enable = len(cate_strings) > 1 and constant.ALL in map(str.lower, cate_strings)

        # First list: categories that be applied this shared parameter
        # Second list: categories that cannot parse
        if is_not_available:
            return [], []
        elif is_get_all:
            return self.__allow_categories, []
        elif is_exclude_enable:
            cat_exclude = [cat for cat in cate_strings if cat.lower() != constant.ALL]
            cat_exclude_valid = [c for c in cat_exclude if c in self.__allow_category_names]
            cat_str_valid = [c for c in self.__allow_category_names if c not in cat_exclude_valid]
            cat_str_cannot_parse = [c for c in cate_strings if c not in self.__allow_category_names]
            return [cat for cat in self.__allow_categories if cat.Name in cat_str_valid], cat_str_cannot_parse
        else:
            cat_str_valid = [c for c in cate_strings if c in self.__allow_category_names]
            cat_str_cannot_parse = [c for c in cate_strings if c not in self.__allow_category_names]
            return [cat for cat in self.__allow_categories if cat.Name in cat_str_valid], cat_str_cannot_parse

    @property
    def families(self):
        # type: () -> tuple[List[DB.Family], List[str]] # noqa
        """Get families that be applied this shared parameter

        Returns:
            List[DB.Family]: families that be applied this shared parameter
        """
        if not self.__loadable_families:
            return [], []

        families_extract = extract_families(self.__register_param.families)
        if not families_extract:
            return [], []

        fam_str_valid = [fam for fam in families_extract if fam in self.__loadable_family_names]
        fam_str_not_valid = [fam for fam in families_extract if fam not in self.__loadable_family_names]

        return [f for f in self.__loadable_families if f.Name in fam_str_valid], fam_str_not_valid

    @property
    def group(self):  # group parameter under
        # type: () -> str
        """Get group parameter under

        Returns:
            DB.BuiltInParameterGroup: group parameter under
        """
        if not self.__register_param.group:
            return None
        if REVIT_VERSION <= 2023:
            groups_name = [DB.LabelUtils.GetLabelFor(x) for x in self.__groups]
        else:
            groups_name = [DB.LabelUtils.GetLabelForGroup(x) for x in self.__groups]
        if self.__register_param.group in groups_name:
            return self.__register_param.group

        return None

    @property
    def is_instance(self):
        # type: () -> bool
        """Check shared parameter is instance or type parameter

        Returns:
            bool: True if shared parameter is instance parameter, False if shared parameter is type parameter
        """
        if not self.__register_param.type or self.__register_param.type.lower() not in constant.PARAM_TYPE:
            return None

        return self.__register_param.type.lower() == constant.INSTANCE_PARAMETER
