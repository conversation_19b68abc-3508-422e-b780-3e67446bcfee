import clr
import os
import System

from DCMvn.core import get_output, HOST_APP
from ui.geometryalignment_view import GeometryAlignmentView, GeometryAlignmentViewModel

from pyrevit import script

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

logger = script.get_logger()
output = get_output()

if __name__ == "__main__":
    xaml_path = os.path.join(os.path.dirname(__file__), "ui/geometryalignment.xaml")
    view = GeometryAlignmentView(xaml_path, GeometryAlignmentViewModel(output, HOST_APP.doc))
    view.show(modal=False)
