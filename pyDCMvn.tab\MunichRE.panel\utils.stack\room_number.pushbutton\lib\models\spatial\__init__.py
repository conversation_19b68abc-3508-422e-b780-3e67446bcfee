# coding: utf-8
"""
Spatial Element Components Module

This module provides object-oriented wrappers for Revit spatial elements
including Rooms, Spaces, and IFC-imported generic spatial elements. It follows
SOLID design principles and provides a consistent interface for working with
spatial elements in the Revit API.

Classes:
    BaseSpatial: Base class for all spatial elements
    RoomSpatial: Wrapper for Revit Room elements
    GenericSpatial: Wrapper for IFC-imported generic spatial elements
"""

# Import base classes
from .base_spatial import BaseSpatial

# Import specific spatial element classes
from .revit_spatial import RevitSpatial
from .generic_spatial import GenericSpatial

# Define what gets imported with "from components import *"
__all__ = [
    'BaseSpatial',
    'RevitSpatial',
    'GenericSpatial',
]