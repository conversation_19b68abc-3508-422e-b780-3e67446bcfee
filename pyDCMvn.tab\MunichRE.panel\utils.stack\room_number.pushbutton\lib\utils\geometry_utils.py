# coding: utf-8
import math
from DCMvn.core import DB, HOST_APP
from DCMvn.core.framework import List, Debug, Trace
from ..models.base_geometry import BaseGeometry


def convert_mm_to_internal(value_mm):
    # type: (float) -> float
    """Convert millimeter value to Revit internal units (feet).
    
    Args:
        value_mm (float): Value in millimeters
        
    Returns:
        float: Value in Revit internal units (feet)
    """
    if HOST_APP.version >= 2021:
        return DB.UnitUtils.ConvertToInternalUnits(value_mm, DB.UnitTypeId.Millimeters)
    else:
        return DB.UnitUtils.ConvertToInternalUnits(value_mm, DB.DisplayUnitType.DUT_MILLIMETERS)


def create_line(start_point, end_point):
    # type: (DB.XYZ, DB.XYZ) -> DB.Line | None
    """Create a line between two points.
    
    Args:
        start_point (DB.XYZ): Start point of the line
        end_point (DB.XYZ): End point of the line
        
    Returns:
        DB.Line: The created line geometry
    """
    try:
        return DB.Line.CreateBound(start_point, end_point)
    except:  # noqa
        return None


def create_circle(center_point, radius, normal=None):
    # type: (DB.XYZ, float, DB.XYZ) -> DB.Arc | None
    """Create a circle at the specified center with given radius.
    
    Args:
        center_point (DB.XYZ): Center point of the circle
        radius (float): Radius of the circle
        normal (DB.XYZ, optional): Normal vector for circle plane. Defaults to Z-axis.
        
    Returns:
        DB.Arc: The created circle geometry (as full arc)
    """
    try:
        if normal is None:
            normal = DB.XYZ.BasisZ
        plane = DB.Plane.CreateByNormalAndOrigin(normal, center_point)
        circle = DB.Arc.Create(plane, radius, 0, 2 * math.pi)
        return circle
    except:  # noqa
        return None
    

def create_rectangle(center_point, width, height, normal=None):
    # type: (DB.XYZ, float, float, DB.XYZ) -> List[DB.Line]
    """Create a rectangle at the specified center with given dimensions.
    
    Args:
        center_point (DB.XYZ): Center point of the rectangle
        width (float): Width of the rectangle
        height (float): Height of the rectangle
        normal (DB.XYZ, optional): Normal vector for rectangle plane. Defaults to Z-axis.
        
    Returns:
        List[DB.Line]: List of four lines forming the rectangle
    """
    try:
        if normal is None:
            normal = DB.XYZ.BasisZ
            
        # Calculate half dimensions
        half_width = width / 2.0
        half_height = height / 2.0
        
        # Determine the coordinate system
        if abs(normal.DotProduct(DB.XYZ.BasisZ)) > 0.9:
            # Normal is close to Z-axis, use X and Y for rectangle plane
            x_axis = DB.XYZ.BasisX
            y_axis = DB.XYZ.BasisY
        else:
            # Calculate perpendicular axes
            x_axis = normal.CrossProduct(DB.XYZ.BasisZ).Normalize()
            y_axis = normal.CrossProduct(x_axis).Normalize()
        
        # Calculate corner points
        corner1 = center_point + x_axis * half_width + y_axis * half_height
        corner2 = center_point - x_axis * half_width + y_axis * half_height
        corner3 = center_point - x_axis * half_width - y_axis * half_height
        corner4 = center_point + x_axis * half_width - y_axis * half_height
        
        # Create the four lines forming the rectangle
        lines = List[DB.Line]()
        lines.Add(DB.Line.CreateBound(corner1, corner2))
        lines.Add(DB.Line.CreateBound(corner2, corner3))
        lines.Add(DB.Line.CreateBound(corner3, corner4))
        lines.Add(DB.Line.CreateBound(corner4, corner1))
        
        return lines
    except:  # noqa
        return List[DB.Line]()
    

def create_rectangle_from_curve(curve, extension_distance):
    # type: (DB.Curve, float) -> list[DB.Line]
    """Create a rectangle around a line segment with extension distance.
    
    Args:
        curve (DB.Curve): The curve to create rectangle from
        extension_distance (float): Distance to extend the rectangle around the line
        
    Returns:
        List[DB.Line]: List of four lines forming the rectangle
    """
    try:
        # Calculate line direction and perpendicular
        start_point = curve.GetEndPoint(0)
        end_point = curve.GetEndPoint(1)
        line_vector = end_point - start_point
        line_length = line_vector.GetLength()
        
        if line_length < 1e-6:  # Very short line
            return List[DB.Line]()
            
        line_direction = line_vector.Normalize()
        
        # Find a perpendicular vector in the horizontal plane
        if abs(line_direction.DotProduct(DB.XYZ.BasisZ)) > 0.9:
            # Line is nearly vertical, use X-axis as perpendicular
            perpendicular = DB.XYZ.BasisX
        else:
            # Create horizontal perpendicular
            horizontal_line = DB.XYZ(line_direction.X, line_direction.Y, 0).Normalize()
            perpendicular = DB.XYZ.BasisZ.CrossProduct(horizontal_line).Normalize()
        
        # Calculate offset points
        offset_vector = perpendicular * extension_distance
        
        # Calculate rectangle corners
        corner1 = start_point + offset_vector
        corner2 = start_point - offset_vector
        corner3 = end_point - offset_vector
        corner4 = end_point + offset_vector
        
        # Create the four lines forming the rectangle
        lines = List[DB.Line]()
        lines.Add(DB.Line.CreateBound(corner1, corner2))
        lines.Add(DB.Line.CreateBound(corner2, corner3))
        lines.Add(DB.Line.CreateBound(corner3, corner4))
        lines.Add(DB.Line.CreateBound(corner4, corner1))
        
        return lines
    except:  # noqa
        return List[DB.Line]()
    

def create_circle_around_point(point, radius):
    # type: (DB.XYZ, float) -> DB.Arc
    """Create a horizontal circle around a point for proximity detection.
    
    Args:
        point (DB.XYZ): Center point of the circle
        radius (float): Radius of the circle
        
    Returns:
        DB.Arc: The created circle geometry
    """
    return create_circle(point, radius, DB.XYZ.BasisZ)


def sample_curve_points(curve, sample_count=3):
    # type: (DB.Curve, int) -> List[DB.XYZ]
    """Sample points along a curve at regular intervals.
    
    Args:
        curve (DB.Curve): The curve to sample
        sample_count (int): Number of sample points (default: 3 for start/mid/end)
        
    Returns:
        List[DB.XYZ]: List of sampled points
    """
    try:
        points = []
        for i in range(sample_count):
            if sample_count == 1:
                param = 0.5  # Single point at middle
            else:
                param = i / float(sample_count - 1)  # 0.0 to 1.0
            point = curve.Evaluate(param, True)
            points.append(point)
        return points
    except Exception as ex:
        Debug.WriteLine("Error sampling curve points: {}".format(str(ex)))
        return []

def get_element_center_point(element):
    # type: (BaseGeometry) -> DB.XYZ | None
    """Get the center point of an element using various strategies.
    
    Args:
        element (BaseGeometry): The element to get center point from
        
    Returns:
        DB.XYZ: Center point of the element, or None if not found
    """
    try:
        # Try LocationPoint first
        bbox = element.bounding_box
        if bbox:
            center_point = (bbox.Min + bbox.Max) * 0.5
            return center_point
        return None
    except Exception as ex:
        Debug.WriteLine("Error getting element center point: {} at {} {}".format(str(ex), element._element.Document.Title, element._element.Id))
        return None

def get_closest_bbox_point(point, bbox):
    # type: (DB.XYZ, DB.BoundingBoxXYZ) -> DB.XYZ | None
    """Get the closest point on a bounding box to a given point."""
    try:
        # Clamp point coordinates to bounding box
        closest_x = max(bbox.Min.X, min(point.X, bbox.Max.X))
        closest_y = max(bbox.Min.Y, min(point.Y, bbox.Max.Y))
        closest_z = max(bbox.Min.Z, min(point.Z, bbox.Max.Z))
        return DB.XYZ(closest_x, closest_y, closest_z)
    except:  # noqa
        return None


def translate_point_by_z(point, z_offset):
    # type: (DB.XYZ, float) -> DB.XYZ
    """Translate a point by the specified Z offset.
    
    Args:
        point (DB.XYZ): Original point
        z_offset (float): Z offset to apply (negative goes down)
        
    Returns:
        DB.XYZ: Translated point
    """
    try:
        return DB.XYZ(point.X, point.Y, point.Z + z_offset)
    except:  # noqa
        return point


# Global cache for intersectors to avoid recreation
_intersector_cache = {
    'solid': None,
    'mesh': None
}


def create_reference_intersector(document, spatial_element):
    # type: (DB.Document, GenericSpatial) -> DB.ReferenceIntersector | None
    """Create a ReferenceIntersector for proximity detection based on spatial element geometry.
    
    Args:
        document (DB.Document): The document
        spatial_element (GenericSpatial): The spatial element to determine geometry type for
        
    Returns:
        DB.ReferenceIntersector: Appropriate cached intersector or None if failed
    """
    global _intersector_cache
    
    try:
        # Import here to avoid circular imports
        from ..models.spatial import GenericSpatial
        
        # Determine if geometry is solid for GenericSpatial elements
        if isinstance(spatial_element, GenericSpatial):
            geometry = spatial_element.geometry
            is_solid = isinstance(geometry, DB.Solid)
        else:
            is_solid = True
        
        cache_key = 'solid' if is_solid else 'mesh'
        
        # Return cached intersector if available
        if _intersector_cache[cache_key] is not None:
            return _intersector_cache[cache_key]
        
        # Create new intersector
        from ..utils.collector_utils import get_ifc_space_type_filter
        element_filter = get_ifc_space_type_filter()
        if is_solid:
            intersector = DB.ReferenceIntersector(  # noqa
                element_filter,
                DB.FindReferenceTarget.Face,  # For Solid geometry
                document.ActiveView
            )
        else:
            intersector = DB.ReferenceIntersector(  # noqa
                element_filter,
                DB.FindReferenceTarget.Mesh,  # For Mesh geometry  
                document.ActiveView
            )

        intersector.FindReferencesInRevitLinks = True
        # Cache the intersector
        _intersector_cache[cache_key] = intersector  # noqa
        return intersector
            
    except Exception as ex:  # noqa
        print("Error creating reference intersector: {}".format(str(ex)))
        # Fallback to solid intersector - create if not cached
        if _intersector_cache['solid'] is None:
            try:
                from ..utils.collector_utils import get_ifc_space_type_filter
                element_filter = get_ifc_space_type_filter()
                intersector = DB.ReferenceIntersector(  # noqa
                    element_filter,
                    DB.FindReferenceTarget.Face,
                    document.ActiveView
                )
                _intersector_cache['solid'] = intersector  # noqa
            except:  # noqa
                return None
        
        return _intersector_cache['solid']


def clear_intersector_cache():
    # type: () -> None
    """Clear the intersector cache. Call this when tool is closing or document changes."""
    global _intersector_cache
    _intersector_cache = {
        'solid': None,
        'mesh': None
    }


def reference_to_element(reference, document):
    # type: (DB.Reference, DB.Document) -> DB.Element | None
    """
    Converts a Revit Reference to an Element.
    
    Args:
        reference (DB.Reference): The Revit Reference to convert
        document (DB.Document): The document to get element from
        
    Returns:
        DB.Element: The corresponding Element, or None if not found
    """
    try:
        if not reference:
            return None
            
        element = document.GetElement(reference)
        if isinstance(element, DB.RevitLinkInstance):
            link_doc = element.GetLinkDocument()
            if link_doc and reference.LinkedElementId:
                return link_doc.GetElement(reference.LinkedElementId)
        
        return element
        
    except:  # noqa
        return None


def is_point_in_bounding_box(point, bbox):
    # type: (DB.XYZ, DB.BoundingBoxXYZ) -> bool
    """Check if a point is inside a bounding box."""
    try:
        return (bbox.Min.X <= point.X <= bbox.Max.X and
                bbox.Min.Y <= point.Y <= bbox.Max.Y and
                bbox.Min.Z <= point.Z <= bbox.Max.Z)
    except:  # noqa
        return False


def is_point_in_solid(point, solid):
    # type: (DB.XYZ, DB.Solid) -> bool
    """Check if a point is located inside a Revit Solid using SolidCurveIntersection.
    
    This method creates a small line from the test point and uses Revit's 
    IntersectWithCurve method with CurveSegmentsInside mode to determine if
    the point is inside the solid.
    
    Args:
        point (DB.XYZ): The point to test
        solid (DB.Solid): The solid geometry to test against
        
    Returns:
        bool: True if point is inside solid, False otherwise
    """
    try:
        if not point or not solid:
            return False
            
        # Use a small vector to create a line from the point
        # Test both positive and negative X directions to handle edge cases
        test_vectors = [DB.XYZ.BasisX, DB.XYZ.BasisX.Negate()]
        tolerance = 1e-6
        
        for vector in test_vectors:
            try:
                # Create a small line from the test point
                end_point = point + vector * 0.1  # 0.1 foot = ~30mm
                line = DB.Line.CreateBound(point, end_point)
                
                # Set up intersection options to find curve segments inside solid
                options = DB.SolidCurveIntersectionOptions()
                options.ResultType = DB.SolidCurveIntersectionMode.CurveSegmentsInside
                
                # Perform intersection
                intersection = solid.IntersectWithCurve(line, options)
                
                # Check if any intersection segment has the test point as an endpoint
                for i in range(intersection.SegmentCount):
                    curve_segment = intersection.GetCurveSegment(i)
                    start_point = curve_segment.GetEndPoint(0)
                    end_point = curve_segment.GetEndPoint(1)
                    
                    if (point.IsAlmostEqualTo(start_point, tolerance) or 
                        point.IsAlmostEqualTo(end_point, tolerance)):
                        return True
                        
            except Exception:
                continue
                
        return False
        
    except Exception as ex:
        Debug.WriteLine("Error checking point in solid: {}".format(str(ex)))
        return False


def is_point_in_meshes(point, meshes, transform=None):
    # type: (DB.XYZ, List[DB.Mesh], DB.Transform) -> bool
    """Check if a point is located inside any of the provided meshes.
    
    This function uses ray casting to determine if a point is inside mesh geometry.
    Uses multiple ray directions for robust detection and consensus voting.
    
    Args:
        point (DB.XYZ): The point to test
        meshes (List[DB.Mesh]): List of mesh geometries to test against
        transform (DB.Transform, optional): Transform to apply to points. Defaults to None.
        
    Returns:
        bool: True if point is inside any mesh, False otherwise
    """
    try:
        if not point or not meshes:
            return False
            
        # Use multiple ray directions for robust detection
        ray_directions = [
            DB.XYZ.BasisX,           # +X direction
            DB.XYZ.BasisX.Negate(),  # -X direction  # noqa
            DB.XYZ.BasisY,           # +Y direction
            DB.XYZ.BasisY.Negate(),  # -Y direction  # noqa
            DB.XYZ.BasisZ,           # +Z direction
            DB.XYZ.BasisZ.Negate()   # -Z direction  # noqa
        ]
        
        for mesh in meshes:
            if not mesh:
                continue
                
            inside_votes = 0
            total_votes = len(ray_directions)
            
            # Test with multiple ray directions
            for ray_direction in ray_directions:
                intersection_count = 0
                
                # Check intersections with all triangles in the mesh
                for triangle_index in range(mesh.NumTriangles):
                    try:
                        triangle = mesh.get_Triangle(triangle_index)
                        vertex1 = transform.OfPoint(triangle.get_Vertex(0)) if transform else triangle.get_Vertex(0)
                        vertex2 = transform.OfPoint(triangle.get_Vertex(1)) if transform else triangle.get_Vertex(1)
                        vertex3 = transform.OfPoint(triangle.get_Vertex(2)) if transform else triangle.get_Vertex(2)
                        
                        # Check if ray intersects with this triangle
                        if _ray_intersects_triangle(point, ray_direction, vertex1, vertex2, vertex3):
                            intersection_count += 1
                            
                    except:  # noqa
                        continue
                
                # Odd number of intersections means point is inside for this direction
                if intersection_count % 2 == 1:
                    inside_votes += 1
            
            # Consensus voting: point is inside if majority of rays agree
            # Use > 50% threshold for robust detection
            if inside_votes > total_votes // 2:
                return True
                
        return False
        
    except Exception as ex:
        Debug.WriteLine("Error checking point in meshes: {}".format(str(ex)))
        return False


def _ray_intersects_triangle(ray_origin, ray_direction, vertex1, vertex2, vertex3):
    # type: (DB.XYZ, DB.XYZ, DB.XYZ, DB.XYZ, DB.XYZ) -> bool
    """Check if a ray intersects with a triangle using Möller-Trumbore algorithm.
    
    Args:
        ray_origin (DB.XYZ): Origin of the ray
        ray_direction (DB.XYZ): Direction of the ray (should be normalized)
        vertex1 (DB.XYZ): First vertex of triangle
        vertex2 (DB.XYZ): Second vertex of triangle
        vertex3 (DB.XYZ): Third vertex of triangle
        
    Returns:
        bool: True if ray intersects triangle, False otherwise
    """
    try:
        epsilon = 1e-8
        
        # Calculate triangle edges
        edge1 = vertex2 - vertex1
        edge2 = vertex3 - vertex1
        
        # Calculate determinant
        h = ray_direction.CrossProduct(edge2)
        a = edge1.DotProduct(h)
        
        # Ray is parallel to triangle
        if abs(a) < epsilon:
            return False
            
        f = 1.0 / a
        s = ray_origin - vertex1
        u = f * s.DotProduct(h)
        
        # Check barycentric coordinate u
        if u < 0.0 or u > 1.0:
            return False
            
        q = s.CrossProduct(edge1)
        v = f * ray_direction.DotProduct(q)
        
        # Check barycentric coordinate v
        if v < 0.0 or u + v > 1.0:
            return False
            
        # Calculate intersection distance
        t = f * edge2.DotProduct(q)
        
        # Ray intersection (t > epsilon means intersection is in positive direction)
        return t > epsilon
        
    except:  # noqa
        return False


def check_ray_distance(start_point, end_point, intersector, max_distance, target_spatial_element, show_visual=False):
    # type: (DB.XYZ, DB.XYZ, DB.ReferenceIntersector, float, DB.Element, bool) -> tuple[bool, float]
    """
    Check if ray casting hits the target spatial element within max distance.
    
    Returns True ONLY if ALL 3 conditions are met:
    1. Ray hits something
    2. Hit element is the target spatial element
    3. Distance <= max_distance
    
    Args:
        start_point (DB.XYZ): Starting point for ray casting
        end_point (DB.XYZ): End point to determine direction
        intersector (DB.ReferenceIntersector): The intersector to use
        max_distance (float): Maximum allowed distance in internal units
        target_spatial_element (DB.Element): The spatial element we expect to hit
        show_visual (bool): If True, traces ray and hit point visually for debugging
        
    Returns:
        tuple[bool, float]: True only if all 3 conditions are satisfied, False otherwise and distance to the hit point
    """
    try:
        # Calculate direction vector
        direction = (end_point - start_point).Normalize()
        
        if show_visual:
            # Create and trace the ray line
            ray_line = create_line(start_point, start_point + direction * max_distance)
            if ray_line:
                Trace.Write(ray_line)  # Visual trace of ray casting line
        
        # Perform ray casting
        reference_with_context = intersector.FindNearest(start_point, direction)  # type: DB.ReferenceWithContext
        
        # Condition 1: Ray hits something
        if not reference_with_context:
            return False, 0
            
        reference = reference_with_context.GetReference()  # type: DB.Reference
        if not reference:
            return False, 0
            
        # Condition 2: Hit element is the target spatial element
        hit_element = reference_to_element(reference, HOST_APP.doc)
        if not hit_element or hit_element.Id != target_spatial_element.Id:
            return False, 0
            
        # Condition 3: Distance <= max_distance
        hit_point = reference.GlobalPoint  # type: DB.XYZ
        distance = start_point.DistanceTo(hit_point)
        if distance > max_distance:
            return False, 0
        
        if show_visual:
            # Trace the hit point
            Trace.Write(hit_point)  # Visual trace of hit point
            # Trace the actual hit distance line
            hit_line = create_line(start_point, hit_point)
            if hit_line:
                Trace.Write(hit_line)  # Visual trace of actual hit distance
        
        # All 3 conditions satisfied
        return True, distance
        
    except:  # noqa
        return False, 0

def boundingbox_to_solid(bbox):
    # type: (DB.BoundingBoxXYZ) -> DB.Solid
    """Convert a bounding box to a Revit Solid geometry."""
    # corners in BBox coords
    pt0 = DB.XYZ(bbox.Min.X, bbox.Min.Y, bbox.Min.Z)
    pt1 = DB.XYZ(bbox.Max.X, bbox.Min.Y, bbox.Min.Z)
    pt2 = DB.XYZ(bbox.Max.X, bbox.Max.Y, bbox.Min.Z)
    pt3 = DB.XYZ(bbox.Min.X, bbox.Max.Y, bbox.Min.Z)
    # edges in BBox coords
    edge0 = DB.Line.CreateBound(pt0, pt1)
    edge1 = DB.Line.CreateBound(pt1, pt2)
    edge2 = DB.Line.CreateBound(pt2, pt3)
    edge3 = DB.Line.CreateBound(pt3, pt0)
    # create loop, still in BBox coords
    edges = [edge0, edge1, edge2, edge3]
    height = bbox.Max.Z - bbox.Min.Z
    baseLoop = DB.CurveLoop.Create(edges)
    loopList = []
    loopList.append(baseLoop)
    pre_transform_box = DB.GeometryCreationUtilities.CreateExtrusionGeometry(
        loopList, DB.XYZ.BasisZ, height  # noqa
    )

    return pre_transform_box