# coding: utf-8
"""
Models Module

This module provides all model classes for the room number mapping tool,
including MEP elements, spatial elements, and data structures.

Available Classes:
    MEP Elements: BaseMep, CurveBasedMep, PointBasedMep
    Spatial Elements: BaseSpatial, RevitSpatial, GenericSpatial  
    Data Models: DetectionResult, ParameterPair, ParameterWrapper, TargetCategory, DocumentWrapper
"""

# Import all model classes
from .spatial import *
from .mep import *
from .detection_result import DetectionResult
from .parameter_pair import ParameterPair
from .parameter_wrapper import ParameterWrapper
from .target_category import TargetCategory
from .document_wrapper import DocumentWrapper