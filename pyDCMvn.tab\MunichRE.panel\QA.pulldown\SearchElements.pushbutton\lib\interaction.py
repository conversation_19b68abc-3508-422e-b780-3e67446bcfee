import clr
from DCMvn.core import DB, UI
from DCMvn.core.framework import System
from DCMvn.forms import alert

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

def set_scope(uidoc, elements, transform=None, create_section_box=False):
    # type: (UI.UIDocument, list[DB.Element], DB.Transform, bool) -> None  # noqa
    """ Set a section box around the bounding box of the elements. """
    if not elements:
        return

    view = uidoc.ActiveView  # type: DB.View3D
    if view.ViewType != DB.ViewType.ThreeD:
        view = DB.FilteredElementCollector(uidoc.Document).OfClass(DB.View3D).FirstOrDefault()  # noqa
        if not view:
            alert("No 3D view found to create section box", warn_icon=True)
            return

    bbox_collect = []
    for ele in elements:
        bbox = ele.get_BoundingBox(None)  # type: DB.BoundingBoxXYZ # noqa
        if bbox:
            bbox_collect.append(bbox)

    bbox_max = bbox_collect.Select(lambda x: x.Max).OrderByDescending(lambda x: x.X).ThenByDescending(lambda x: x.Y).ThenByDescending(lambda x: x.Z).FirstOrDefault()  # noqa
    bbox_min = bbox_collect.Select(lambda x: x.Min).OrderBy(lambda x: x.X).ThenBy(lambda x: x.Y).ThenBy(lambda x: x.Z).FirstOrDefault()  # noqa

    try:
        scope_bbox = DB.BoundingBoxXYZ()
        if transform:
            scope_bbox.Max = transform.OfPoint(bbox_max)
            scope_bbox.Min = transform.OfPoint(bbox_min)
        else:
            scope_bbox.Max = bbox_max
            scope_bbox.Min = bbox_min

        ui_views = uidoc.GetOpenUIViews()
        ui_view = [x for x in ui_views if x.ViewId == view.Id][0]  # type: UI.UIView
        ui_view.ZoomAndCenterRectangle(bbox_min, bbox_max)

        if create_section_box:
            with DB.Transaction(uidoc.Document, "Set Section Box") as t:
                t.Start()
                view.SetSectionBox(scope_bbox)
                t.Commit()
    except Exception as e:
        print(e)