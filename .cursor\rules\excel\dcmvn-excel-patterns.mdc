---
description: DCMvn Excel operations using MiniExcel for safe and effective file handling
---

# DCMvn Excel Operations with MiniExcel

## Core Imports and Setup
Always use this import pattern at the top of your module for Excel operations:
```python
# coding: utf-8
import clr
import os

# DCMvn core imports
from DCMvn.io import save_excel_file
from DCMvn.coreutils.assemblyhandler import load_miniexcel
from DCMvn.core.framework import IDictionary, Debug
from DCMvn.forms import alert

# Load MiniExcel assembly at module level
load_miniexcel()
from MiniExcelLibs import MiniExcel
from MiniExcelLibs.OpenXml import OpenXmlConfiguration

# .NET collections support
clr.AddReference("System.Collections.Specialized")
from System.Collections.Specialized import OrderedDictionary
from System.Collections.Generic import Dictionary
```

## Safe File Operations Pattern
Always follow this pattern for Excel file operations:

### 1. File Path Selection
```python
# Use DCMvn's built-in file dialog
file_path = save_excel_file(title="Save Report Name")
if not file_path:
    return None  # User cancelled
```

### 2. Safe File Cleanup
```python
# Always clean existing file before saving
if os.path.exists(file_path):
    os.remove(file_path)
```

### 3. Exception Handling
```python
try:
    MiniExcel.SaveAs(file_path, data)
    return file_path
except Exception as e:
    from DCMvn.forms import alert
    alert("Failed to save Excel file: {}".format(str(e)))
    return None
```

## Data Structure Patterns

### Single Sheet Data (List of OrderedDictionary)
```python
def create_single_sheet_data(items):
    """Create data for a single Excel sheet with consistent column order."""
    sheet_data = []
    for item in items:
        row_data = OrderedDictionary()
        row_data["Column1"] = item.property1
        row_data["Column2"] = item.property2
        row_data["Column3"] = item.property3
        sheet_data.append(row_data)
    return sheet_data
```

### Multi-Sheet Data (Dictionary[string, List])
```python
def create_multi_sheet_data():
    """Create data for multiple Excel sheets."""
    exported_data = Dictionary[str, object]()
    
    # Each key becomes a sheet name
    exported_data["Summary"] = summary_data
    exported_data["Details"] = details_data
    exported_data["Errors"] = error_data
    
    return exported_data
```

## Column Ordering Helper
Use this helper function to ensure consistent column ordering:
```python
def create_ordered_row(key_value_pairs):
    """
    Create an OrderedDictionary from a list of (key, value) tuples.
    
    Args:
        key_value_pairs: List of (key, value) tuples in desired column order
        
    Returns:
        OrderedDictionary: Ordered row data
    """
    ordered_dict = OrderedDictionary()
    for key, value in key_value_pairs:
        ordered_dict[key] = value
    return ordered_dict

# Usage example:
row_data = [
    ("Element ID", element.Id.IntegerValue),
    ("Name", element.Name),
    ("Category", element.Category.Name),
    ("Status", status)
]
ordered_row = create_ordered_row(row_data)
```

## Unit Conversion Pattern
Always convert Revit internal units for Excel export:
```python
from DCMvn.core import DB

# Area conversion
area_m2 = DB.UnitUtils.ConvertFromInternalUnits(
    area_internal, DB.UnitTypeId.SquareMeters
)
formatted_area = "{:.3f} m2".format(area_m2)

# Volume conversion  
volume_m3 = DB.UnitUtils.ConvertFromInternalUnits(
    volume_internal, DB.UnitTypeId.CubicMeters
)
formatted_volume = "{:.3f} m3".format(volume_m3)

# Length conversion
length_mm = DB.UnitUtils.ConvertFromInternalUnits(
    length_internal, DB.UnitTypeId.Millimeters
)
```

## User Experience Pattern
Always provide user feedback and folder opening option:
```python
def save_and_notify_user(file_path, data, success_message="Data exported successfully"):
    """Save Excel file and provide user feedback."""
    try:
        # Clean and save
        if os.path.exists(file_path):
            os.remove(file_path)
        MiniExcel.SaveAs(file_path, data)
        
        # User notification with folder opening option
        from DCMvn.forms import alert
        open_folder = alert(
            "{}:\n{}\n\nOpen folder?".format(success_message, file_path),
            yes=True, no=True
        )
        if open_folder:
            folder_path = os.path.dirname(file_path)
            os.startfile(folder_path)
            
        return file_path
    except Exception as e:
        alert("Failed to save file: {}".format(str(e)))
        return None
```

## Configuration Integration
For viewmodels that need persistent Excel folder paths:
```python
from DCMvn.io import get_config_property, set_config_property

class ExcelViewModel(ViewModelBase):
    def __init__(self):
        # Configuration management
        self._config_section = "YourApp.ExcelConfig"
        self._excel_folder_property = "EXCEL_FOLDER_PATH"
        
        # Load saved path
        self._excel_folder_path = get_config_property(
            self._config_section, self._excel_folder_property
        )
    
    def save_folder_path(self, folder_path):
        """Save folder path to configuration."""
        set_config_property(
            self._config_section, self._excel_folder_property, folder_path
        )
        self._excel_folder_path = folder_path
```

## Reading Excel Files Pattern
```python
def read_excel_safely(excel_path, sheet_name="Sheet1", use_header_row=True):
    """Read Excel file with error handling using DCMvn MiniExcel."""
    try:
        if not os.path.exists(excel_path):
            return None
        
        # Query Excel data (MiniExcel already loaded at module level)
        data = MiniExcel.Query(
            excel_path, 
            useHeaderRow=use_header_row, 
            sheetName=sheet_name
        ).Cast[IDictionary[str, object]]()
        
        return data
    except Exception as e:
        Debug.WriteLine("Failed to read Excel: {}".format(str(e)))
        return None

def read_excel_with_config(excel_path, sheet_name="Sheet1", start_cell="A1"):
    """Read Excel with advanced configuration."""
    try:
        # Configure options
        config = OpenXmlConfiguration()
        config.FillMergedCells = True
        
        # Query with configuration
        data = MiniExcel.Query(
            excel_path, 
            useHeaderRow=True, 
            startCell=start_cell,
            sheetName=sheet_name, 
            configuration=config
        ).Cast[IDictionary[str, object]]()
        
        return data
    except Exception as e:
        Debug.WriteLine("Failed to read Excel with config: {}".format(str(e)))
        return None

def get_sheet_names(excel_path):
    """Get all sheet names from Excel file."""
    try:
        return MiniExcel.GetSheetNames(excel_path)
    except Exception as e:
        Debug.WriteLine("Failed to get sheet names: {}".format(str(e)))
        return []
```

## Error Handling Best Practices
1. **Always use try-catch** around MiniExcel operations
2. **Log errors** using DCMvn's Debug.WriteLine for development
3. **Show user-friendly messages** using DCMvn.forms.alert
4. **Return None or False** to indicate failure
5. **Clean up files** before saving to avoid conflicts

## Performance Considerations
1. **Use OrderedDictionary** only when column order matters
2. **Pre-build data structures** before Excel operations
3. **Batch operations** rather than multiple small saves
4. **Cache configuration** properties to avoid repeated file I/O