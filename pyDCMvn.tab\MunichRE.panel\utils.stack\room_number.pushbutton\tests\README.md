# Room Number Mapping Test Suite

Comprehensive test suite for room number mapping functionality with organized test categories and clear visualization using `Trace.Write()` for geometry objects.

## Quick Start

```python
# Import the main test module
from test_new import *

# Run complete test suite
run_all_tests()

# Or run specific test categories
run_spatial_tests()        # Tests 1-4
run_intersection_tests()   # Tests 5-9

# Run individual tests
run_test_01()  # Trace spatial geometry
run_test_05()  # MEP intersection test
```

## Test Structure

### Tests 1-4: Spatial Geometry Tests (`test_spatial_geometry.py`)
Focus on linked spatial element geometry analysis and visualization:

- **Test 1**: `test_01_trace_linked_spatial_geometry()`
  - Select linked spatial → Trace geometry using `Trace.Write(geometry)`
  - Visualizes raw geometry, volume, surface area

- **Test 2**: `test_02_trace_linked_spatial_bounding_box()`
  - Select linked spatial → Trace bounding box using `Trace.WriteLine(bbox)`
  - Shows dimensions, volume, transform info

- **Test 3**: `test_03_trace_linked_spatial_expanded_bounding_box()`
  - Select linked spatial → Trace expanded bounding box (300mm offset)
  - Shows proximity detection boundaries with size comparisons

- **Test 4**: `test_04_trace_linked_spatial_center_point()`
  - Select linked spatial → Trace center point using `Trace.WriteLine(point)`
  - Calculates and visualizes center point with coordinate details

### Tests 5-9: MEP Intersection Tests (`test_mep_intersection.py`)
Focus on MEP-spatial relationship detection and analysis:

- **Test 5**: `test_05_mep_intersection_with_linked_spatial()`
  - Select MEP → Select linked spatial → Test direct intersection
  - No proximity detection, pure geometric intersection

- **Test 6**: `test_06_mep_intersection_with_proximity()`
  - Select MEP → Select linked spatial → Test intersection with 300mm proximity
  - Shows both direct and proximity-based detection

- **Test 7**: `test_07_linked_spatial_show_intersection_meps()`
  - Select linked spatial → Find all intersecting MEP elements by category
  - Comprehensive analysis of all MEP categories in current document

- **Test 8**: `test_08_linked_spatial_show_proximity_meps()`
  - Select linked spatial → Find all MEP elements within 300mm by category
  - Uses BoundingBoxIntersectsFilter for performance optimization

- **Test 9**: `test_09_interactive_mep_spatial_intersection()`
  - Interactive test: Select spatial → Select MEP → Detailed analysis
  - Compares direct vs proximity results with detailed logging

### Tests 10+: Additional Tests (`test_runner.py`)
Extended testing for performance and edge cases:

- **Test 10**: `test_10_performance_benchmark()`
  - Performance timing for different detection strategies
  - Measures direct vs proximity detection overhead

- **Test 11**: `test_11_edge_cases()`
  - Edge case testing framework for unusual elements
  - Tests robustness with invalid/unusual configurations

- **Test 12**: `test_12_validation_and_reporting()`
  - Result validation and comprehensive reporting
  - Accuracy verification and cross-reference testing

## Test Utilities (`test_utils.py`)

### Core Helper Classes

- **`TestOptions`**: Centralized configuration (proximity distances, debug settings)
- **`SpatialElementSelector`**: Linked spatial element selection helper
- **`MepElementSelector`**: MEP element selection helper  
- **`SpatialWrapperFactory`**: Creates appropriate spatial wrappers (Revit vs Generic)
- **`GeometryHelper`**: Geometry operations and transformations
- **`TestLogger`**: Centralized logging with element/geometry info
- **`IntersectionTester`**: MEP-spatial intersection testing

### Key Features

- **Automatic Element Type Detection**: Distinguishes Room/Space vs Generic spatials
- **Coordinate Transformation Handling**: Proper linked document coordinate handling
- **Performance Optimization**: BoundingBox pre-filtering for large datasets
- **Comprehensive Logging**: Detailed debug output with visual tracing
- **Error Resilience**: Graceful handling of selection and processing errors

## Architecture Integration

### Models Used
- `BaseMep`, `PointBasedMep`, `CurveBasedMep` from `lib.models.mep`
- `RevitSpatial`, `GenericSpatial` from `lib.models.spatial`
- `DocumentWrapper` for linked document handling

### Services Used
- `DetectionService` for MEP classification
- `GeometryDetector` for advanced geometry algorithms
- `RevitSpatialStrategy`, `GenericSpatialStrategy` for detection

### Advanced Options
- `AdvancedOptionsViewModel` for proximity settings
- Configurable proximity distances (default: 300mm)
- Z-offset detection for elements above spaces

## Usage Examples

### Run All Tests
```python
from test_new import run_all_tests
run_all_tests()  # Runs tests 1-9 sequentially
```

### Run Specific Test Category
```python
from test_new import run_spatial_tests, run_intersection_tests
run_spatial_tests()        # Tests 1-4: Geometry analysis
run_intersection_tests()   # Tests 5-9: MEP intersections
```

### Run Individual Tests
```python
from test_new import run_test_01, run_test_05
run_test_01()  # Trace spatial geometry
run_test_05()  # MEP intersection test
```

### Import and Use Test Utilities
```python
from tests.test_utils import SpatialElementSelector, IntersectionTester

# Select elements manually
selector = SpatialElementSelector()
spatial_element, link_instance, doc_wrapper = selector.pick_linked_spatial()

# Test intersections
tester = IntersectionTester()
is_intersecting, distance, details = tester.test_mep_spatial_intersection(
    mep_element, spatial_wrapper, use_proximity=True
)
```

## Output and Visualization

### Trace Visualization
- **`Trace.Write(geometry)`**: Visualizes Solid, Mesh, or other geometry objects
- **`Trace.WriteLine(bbox)`**: Visualizes bounding boxes
- **`Trace.WriteLine(point)`**: Visualizes XYZ points
- **`Trace.Write(outline)`**: Visualizes outlines and expanded boundaries

### Debug Output
- Detailed element information (ID, name, category)
- Coordinate values in both internal units and mm
- Distance calculations and intersection results
- Performance timing and classification details

### Results
- User alerts with summary information
- Comprehensive debug log in Output window
- Visual geometry tracing for spatial understanding
- Performance metrics and validation data

## File Organization

```
tests/
├── __init__.py                    # Package initialization
├── README.md                      # This documentation
├── test_utils.py                  # Shared utilities and helpers
├── test_spatial_geometry.py       # Tests 1-4: Spatial geometry
├── test_mep_intersection.py       # Tests 5-9: MEP intersections
└── test_runner.py                 # Main test runner and Tests 10+

test_new.py                        # Main entry point and quick start
```

## Best Practices

1. **Use Visual Tracing**: Always use `Trace.Write()` for geometry visualization
2. **Check Output Window**: Detailed results are logged to debug output
3. **Test Incrementally**: Start with individual tests before running full suite
4. **Verify Selection**: Ensure correct element selection before running tests
5. **Monitor Performance**: Use Test 10 for performance analysis
6. **Handle Errors Gracefully**: Tests include comprehensive error handling

## Migration from Old Tests

The old `test.py` functions are mapped to new tests:
- `test_generic_spatial_detection()` → `run_test_09()`
- `test_mep_spatial_detection_with_ifc_spaces()` → `run_test_07()`

Use `test_new.py` as the main entry point for all new testing.