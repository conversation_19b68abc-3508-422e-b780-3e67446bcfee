# -*- coding: utf-8 -*-
import clr
from pyrevit import script

from DCMvn.core import DB, HOST_APP, get_output, REVIT_VERSION
from DCMvn.core.framework import List, IO, System # noqa
from DCMvn.io import pick_excel_file
from DCMvn.forms import wpfforms, alert
from DCMvn.revit.transaction import transaction_wrapper

# Load MiniExcel for reading Excel files
from DCMvn.coreutils.assemblyhandler import load_miniexcel
load_miniexcel()
from MiniExcelLibs import MiniExcel

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

output = get_output()
logger = script.get_logger()
doc = HOST_APP.doc


def select_work_type():
    """Bước 1: Tạo UI để người dùng chọn loại công việc"""
    # type: () -> str | None
    
    work_options = ["Workset Filter", "Status Filter"]
    
    # Thử CommandSwitchWindow trước
    try:
        selected_work = wpfforms.CommandSwitchWindow.show(work_options, "Select Work Type")
        if selected_work:
            return selected_work
    except:
        pass
    
    # Fallback: sử dụng SelectFromList
    try:
        selected_work = wpfforms.SelectFromList.show(
            work_options, 
            title="Select Work Type",
            multiselect=False,
            button_name="Select"
        )
        return selected_work
    except Exception as e:
        output.print_md("Error with work type selection: {}".format(str(e)))
        alert("Error selecting work type: {}".format(str(e)), warn_icon=True)
        return None


def select_excel_file_and_sheet():
    """Bước 3: Cho phép người dùng chọn Excel file và sheet"""
    # type: () -> tuple[str, str] | None
    
    # Chọn Excel file
    excel_file = pick_excel_file(title="Select Excel File")
    if not excel_file:
        return None
    
    # Lấy danh sách sheets
    try:
        sheet_names = MiniExcel.GetSheetNames(excel_file)
        if not sheet_names or len(sheet_names) == 0:
            alert("No sheets found in Excel file.", warn_icon=True)
            return None
        
        # Chuyển đổi thành list để sử dụng với CommandSwitchWindow
        sheet_list = [str(sheet) for sheet in sheet_names]
        
        # Cho phép người dùng chọn sheet
        try:
            selected_sheet = wpfforms.CommandSwitchWindow.show(sheet_list, "Select Sheet")
            if selected_sheet:
                return excel_file, selected_sheet
        except:
            pass
        
        # Fallback: sử dụng SelectFromList
        try:
            selected_sheet = wpfforms.SelectFromList.show(
                sheet_list, 
                title="Select Sheet",
                multiselect=False,
                button_name="Select"
            )
            if not selected_sheet:
                return None
        except Exception as e:
            output.print_md("Error with sheet selection: {}".format(str(e)))
            alert("Error selecting sheet: {}".format(str(e)), warn_icon=True)
            return None
            
        return excel_file, selected_sheet
        
    except Exception as e:
        alert("Error reading Excel file: {}".format(str(e)), warn_icon=True)
        return None


def select_status_column(excel_file, sheet_name):
    """Cho phép người dùng chọn cột status từ Excel file"""
    # type: (str, str) -> str | None
    
    try:
        output.print_md("## Selecting Status Column...")
        
        # Đọc dữ liệu Excel để lấy danh sách columns
        excel_data = MiniExcel.Query(excel_file, useHeaderRow=True, sheetName=sheet_name)
        rows = list(excel_data)
        
        if not rows or len(rows) == 0:
            alert("No data found in Excel sheet to analyze columns.", warn_icon=True)
            return None
        
        # Lấy danh sách tất cả columns có sẵn
        first_row = rows[0]
        available_columns = []
        for attr in dir(first_row):
            if not attr.startswith('_'):
                available_columns.append(attr)
        
        if not available_columns:
            alert("No columns found in Excel sheet.", warn_icon=True)
            return None
        
        output.print_md("Available columns: {}".format(", ".join(available_columns)))
        
        # Cho phép người dùng chọn cột status
        try:
            # Thử dùng CommandSwitchWindow trước
            selected_column = wpfforms.CommandSwitchWindow.show(available_columns, "Select Status Column")
            if selected_column:
                return selected_column
        except:
            pass
        
        # Fallback: sử dụng SelectFromList nếu CommandSwitchWindow không hoạt động
        try:
            selected_column = wpfforms.SelectFromList.show(
                available_columns, 
                title="Select Status Column",
                multiselect=False,
                button_name="Select"
            )
            if selected_column:
                output.print_md("Selected status column: '{}'".format(selected_column))
                return selected_column
            else:
                return None
        except Exception as fallback_error:
            output.print_md("Error with column selection dialogs: {}".format(str(fallback_error)))
            # Fallback cuối cùng: chọn cột đầu tiên có chứa "assessment" hoặc "status"
            for col in available_columns:
                col_lower = col.lower()
                if 'assessment' in col_lower or 'status' in col_lower:
                    output.print_md("Auto-selected column: '{}'".format(col))
                    alert("Auto-selected status column: '{}'.\nIf this is incorrect, please check your Excel file.".format(col))
                    return col
            
            # Nếu không tìm thấy, cho phép người dùng chọn từ danh sách bằng alert
            column_list = "\n".join("{}. {}".format(i+1, col) for i, col in enumerate(available_columns))
            alert("Available columns:\n{}\n\nPlease restart and ensure your Excel file has a clear status column.".format(column_list), warn_icon=True)
            return None
        
    except Exception as e:
        output.print_md("ERROR selecting status column: {}".format(str(e)))
        alert("Failed to select status column: {}".format(str(e)), warn_icon=True)
        return None


def read_excel_data(excel_file, sheet_name, status_column):
    """Đọc dữ liệu Excel từ cột Key (IfcGUID) và cột status được chọn"""
    # type: (str, str, str) -> dict[str, str] | None
    
    try:
        output.print_md("## Reading Excel Data...")
        output.print_md("File: {}".format(excel_file))
        output.print_md("Sheet: {}".format(sheet_name))
        output.print_md("Status column: {}".format(status_column))
        
        # Đọc dữ liệu Excel
        excel_data = MiniExcel.Query(excel_file, useHeaderRow=True, sheetName=sheet_name)
        rows = list(excel_data)
        
        if not rows or len(rows) == 0:
            alert("No data found in Excel sheet.", warn_icon=True)
            return None
        
        output.print_md("Total rows found: {}".format(len(rows)))
        
        # Kiểm tra cột
        first_row = rows[0]
        has_key = hasattr(first_row, "Key")
        has_status = hasattr(first_row, status_column)
        
        if not has_key:
            alert("Required column 'Key' not found.", warn_icon=True)
            return None
            
        if not has_status:
            alert("Selected status column '{}' not found.".format(status_column), warn_icon=True)
            return None
        
        # Tạo dictionary: IfcGUID -> Status (sử dụng giá trị thực từ Excel)
        guid_status_map = {}
        sample_values = set()  # Để hiển thị các giá trị mẫu
        
        for i, row in enumerate(rows):
            try:
                ifc_guid = str(getattr(row, "Key", "")).strip()
                status = str(getattr(row, status_column, "")).strip()
                
                if ifc_guid and status:
                    # Sử dụng trực tiếp giá trị từ Excel column
                    guid_status_map[ifc_guid] = status
                    
                    # Thu thập các giá trị mẫu để hiển thị
                    if len(sample_values) < 10:
                        sample_values.add(status)
                    
            except Exception as row_error:
                continue
        
        # Hiển thị các giá trị mẫu từ Excel
        if sample_values:
            output.print_md("**Sample values from '{}' column:** {}".format(status_column, ", ".join(sorted(sample_values))))
        
        output.print_md("**Found {} valid IfcGUID-Status pairs**".format(len(guid_status_map)))
        return guid_status_map
        
    except Exception as e:
        output.print_md("ERROR reading Excel data: {}".format(str(e)))
        alert("Failed to read Excel data: {}".format(str(e)), warn_icon=True)
        return None


def get_all_elements_with_ifc_guid():
    """Thu thập TẤT CẢ elements có IFC GUID trong model"""
    # type: () -> dict[str, DB.Element]
    
    try:
        output.print_md("## Collecting all elements with IFC GUID in model...")
        
        # Thu thập tất cả elements (không phải element types)
        collector = DB.FilteredElementCollector(doc).WhereElementIsNotElementType()
        elements = collector.ToList()
        
        output.print_md("Total elements in model: {}".format(len(elements)))
        
        # Tạo dictionary: IfcGUID -> Element
        guid_element_map = {}
        
        for element in elements:
            try:
                # Lấy IFC GUID parameter
                ifc_param = element.get_Parameter(DB.BuiltInParameter.IFC_GUID)
                if ifc_param and ifc_param.HasValue:
                    ifc_guid = ifc_param.AsString()
                    if ifc_guid:
                        guid_element_map[ifc_guid] = element
            except:
                continue
        
        output.print_md("**Found {} elements with IFC GUID**".format(len(guid_element_map)))
        return guid_element_map
        
    except Exception as e:
        output.print_md("ERROR collecting elements: {}".format(str(e)))
        return {}


def set_element_comment_parameter(element, status):
    """Set Comments parameter (không tạo transaction)"""
    # type: (DB.Element, str) -> bool
    
    try:
        if not element or not element.IsValidObject:
            return False
        
        # Tìm parameter Comments
        comment_param = element.LookupParameter("Comments")
        if not comment_param:
            comment_param = element.LookupParameter("Comment")
        
        if not comment_param or comment_param.IsReadOnly:
            return False
        
        comment_param.Set(status)
        return True
                
    except Exception as e:
        logger.debug("Error setting comment for element {}: {}".format(element.Id.IntegerValue, str(e)))
        return False


def process_status_filter(excel_file, sheet_name):
    """Xử lý Status Filter: đọc Excel và cập nhật Comments parameter"""
    # type: (str, str) -> None
    
    try:
        output.print_md("## Processing Status Filter...")
        
        # Bước 1: Cho phép người dùng chọn cột status
        status_column = select_status_column(excel_file, sheet_name)
        if not status_column:
            output.print_md("No status column selected. Exiting Status Filter...")
            return
        
        # Bước 2: Đọc Excel data thành dictionary
        guid_status_map = read_excel_data(excel_file, sheet_name, status_column)
        if not guid_status_map:
            return
        
        # Bước 3: Thu thập tất cả elements có IFC GUID
        guid_element_map = get_all_elements_with_ifc_guid()
        if not guid_element_map:
            alert("No elements with IFC GUID found in model.", warn_icon=True)
            return
        
        # Bước 4: Tìm elements cần update (intersection)
        elements_to_update = []
        for ifc_guid, status in guid_status_map.items():
            if ifc_guid in guid_element_map:
                element = guid_element_map[ifc_guid]
                elements_to_update.append((element, status))
        
        output.print_md("**Found {} elements to update**".format(len(elements_to_update)))
        
        if not elements_to_update:
            alert("No matching elements found between Excel and Revit model.", warn_icon=True)
            return
        
        # Bước 5: Update với một transaction duy nhất
        with DB.Transaction(doc, "Update Comments Parameters") as trans:
            trans.Start()
            
            try:
                updated_count = 0
                failed_count = 0
                
                for element, status in elements_to_update:
                    if set_element_comment_parameter(element, status):
                        updated_count += 1
                        if updated_count <= 20:  # Hiển thị 20 update đầu tiên
                            output.print_md("✅ Updated Element ID {} with status: '{}'".format(element.Id.IntegerValue, status))
                    else:
                        failed_count += 1
                        if failed_count <= 10:
                            output.print_md("❌ Failed to update Element ID: {}".format(element.Id.IntegerValue))
                
                trans.Commit()
                
                # Tóm tắt kết quả
                output.print_md("## Status Filter Completed!")
                output.print_md("- **Excel entries:** {}".format(len(guid_status_map)))
                output.print_md("- **Model elements with IFC GUID:** {}".format(len(guid_element_map)))
                output.print_md("- **Matching elements:** {}".format(len(elements_to_update)))
                output.print_md("- **Successfully updated:** {}".format(updated_count))
                output.print_md("- **Failed to update:** {}".format(failed_count))
                
                if updated_count > 0:
                    alert("Successfully updated {} element Comments parameters!".format(updated_count))
                else:
                    alert("No elements were updated.", warn_icon=True)
                    
            except Exception as trans_error:
                trans.RollBack()
                output.print_md("❌ Transaction failed: {}".format(str(trans_error)))
                alert("Transaction failed: {}".format(str(trans_error)), warn_icon=True)
        
    except Exception as e:
        output.print_md("## ERROR in Status Filter:")
        output.print_md("**Error:** {}".format(str(e)))
        alert("Status Filter failed: {}".format(str(e)), warn_icon=True)


## 🎯 Workset Filter - New Implementation

### Workflow theo yêu cầu:


def get_or_create_worksets(workset_names):
    """Tạo hoặc lấy worksets từ danh sách tên"""
    # type: (list[str]) -> dict[str, str]
    
    try:
        output.print_md("## Creating/Getting Worksets...")
        
        if not doc.IsWorkshared:
            alert("This model is not workshared. Cannot create worksets.", warn_icon=True)
            return {}
        
        created_count = 0
        existing_count = 0
        result_worksets = {}
        
        with DB.Transaction(doc, "Create Worksets") as trans:
            trans.Start()
            
            try:
                for workset_name in workset_names:
                    try:
                        # Thử tạo workset mới
                        new_workset = DB.Workset.Create(doc, workset_name)
                        result_worksets[workset_name] = workset_name  # Lưu tên workset
                        created_count += 1
                        output.print_md("🆕 Created new workset: '{}'".format(workset_name))
                    except Exception as create_error:
                        # Kiểm tra lỗi do workset đã tồn tại
                        error_msg = str(create_error).lower()
                        if ("already" in error_msg and "use" in error_msg) or \
                           ("duplicate" in error_msg) or \
                           ("name is not unique" in error_msg) or \
                           ("already in use" in error_msg):
                            # Workset đã tồn tại - vẫn thêm vào result
                            result_worksets[workset_name] = workset_name
                            existing_count += 1
                            output.print_md("✅ Workset '{}' already exists".format(workset_name))
                        else:
                            # Lỗi khác - vẫn thêm vào result để tiếp tục
                            result_worksets[workset_name] = workset_name
                            output.print_md("⚠️ Workset '{}' - Error: {} (Will still try to use)".format(workset_name, str(create_error)))
                
                trans.Commit()
                
                output.print_md("**Workset Summary:**")
                output.print_md("- Created: {}".format(created_count))
                output.print_md("- Already existed: {}".format(existing_count))
                output.print_md("- Total available: {}".format(len(result_worksets)))
                
                return result_worksets
                
            except Exception as trans_error:
                trans.RollBack()
                output.print_md("❌ Transaction failed: {}".format(str(trans_error)))
                alert("Failed to create worksets: {}".format(str(trans_error)), warn_icon=True)
                return {}
        
    except Exception as e:
        output.print_md("ERROR in workset creation: {}".format(str(e)))
        return {}


def select_workset_list():
    """Bước 1: Cho phép người dùng chọn file chứa danh sách worksets"""
    # type: () -> list[str] | None
    
    try:
        output.print_md("## Step 1: Select Workset List File")
        
        # Chọn Excel file chứa danh sách worksets
        workset_file = pick_excel_file(title="Select Workset List File")
        if not workset_file:
            return None
        
        # Lấy danh sách sheets
        sheet_names = MiniExcel.GetSheetNames(workset_file)
        if not sheet_names or len(sheet_names) == 0:
            alert("No sheets found in workset file.", warn_icon=True)
            return None
        
        sheet_list = [str(sheet) for sheet in sheet_names]
        selected_sheet = wpfforms.CommandSwitchWindow.show(sheet_list, "Select Workset Sheet")
        if not selected_sheet:
            return None
        
        # Đọc danh sách worksets
        excel_data = MiniExcel.Query(workset_file, useHeaderRow=True, sheetName=selected_sheet)
        rows = list(excel_data)
        
        if not rows:
            alert("No data found in workset sheet.", warn_icon=True)
            return None
        
        # Tìm cột "Workset" chính xác
        first_row = rows[0]
        workset_column = None
        
        # Debug: In ra tất cả các cột có sẵn
        available_columns = []
        for attr in dir(first_row):
            if not attr.startswith('_'):
                available_columns.append(attr)
        
        output.print_md("Available columns: {}".format(", ".join(available_columns)))
        
        # Tìm cột "Workset" (ưu tiên tên chính xác)
        for attr in available_columns:
            if attr == "Workset":  # Tìm chính xác cột "Workset"
                workset_column = attr
                break
        
        # Nếu không tìm thấy "Workset", thử các tên khác
        if not workset_column:
            for attr in available_columns:
                attr_lower = attr.lower()
                if 'workset' in attr_lower or attr_lower in ['name', 'worksetname']:
                    workset_column = attr
                    break
        
        if not workset_column:
            alert("Could not find 'Workset' column.\nAvailable columns: {}\n\nPlease ensure your Excel has a column named 'Workset'.".format(", ".join(available_columns)), warn_icon=True)
            return None
        
        output.print_md("Using column: '{}'".format(workset_column))
        
        # Đọc danh sách workset names
        workset_names = []
        for i, row in enumerate(rows):
            try:
                workset_name = str(getattr(row, workset_column, "")).strip()
                if workset_name and workset_name not in workset_names:
                    workset_names.append(workset_name)
                    output.print_md("Row {}: Found workset name '{}'".format(i+1, workset_name))
            except Exception as row_error:
                output.print_md("Row {}: Error reading - {}".format(i+1, str(row_error)))
                continue
        
        output.print_md("Found {} unique workset names to create/check".format(len(workset_names)))
        for name in workset_names:
            output.print_md("- '{}'".format(name))
            
        return workset_names
        
    except Exception as e:
        output.print_md("ERROR selecting workset list: {}".format(str(e)))
        alert("Failed to read workset list: {}".format(str(e)), warn_icon=True)
        return None


def select_multiple_element_files():
    """Bước 2: Cho phép người dùng chọn nhiều Excel files chứa element data"""
    # type: () -> list[str] | None
    
    try:
        output.print_md("## Step 2: Select Element Data Files")
        
        import clr
        clr.AddReference("System.Windows.Forms")
        from System.Windows.Forms import OpenFileDialog, DialogResult
        from System.Windows.Forms import SelectionMode
        
        # Tạo OpenFileDialog với Multiselect = True
        dialog = OpenFileDialog()
        dialog.Title = "Select Element Data Files (Multiple Selection)"
        dialog.Filter = "Excel Files|*.xlsx;*.xls|All Files|*.*"
        dialog.Multiselect = True  # Cho phép chọn nhiều files
        dialog.RestoreDirectory = True
        
        if dialog.ShowDialog() == DialogResult.OK:
            selected_files = list(dialog.FileNames)  # Lấy tất cả files đã chọn
            
            if not selected_files:
                output.print_md("No element files selected.")
                return None
            
            output.print_md("**Total selected files: {}**".format(len(selected_files)))
            for i, file_path in enumerate(selected_files, 1):
                import os
                filename = os.path.basename(file_path)
                output.print_md("{}. {}".format(i, filename))
            
            return selected_files
        else:
            output.print_md("No element files selected.")
            return None
        
    except Exception as e:
        output.print_md("ERROR selecting element files: {}".format(str(e)))
        alert("Failed to select element files: {}".format(str(e)), warn_icon=True)
        return None


def read_element_data_from_files(file_paths):
    """Bước 3: Đọc IFC GUID từ nhiều Excel files (cột Item→GUID)"""
    # type: (list[str]) -> dict[str, list[str]] | None
    
    try:
        output.print_md("## Step 3: Reading Element Data from Files...")
        
        file_guid_map = {}  # {filename: [list_of_guids]}
        
        for file_path in file_paths:
            try:
                import os
                filename = os.path.basename(file_path).replace('.xlsx', '').replace('.xls', '')
                
                output.print_md("### Processing file: {}".format(filename))
                
                # Lấy danh sách sheets
                sheet_names = MiniExcel.GetSheetNames(file_path)
                if not sheet_names:
                    output.print_md("❌ No sheets found in {}".format(filename))
                    continue
                
                # Chọn sheet đầu tiên hoặc sheet có tên phù hợp
                sheet_list = [str(sheet) for sheet in sheet_names]
                selected_sheet = sheet_list[0]  # Mặc định chọn sheet đầu tiên
                
                # Đọc dữ liệu
                excel_data = MiniExcel.Query(file_path, useHeaderRow=True, sheetName=selected_sheet)
                rows = list(excel_data)
                
                if not rows:
                    output.print_md("❌ No data in {}".format(filename))
                    continue
                
                # Tìm cột "Item→GUID" hoặc tương tự
                first_row = rows[0]
                guid_column = None
                
                for attr in dir(first_row):
                    if not attr.startswith('_'):
                        attr_lower = attr.lower().replace('→', '').replace('->', '').replace(' ', '')
                        if 'itemguid' in attr_lower or 'item_guid' in attr_lower or attr_lower in ['guid', 'ifcguid', 'ifc_guid']:
                            guid_column = attr
                            break
                
                if not guid_column:
                    output.print_md("❌ Could not find GUID column in {}".format(filename))
                    continue
                
                # Đọc GUIDs
                guid_list = []
                for row in rows:
                    try:
                        guid_value = str(getattr(row, guid_column, "")).strip()
                        if guid_value and guid_value not in guid_list:
                            guid_list.append(guid_value)
                    except:
                        continue
                
                file_guid_map[filename] = guid_list
                output.print_md("✅ Found {} GUIDs in {}".format(len(guid_list), filename))
                
            except Exception as file_error:
                output.print_md("❌ Error processing {}: {}".format(file_path, str(file_error)))
                continue
        
        total_guids = sum(len(guids) for guids in file_guid_map.values())
        output.print_md("**Total GUIDs from all files: {}**".format(total_guids))
        
        return file_guid_map if file_guid_map else None
        
    except Exception as e:
        output.print_md("ERROR reading element data: {}".format(str(e)))
        return None


def assign_elements_by_file_mapping(file_guid_map, workset_map, guid_element_map):
    """Bước 4: Assign elements vào worksets dựa trên mapping filename → workset"""
    # type: (dict[str, list[str]], dict[str, str], dict[str, DB.Element]) -> dict[str, tuple[int, int]]
    
    try:
        output.print_md("## Step 4: Assigning Elements to Worksets...")
        
        assignment_results = {}  # {workset_name: (assigned_count, failed_count)}
        
        # Lấy tất cả worksets hiện có bằng cách mới - sử dụng WorksetTable trực tiếp
        available_worksets = {}
        try:
            workset_table = doc.GetWorksetTable()
            
            # Thử nhiều cách để lấy workset IDs
            output.print_md("Attempting to discover all worksets...")
            
            # Cách 1: Brute force với range lớn hơn
            for i in range(-10, 1000):  # Thử từ -10 đến 999
                try:
                    workset_id = DB.WorksetId(i)
                    workset = workset_table.GetWorkset(workset_id)
                    if workset and workset.Kind == DB.WorksetKind.UserWorkset:
                        available_worksets[workset.Name] = workset
                        output.print_md("Found workset: '{}' (ID: {})".format(workset.Name, i))
                except:
                    continue
            
            # Cách 2: Nếu vẫn không đủ, thử lấy từ elements
            if len(available_worksets) < len(workset_map):
                output.print_md("Brute force found {} worksets, need {}. Trying element-based discovery...".format(
                    len(available_worksets), len(workset_map)))
                
                # Lấy một sample lớn hơn từ elements
                collector = DB.FilteredElementCollector(doc).WhereElementIsNotElementType()
                found_workset_ids = set()
                
                for element in collector:
                    try:
                        workset_param = element.get_Parameter(DB.BuiltInParameter.ELEM_PARTITION_PARAM)
                        if workset_param and workset_param.HasValue:
                            workset_id_int = workset_param.AsInteger()
                            if workset_id_int not in found_workset_ids:
                                found_workset_ids.add(workset_id_int)
                                try:
                                    workset_id = DB.WorksetId(workset_id_int)
                                    workset = workset_table.GetWorkset(workset_id)
                                    if workset and workset.Kind == DB.WorksetKind.UserWorkset and workset.Name not in available_worksets:
                                        available_worksets[workset.Name] = workset
                                        output.print_md("Element-based found workset: '{}' (ID: {})".format(workset.Name, workset_id_int))
                                except:
                                    continue
                        
                        if len(found_workset_ids) > 50:  # Giới hạn để tránh quá lâu
                            break
                    except:
                        continue
                        
        except Exception as workset_error:
            output.print_md("Error discovering worksets: {}".format(str(workset_error)))
        
        output.print_md("Found {} total worksets: {}".format(
            len(available_worksets), list(available_worksets.keys())))
        
        # Nếu vẫn không tìm thấy đủ worksets, thử cách cuối cùng
        if len(available_worksets) < len(workset_map):
            output.print_md("Still missing worksets. Trying to create workset objects on-the-fly...")
            
            # Thử tạo workset objects trực tiếp từ tên
            missing_worksets = []
            for workset_name in workset_map.keys():
                if workset_name not in available_worksets:
                    missing_worksets.append(workset_name)
            
            output.print_md("Missing worksets: {}".format(missing_worksets))
        
        with DB.Transaction(doc, "Assign Elements to Worksets") as trans:
            trans.Start()
            
            try:
                for filename, guid_list in file_guid_map.items():
                    output.print_md("### Processing file: {}".format(filename))
                    
                    # Tìm workset tương ứng với filename
                    target_workset = None
                    target_workset_name = None
                    
                    # Matching logic
                    best_match = None
                    best_match_length = 0
                    
                    for workset_name in workset_map.keys():
                        filename_clean = filename.replace('O1-ARC_Existing_', '').replace('.xlsx', '').replace('.xls', '')
                        workset_clean = workset_name.replace('000_LinkIFC_O1-ARC_Existing_', '')
                        
                        if filename_clean.lower() == workset_clean.lower():
                            best_match = workset_name
                            break
                        elif filename_clean.lower() in workset_clean.lower() or workset_clean.lower() in filename_clean.lower():
                            if len(workset_name) > best_match_length:
                                best_match = workset_name
                                best_match_length = len(workset_name)
                    
                    if best_match:
                        target_workset_name = best_match
                        output.print_md("✅ Matched file '{}' with workset '{}'".format(filename, target_workset_name))
                        
                        # Tìm workset object
                        if target_workset_name in available_worksets:
                            target_workset = available_worksets[target_workset_name]
                            output.print_md("✅ Found workset object for '{}'".format(target_workset_name))
                        else:
                            # Thử tìm workset bằng cách iterate qua tất cả workset IDs có thể
                            output.print_md("Workset object not in cache, searching by name...")
                            workset_table = doc.GetWorksetTable()
                            
                            for test_id in range(-50, 2000):  # Range rất lớn
                                try:
                                    test_workset = workset_table.GetWorkset(DB.WorksetId(test_id))
                                    if test_workset and test_workset.Name == target_workset_name:
                                        target_workset = test_workset
                                        output.print_md("✅ Found workset '{}' with ID {}".format(target_workset_name, test_id))
                                        break
                                except:
                                    continue
                    else:
                        output.print_md("❌ No matching workset found for file: {}".format(filename))
                        continue
                    
                    if not target_workset:
                        output.print_md("❌ Could not find workset object for '{}' even with extensive search".format(target_workset_name))
                        continue
                    
                    output.print_md("### Assigning {} elements to workset '{}'".format(len(guid_list), target_workset.Name))
                    
                    assigned_count = 0
                    failed_count = 0
                    matching_elements = 0
                    
                    # Đếm matching elements
                    for guid in guid_list:
                        if guid in guid_element_map:
                            matching_elements += 1
                    
                    output.print_md("Found {} matching elements out of {} GUIDs".format(matching_elements, len(guid_list)))
                    
                    for guid in guid_list:
                        if guid in guid_element_map:
                            element = guid_element_map[guid]
                            try:
                                if element and element.IsValidObject:
                                    workset_param = element.get_Parameter(DB.BuiltInParameter.ELEM_PARTITION_PARAM)
                                    if workset_param and not workset_param.IsReadOnly:
                                        old_workset_id = workset_param.AsInteger()
                                        workset_param.Set(target_workset.Id.IntegerValue)
                                        assigned_count += 1
                                        
                                        if assigned_count <= 3:  # Hiển thị 3 assignment đầu tiên
                                            output.print_md("✅ Element ID {} → '{}' (was workset ID: {})".format(
                                                element.Id.IntegerValue, target_workset.Name, old_workset_id))
                                    else:
                                        failed_count += 1
                                        if failed_count <= 3:
                                            output.print_md("❌ Element ID {} - workset parameter is read-only".format(element.Id.IntegerValue))
                                else:
                                    failed_count += 1
                            except Exception as assign_error:
                                failed_count += 1
                                if failed_count <= 3:
                                    output.print_md("❌ Assignment error for element {}: {}".format(element.Id.IntegerValue, str(assign_error)))
                    
                    assignment_results[target_workset.Name] = (assigned_count, failed_count)
                    output.print_md("**File '{}' → Workset '{}': {} assigned, {} failed**".format(
                        filename, target_workset.Name, assigned_count, failed_count))
                
                trans.Commit()
                return assignment_results
                
            except Exception as trans_error:
                trans.RollBack()
                output.print_md("❌ Transaction failed: {}".format(str(trans_error)))
                alert("Assignment transaction failed: {}".format(str(trans_error)), warn_icon=True)
                return {}
        
    except Exception as e:
        output.print_md("ERROR in assignment: {}".format(str(e)))
        return {}


def process_workset_filter():
    """Xử lý Workset Filter theo workflow mới"""
    # type: () -> None
    
    try:
        output.print_md("## Processing Workset Filter - New Workflow")
        
        # Kiểm tra model có workshared không
        if not doc.IsWorkshared:
            alert("This model is not workshared.\nWorkset Filter requires a workshared model.", warn_icon=True)
            return
        
        # Bước 1: Chọn workset list và tạo worksets
        workset_names = select_workset_list()
        if not workset_names:
            output.print_md("No workset list selected. Exiting...")
            return
        
        workset_map = get_or_create_worksets(workset_names)
        if not workset_map:
            return
        
        # Bước 2: Chọn nhiều element data files
        element_files = select_multiple_element_files()
        if not element_files:
            output.print_md("No element files selected. Exiting...")
            return
        
        # Bước 3: Đọc element data từ các files
        file_guid_map = read_element_data_from_files(element_files)
        if not file_guid_map:
            alert("No element data found in selected files.", warn_icon=True)
            return
        
        # Thu thập tất cả elements có IFC GUID từ model
        output.print_md("## Collecting elements from Revit model...")
        guid_element_map = get_all_elements_with_ifc_guid()
        if not guid_element_map:
            alert("No elements with IFC GUID found in model.", warn_icon=True)
            return
        
        # Bước 4: Assign elements theo file mapping
        assignment_results = assign_elements_by_file_mapping(file_guid_map, workset_map, guid_element_map)
        
        # Tóm tắt kết quả
        output.print_md("## Workset Filter Completed!")
        output.print_md("### Summary:")
        
        total_assigned = 0
        total_failed = 0
        
        for workset_name, (assigned, failed) in assignment_results.items():
            total_assigned += assigned
            total_failed += failed
            output.print_md("- **{}**: {} assigned, {} failed".format(workset_name, assigned, failed))
        
        output.print_md("### Overall Results:")
        output.print_md("- **Total worksets processed:** {}".format(len(assignment_results)))
        output.print_md("- **Total elements assigned:** {}".format(total_assigned))
        output.print_md("- **Total assignment failures:** {}".format(total_failed))
        
        if total_assigned > 0:
            alert("Successfully assigned {} elements to worksets!".format(total_assigned))
        else:
            alert("No elements were assigned to worksets.", warn_icon=True)
        
    except Exception as e:
        output.print_md("## ERROR in Workset Filter:")
        output.print_md("**Error:** {}".format(str(e)))
        alert("Workset Filter failed: {}".format(str(e)), warn_icon=True)


def main():
    """Main function - điều khiển workflow chính"""
    try:
        output.print_md("## FilterARC Tool - Updated Workflow")
        
        # Bước 1: Chọn loại công việc
        selected_work = select_work_type()
        if not selected_work:
            output.print_md("No work type selected. Exiting...")
            return
        
        output.print_md("Selected work type: {}".format(selected_work))
        
        # Bước 2: Xử lý theo loại công việc được chọn
        if selected_work == "Workset Filter":
            process_workset_filter()
        elif selected_work == "Status Filter":
            # Bước 3: Chọn Excel file và sheet
            excel_selection = select_excel_file_and_sheet()
            if not excel_selection:
                output.print_md("No Excel file selected. Exiting...")
                return
            
            excel_file, sheet_name = excel_selection
            output.print_md("Selected file: {}".format(excel_file))
            output.print_md("Selected sheet: {}".format(sheet_name))
            
            # Bước 4-5: Xử lý Status Filter
            process_status_filter(excel_file, sheet_name)
        
        output.print_md("## Script completed!")
        
    except Exception as main_error:
        output.print_md("## CRITICAL ERROR in main function:")
        output.print_md("**Error:** {}".format(str(main_error)))
        
        import traceback
        error_details = traceback.format_exc()
        output.print_md("**Full traceback:**")
        output.print_md("```")
        output.print_md(error_details)
        output.print_md("```")
        
        alert("Script failed with error: {}".format(str(main_error)), warn_icon=True)


if __name__ == "__main__":
    main()


