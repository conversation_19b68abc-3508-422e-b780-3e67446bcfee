# DCMvn Service Architecture Generator

You are an expert in DCMvn framework architecture. Generate service layer components following established patterns and best practices.

## Context and Reference Files
Use these files as templates and patterns:
- [Service Patterns](#file:../.cursor/rules/services/service-patterns.mdc)
- [Collector Service](#file:../.cursor/rules/services/collector-service-pattern.py)
- [Model Patterns](#file:../.cursor/rules/models/model-patterns.mdc)
- [Element Wrapper](#file:../.cursor/rules/models/element-wrapper-pattern.py)

## Service Layer Architecture

### Base Service Pattern
All services must inherit from this pattern:
```python
class BaseService(object):
    def __init__(self, document):
        self.document = document
        self._cache = {}
    
    def clear_cache(self):
        self._cache.clear()
```

### Required Service Types

#### 1. CollectorService
- Element collection and filtering
- Category-based queries
- Physical element filtering
- Caching for performance
- Link document support

#### 2. DetectionService
- Spatial relationship calculations
- Intersection detection
- Distance measurements
- Geometric analysis
- Tolerance handling

#### 3. ReportService
- Data aggregation and processing
- Excel export using MiniExcel
- Report generation
- Data validation
- File management

### Implementation Requirements

1. **Caching Strategy**
   - Use `_cache` dictionary for expensive operations
   - Implement cache keys based on operation parameters
   - Provide cache management methods
   - Consider memory usage for large datasets

2. **Error Handling**
   - Validate input parameters
   - Use try-catch blocks around Revit API calls
   - Log errors using `Debug.WriteLine()`
   - Return meaningful error indicators

3. **Performance Optimization**
   - Use LINQ extensions for filtering
   - Implement lazy loading where appropriate
   - Cache geometry calculations
   - Batch operations when possible

4. **Integration Patterns**
   - Accept document reference in constructor
   - Coordinate with other services
   - Support both host and linked documents
   - Handle element validation

## Service Generation Requirements

Generate complete service classes with:
- Proper inheritance from BaseService
- Constructor with document parameter
- Core business logic methods
- Caching implementation
- Error handling
- Comprehensive documentation
- Unit test considerations

## Quality Standards
- Follow DCMvn naming conventions
- Implement defensive programming
- Use appropriate data structures
- Consider thread safety
- Provide clear method signatures
- Include performance considerations
