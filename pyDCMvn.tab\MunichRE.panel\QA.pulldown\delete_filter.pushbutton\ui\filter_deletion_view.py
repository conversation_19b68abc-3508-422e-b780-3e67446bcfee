# coding: utf-8
import os
from DCMvn.forms.wpfforms import WPFWindow


class FilterDeletionView(WPFWindow):
    """WPF View cho công cụ xóa filter"""
    
    def __init__(self, view_model):
        xaml_path = os.path.join(os.path.dirname(__file__), "filter_deletion.xaml")
        WPFWindow.__init__(self, xaml_path)
        
        self.DataContext = view_model
        self.view_model = view_model
        
        # Set window properties
        self.Title = "Xóa Filter trong Revit"
        self.Width = 600
        self.Height = 500
        self.WindowStartupLocation = self.WindowStartupLocation.CenterOwner
        
        # Setup cleanup
        self.view_model.owner_window = self
        self.Closing += self._on_window_closing
    
    def _on_window_closing(self, sender, e):
        """Cleanup khi đóng window"""
        try:
            if hasattr(self.view_model, 'owner_window'):
                self.view_model.owner_window = None
            if hasattr(self.view_model, 'cleanup'):
                self.view_model.cleanup()
        except Exception as ex:
            from DCMvn.core.framework import Debug
            Debug.WriteLine("Window cleanup error: {}".format(str(ex)))
    
    def OnCloseClick(self, sender, e):
        """Xử lý nút Đóng"""
        self.Close()
