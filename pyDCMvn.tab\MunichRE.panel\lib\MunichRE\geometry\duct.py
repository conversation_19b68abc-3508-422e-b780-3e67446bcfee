# coding: utf-8
import ast
from DCMvn.core import HOST_APP
from DCMvn.revit.geometry import is_vertical, is_horizontal, ProjectToPlane

from . import *
from MunichRE.constants import *

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)


class DuctSegment(MEPCurveGeometry):
    def __init__(self, expando_object):
        """
        Duct Segment
        Args:
            expando_object: ExpandedObject
        """
        MEPCurveGeometry.__init__(self, expando_object)

    @property
    def length(self):
        # type: () -> float
        if isinstance(DUCT_LENGTH, list):
            length = DuctSegment.get_value(param_list=DUCT_LENGTH, expando_object=self._object,
                                           error_message="Length is not defined")
        else:
            length = getattr(self._object, DUCT_LENGTH)
        if not length:
            raise Exception("Length is not defined")
        if isinstance(length, str):
            try:
                length = ast.literal_eval(length)[0]
            except ValueError:
                raise ValueError("Invalid Length Value")
        return DB.UnitUtils.ConvertToInternalUnits(length, DB.UnitTypeId.Millimeters)

    @property
    def shape(self):
        value = getattr(self._object, DUCT_SHAPE)
        if value:
            return ast.literal_eval(value)[0]
        return None

    @property
    def width(self):
        # type: () -> float
        if isinstance(DUCT_WIDTH, list):
            width = DuctSegment.get_value(param_list=DUCT_WIDTH, expando_object=self._object,
                                          error_message="Width is not defined")
        else:
            width = getattr(self._object, DUCT_WIDTH)
        if not width:
            raise Exception("Width is not defined")
        if isinstance(width, str):
            try:
                width = ast.literal_eval(width)[0]
            except ValueError:
                raise ValueError("Invalid Width Value")
        return DB.UnitUtils.ConvertToInternalUnits(width, DB.UnitTypeId.Millimeters)

    @property
    def height(self):
        # type: () -> float
        if self.shape == ROUND_SHAPE:
            return self.width

        if isinstance(DUCT_HEIGHT, list):
            height = DuctSegment.get_value(param_list=DUCT_HEIGHT, expando_object=self._object,
                                           error_message="Height is not defined")
        else:
            height = getattr(self._object, DUCT_HEIGHT)
        if not height:
            raise Exception("Height is not defined")
        if isinstance(height, str):
            try:
                height = ast.literal_eval(height)[0]
            except ValueError:
                raise ValueError("Invalid Height Value")
        return DB.UnitUtils.ConvertToInternalUnits(height, DB.UnitTypeId.Millimeters)

    def _get_max_edge(self, side_edges):
        # type: (list) -> tuple
        """Get the largest edge as the width line"""
        if self.width > self.height:
            width_line = max(side_edges, key=lambda x: x.Length)
            width_line_points = width_line.Tessellate().OrderBy(lambda x: x.X).ThenBy(lambda x: x.Y).ThenBy(
                lambda x: x.Z).ToList()
            width_line_direction = (width_line_points[1] - width_line_points[0]).Normalize()
        else:
            width_line = min(side_edges, key=lambda x: x.Length)
            width_line_points = width_line.Tessellate().OrderBy(lambda x: x.X).ThenBy(lambda x: x.Y).ThenBy(
                lambda x: x.Z).ToList()
            width_line_direction = (width_line_points[0] - width_line_points[1]).Normalize()

        return width_line, width_line_direction

    def _get_vertical_duct_dimensions(self, side_edges):
        # type: (list) -> tuple
        """Vertical Duct"""
        width_line, width_line_direction = self._get_max_edge(side_edges)

        width_value = self.width
        height_value = self.height

        # Get Angle
        reference_axis = DB.XYZ.BasisX
        angle = width_line_direction.AngleTo(reference_axis)
        cross = reference_axis.CrossProduct(width_line_direction)

        # If cross.Z < 0, angle should be negative (quadrant 1 and 4)
        if cross.Z < 0:
            angle = -angle

        return width_value, height_value, angle

    @staticmethod
    def _get_horizontal_duct_dimensions(side_edges):
        # type: (list) -> tuple
        """Non-vertical and non-twisted"""
        width_line = next((e for e in side_edges if is_horizontal(e.Direction, tolerance=0.01)), None)
        height_line = next((e for e in side_edges if e != width_line), None)
        angle = 0

        if width_line is None or height_line is None:
            raise ValueError("Cannot find width and height line")

        return width_line.Length, height_line.Length, angle

    def _get_twisted_duct_dimensions(self, side_edges, start_point, end_point):
        # type: (list, DB.XYZ, DB.XYZ) -> tuple
        """Non-vertical and twisted"""
        width_value = max(self.width, self.height) if self.width > self.height else min(self.width, self.height)
        height_value = min(self.width, self.height) if self.width > self.height else max(self.width, self.height)

        width_line, width_line_direction = self._get_max_edge(side_edges)

        # project duct direction to plane
        project_plane = DB.Plane.CreateByNormalAndOrigin(DB.XYZ.BasisZ, start_point)
        start_point_projected = ProjectToPlane(start_point, project_plane)
        end_point_projected = ProjectToPlane(end_point, project_plane)
        duct_direction = (end_point_projected - start_point_projected).Normalize()
        reference_axis = duct_direction.CrossProduct((end_point - start_point).Normalize())

        # Get Angle
        angle = width_line_direction.AngleTo(reference_axis)
        cross = reference_axis.CrossProduct(width_line_direction)
        if cross.Z < 0:
            angle = -angle

        return width_value, height_value, angle

    def _get_duct_dimensions(self, side_edges, start_point, end_point):
        is_vertical_duct = is_vertical((end_point - start_point).Normalize())

        if is_vertical_duct:
            return self._get_vertical_duct_dimensions(side_edges)
        else:
            try:
                return self._get_horizontal_duct_dimensions(side_edges)
            except ValueError:
                return self._get_twisted_duct_dimensions(side_edges, start_point, end_point)

    def create_duct(self, system_type_id, duct_type_id, level_id):
        cross_points, direction_edge, side_edges = self._get_side_edges(self.length)
        start_point, end_point = self._get_points(self.length, cross_points, direction_edge)

        duct_ = DB.Mechanical.Duct.Create(HOST_APP.doc, system_type_id, duct_type_id, level_id,
                                          start_point, end_point)  # type: DB.Mechanical.Duct

        if self.shape == RECTANGULAR_SHAPE:
            width, height, rotate_angle = self._get_duct_dimensions(side_edges, start_point, end_point)
            duct_.get_Parameter(DB.BuiltInParameter.RBS_CURVE_WIDTH_PARAM).Set(width)
            duct_.get_Parameter(DB.BuiltInParameter.RBS_CURVE_HEIGHT_PARAM).Set(height)
            DB.ElementTransformUtils.RotateElement(HOST_APP.doc, duct_.Id,
                                                   DB.Line.CreateBound(start_point, end_point), rotate_angle)
        elif self.shape == ROUND_SHAPE:
            duct_.get_Parameter(DB.BuiltInParameter.RBS_CURVE_DIAMETER_PARAM).Set(self.width)
        duct_.get_Parameter(DB.BuiltInParameter.IFC_GUID).Set(self.id)
