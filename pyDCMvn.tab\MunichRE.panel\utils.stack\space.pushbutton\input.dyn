{"Uuid": "438dde9e-2327-41a6-8a4f-c0e1578bf7f2", "IsCustomNode": false, "Description": "", "Name": "input", "ElementResolver": {"ResolutionMap": {}}, "Inputs": [], "Outputs": [], "Nodes": [{"ConcreteType": "CoreNodeModels.Input.BoolSelector, CoreNodeModels", "Id": "6a416ca0432b4221b0f337b9b2076fb9", "NodeType": "BooleanInputNode", "Inputs": [], "Outputs": [{"Id": "c0f17bc8a66d4f21bc52b2c0b47fd7ed", "Name": "", "Description": "Boolean", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Enables selection between True and False", "InputValue": true}, {"ConcreteType": "PythonNodeModels.PythonStringNode, PythonNodeModels", "Engine": "IronPython3", "VariableInputPorts": true, "Id": "bcdc159a4b6c4271911e39570ced65f0", "NodeType": "ExtensionNode", "Inputs": [{"Id": "4ff753c8a0374d83903a1d7e1187cca7", "Name": "script", "Description": "Python script to run.", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "f2bf81ef7feb4d8a9c274e8b50550d6e", "Name": "IN[0]", "Description": "Input #0", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "3f582892e309483492912bd4dc0741c6", "Name": "IN[1]", "Description": "Input #1", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "15ed5a506ea94a45a2e45ae59e020721", "Name": "OUT", "Description": "Result of the python script", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Runs a Python script from a string."}, {"ConcreteType": "PythonNodeModels.PythonNode, PythonNodeModels", "Code": "import clr\r\n\r\nclr.AddReference('DynamoServices')  # noqa\r\nfrom Dynamo.Events import ExecutionEvents  # noqa\r\nfrom System import IO, Environment\r\n\r\n\r\ndef internal_lib(start_path):\r\n    lib_folders = []\r\n    current_path = start_path\r\n    while current_path:\r\n        _lib_path = IO.Path.Combine(current_path, \"lib\")\r\n        if IO.Directory.Exists(_lib_path):\r\n            lib_folders.append(_lib_path)\r\n        if \".extension\" in IO.Directory.GetDirectories(current_path):\r\n            break\r\n        current_path = IO.Path.GetDirectoryName(current_path)\r\n    return lib_folders\r\n\r\n\r\n# Paths\r\ncurrent_dynamo_path = ExecutionEvents.ActiveSession.CurrentWorkspacePath\r\ncurrent_workspace = IO.Path.GetDirectoryName(current_dynamo_path)\r\nappdata = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)\r\npydcmvn_lib = IO.Path.Combine(appdata, \"pyRevit\", \"Extensions\", \"pyDCMvn.lib\")\r\npyrevit_site_packages = IO.Path.Combine(appdata, \"pyRevit-Master\", \"site-packages\")\r\n\r\nlib_paths = internal_lib(current_workspace)\r\nlib_paths.append(pydcmvn_lib) if IO.Directory.Exists(pydcmvn_lib) else None\r\nlib_paths.append(pyrevit_site_packages) if IO.Directory.Exists(pyrevit_site_packages) else None\r\n\r\n# Python file end with script.py\r\npython_file = None\r\nfor file_ in IO.Directory.GetFiles(current_workspace):\r\n    if file_.endswith(\"script.py\"):\r\n        python_file = file_\r\n        break\r\n\r\n# Python File Content\r\npython_content = IO.File.ReadAllText(python_file)\r\n\r\nOUT = python_content, lib_paths, current_workspace\r\n", "Engine": "CPython3", "VariableInputPorts": true, "Id": "5b2064176ce14a07b09f697218bafd51", "NodeType": "PythonScriptNode", "Inputs": [{"Id": "d8775e77b7e74a5888ef408eb0259564", "Name": "IN[0]", "Description": "Input #0", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "9b80a062f3d74a15a67d0a23279513c3", "Name": "OUT", "Description": "Result of the python script", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Runs an embedded Python script."}, {"ConcreteType": "Dynamo.Graph.Nodes.CodeBlockNodeModel, DynamoCore", "Id": "9f22c350a52e48099c878295485072f7", "NodeType": "CodeBlockNode", "Inputs": [{"Id": "10f12979633843a48936f3b3ad54fc01", "Name": "t4", "Description": "t4", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "4d5e5f611c3b420ba0b7b7adb18be08e", "Name": "", "Description": "input", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "30b9b7cf9378439cb7e76420ae94e398", "Name": "", "Description": "script_content", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "d29d49e9e7f2415ca6ae267acb0df4b7", "Name": "", "Description": "lib_paths", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "4804e976abd2437988821e20b9cf9212", "Name": "", "Description": "current_workspace", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Allows for DesignScript code to be authored directly", "Code": "input = t4;\nscript_content = input[0];\nlib_paths = input[1];\ncurrent_workspace = input[2];"}, {"ConcreteType": "Dynamo.Graph.Nodes.CodeBlockNodeModel, DynamoCore", "Id": "d194912970684e37a7763c540c15c0c9", "NodeType": "CodeBlockNode", "Inputs": [{"Id": "808c7e9e9c204077a884d69beef90416", "Name": "a", "Description": "a", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "87fa9f29cc9640d39ab2008c204f0e8c", "Name": "", "Description": "Value of expression at line 1", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Allows for DesignScript code to be authored directly", "Code": "a[0];"}, {"ConcreteType": "Dynamo.Graph.Nodes.ZeroTouch.DSFunction, DynamoCore", "Id": "399bdcbb348f4feea35c14a5ec6baacd", "NodeType": "FunctionNode", "Inputs": [{"Id": "78657b1a74664231bc1be760d80a790c", "Name": "vector", "Description": "Autodesk.DesignScript.Geometry.Vector", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "9fdc32ff54f145638badc851ea6e27c8", "Name": "vec", "Description": "a second vector\n\nVector", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "dbb7674638aa43ab956d2269b9a22fba", "Name": "double", "Description": "Resulting dot product", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "FunctionSignature": "<EMAIL>", "Replication": "Auto", "Description": "Form the dot product of two vectors\n\nVector.Dot (vec: Vector): double"}, {"ConcreteType": "DSRevitNodesUI.ElementsOfCategory, DSRevitNodesUI", "Id": "c46f37299471423b9945677c0592dab2", "NodeType": "ExtensionNode", "Inputs": [{"Id": "868954edb4cf4c52a996389a5e3769c1", "Name": "Category", "Description": "The Category", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "10edfe7e638b486aa26cc915cebf6926", "Name": "Elements", "Description": "An element class.", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Get all elements of the specified category from the model."}, {"ConcreteType": "DSRevitNodesUI.Categories, DSRevitNodesUI", "SelectedIndex": 547, "SelectedString": "OST_MEPSpaces", "Id": "1b98c6f8230840668c29978c3b1f6337", "NodeType": "ExtensionNode", "Inputs": [], "Outputs": [{"Id": "ee7720cdba7146268c6f7802374840ad", "Name": "Category", "Description": "The selected Category.", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "All built-in categories."}, {"ConcreteType": "Dynamo.Graph.Nodes.ZeroTouch.DSFunction, DynamoCore", "Id": "e905caa45f6c4050bce4c9c5f14a18dc", "NodeType": "FunctionNode", "Inputs": [{"Id": "dbee41c255ad4521b1f5971095cb7de2", "Name": "element", "Description": "Revit.Elements.Element", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "5610f97fa3df4f2cbe694b4b9e02c47e", "Name": "Solid[]", "Description": "Solid[]", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "FunctionSignature": "Revit.Elements.Element.Solids", "Replication": "Auto", "Description": "The Solids in this Element\n\nElement.Solids: Solid[]"}], "Connectors": [{"Start": "c0f17bc8a66d4f21bc52b2c0b47fd7ed", "End": "d8775e77b7e74a5888ef408eb0259564", "Id": "18e1531ca04044179491fb3360cd1e56", "IsHidden": "False"}, {"Start": "15ed5a506ea94a45a2e45ae59e020721", "End": "808c7e9e9c204077a884d69beef90416", "Id": "dd137c04a77044049bdb18104cd7c81b", "IsHidden": "False"}, {"Start": "9b80a062f3d74a15a67d0a23279513c3", "End": "10f12979633843a48936f3b3ad54fc01", "Id": "530ba9910a6946d8b8b4a308d9d8e1c0", "IsHidden": "False"}, {"Start": "30b9b7cf9378439cb7e76420ae94e398", "End": "4ff753c8a0374d83903a1d7e1187cca7", "Id": "660ac6331967404b81132c512d13564d", "IsHidden": "False"}, {"Start": "d29d49e9e7f2415ca6ae267acb0df4b7", "End": "f2bf81ef7feb4d8a9c274e8b50550d6e", "Id": "d4317448b0704edd9d6a026099dfd4ea", "IsHidden": "False"}, {"Start": "4804e976abd2437988821e20b9cf9212", "End": "3f582892e309483492912bd4dc0741c6", "Id": "525ab9929bc04c5fb0898bafa0a653c8", "IsHidden": "False"}, {"Start": "10edfe7e638b486aa26cc915cebf6926", "End": "dbee41c255ad4521b1f5971095cb7de2", "Id": "89309d0482b848f4bdec5bb6ca3e26e7", "IsHidden": "False"}, {"Start": "ee7720cdba7146268c6f7802374840ad", "End": "868954edb4cf4c52a996389a5e3769c1", "Id": "0af17a54fbc64ec8a8ed36e1d5375c69", "IsHidden": "False"}], "Dependencies": [], "NodeLibraryDependencies": [{"Name": "DynamoIronPython3", "Version": "1.5.1", "ReferenceType": "Package", "Nodes": ["bcdc159a4b6c4271911e39570ced65f0"]}], "EnableLegacyPolyCurveBehavior": true, "Thumbnail": "", "GraphDocumentationURL": null, "ExtensionWorkspaceData": [{"ExtensionGuid": "28992e1d-abb9-417f-8b1b-05e053bee670", "Name": "Properties", "Version": "2.12", "Data": {}}, {"ExtensionGuid": "DFBD9CC0-DB40-457A-939E-8C8555555A9D", "Name": "Generative Design", "Version": "1.10", "Data": {}}], "Author": "", "Linting": {"activeLinter": "None", "activeLinterId": "7b75fb44-43fd-4631-a878-29f4d5d8399a", "warningCount": 0, "errorCount": 0}, "Bindings": [], "View": {"Dynamo": {"ScaleFactor": 1.0, "HasRunWithoutCrash": true, "IsVisibleInDynamoLibrary": true, "Version": "3.3.0.6316", "RunType": "Automatic", "RunPeriod": "1000"}, "Camera": {"Name": "_Background Preview", "EyeX": 210314.28125, "EyeY": 102387.0, "EyeZ": -23643.92578125, "LookX": -1722.4375, "LookY": -109395.5703125, "LookZ": 3147.44921875, "UpX": 0.4798021912574768, "UpY": -0.03279278427362442, "UpZ": -0.8767635822296143}, "ConnectorPins": [], "NodeViews": [{"Id": "6a416ca0432b4221b0f337b9b2076fb9", "Name": "Boolean", "IsSetAsInput": false, "IsSetAsOutput": false, "Excluded": false, "ShowGeometry": true, "X": 1204.2155910410884, "Y": 629.6536248447326}, {"Id": "bcdc159a4b6c4271911e39570ced65f0", "Name": "Python Script From String", "IsSetAsInput": false, "IsSetAsOutput": false, "Excluded": true, "ShowGeometry": false, "X": 1202.767790987816, "Y": 755.8215507741447}, {"Id": "5b2064176ce14a07b09f697218bafd51", "Name": "Content", "IsSetAsInput": false, "IsSetAsOutput": false, "Excluded": false, "ShowGeometry": true, "X": 648.3732594608178, "Y": 766.8084113064322}, {"Id": "9f22c350a52e48099c878295485072f7", "Name": "Code Block", "IsSetAsInput": false, "IsSetAsOutput": false, "Excluded": false, "ShowGeometry": true, "X": 828.0292018206035, "Y": 754.5577782383311}, {"Id": "d194912970684e37a7763c540c15c0c9", "Name": "Code Block", "IsSetAsInput": false, "IsSetAsOutput": false, "Excluded": false, "ShowGeometry": true, "X": 1501.0, "Y": 824.0}, {"Id": "399bdcbb348f4feea35c14a5ec6baacd", "Name": "Vector.Dot", "IsSetAsInput": false, "IsSetAsOutput": false, "Excluded": false, "ShowGeometry": true, "X": 1873.1935632572754, "Y": 405.0947288944061}, {"Id": "c46f37299471423b9945677c0592dab2", "Name": "All Elements of Category", "IsSetAsInput": false, "IsSetAsOutput": false, "Excluded": false, "ShowGeometry": true, "X": 1186.9972247923797, "Y": 1295.9796681487142}, {"Id": "1b98c6f8230840668c29978c3b1f6337", "Name": "Categories", "IsSetAsInput": false, "IsSetAsOutput": false, "Excluded": false, "ShowGeometry": true, "X": 996.4255422275453, "Y": 1096.3331435569823}, {"Id": "e905caa45f6c4050bce4c9c5f14a18dc", "Name": "Element.Solids", "IsSetAsInput": false, "IsSetAsOutput": false, "Excluded": false, "ShowGeometry": true, "X": 1483.4420643376784, "Y": 1345.8912992966468}], "Annotations": [], "X": -307.3161792434631, "Y": -113.84285380209155, "Zoom": 0.5013045592491331}}