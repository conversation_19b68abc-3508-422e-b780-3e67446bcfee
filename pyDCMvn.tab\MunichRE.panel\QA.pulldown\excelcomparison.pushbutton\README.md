# Excel Comparison and Merging Tool

## Overview
This pyRevit script performs Excel file comparison and merging based on GUID matching, with enhanced data preservation for Approver and Reason columns. The tool treats the first file as the "old" version (baseline) and the second file as the "new" version (updated).

## Functionality

### Step 1: File Selection
- Prompts user to select the old Excel file (baseline) using a file dialog
- Prompts user to select the new Excel file (updated version) using a file dialog
- Allows sheet selection if multiple sheets exist

### Step 2: Data Reading and Processing
- Uses MiniExcel library to read both Excel files
- Loads the data into appropriate data structures for comparison
- Automatically detects GUID columns (looks for: Guid, ID, GlobalId, Global_Id)
- Automatically detects Approver and Reason columns in both files

### Step 3: GUID Comparison and Data Merging
- Identifies all unique GUIDs from both the old and new Excel files
- Creates merged records that preserve all unique GUIDs from both files
- Implements data preservation priority for Approver and Reason columns:
  - New file values take priority over old file values
  - Old file values are preserved when new file values are empty/null
  - No data loss occurs during the merging process

### Step 4: Enhanced Output Generation
- Creates a comprehensive Excel file containing ALL unique GUIDs from both files
- Preserves existing Approver and Reason values rather than overwriting them
- Maintains audit trail information by prioritizing non-empty values
- Ensures no critical data is lost during the comparison and export process

## Technical Features
- Follows pyRevit script structure and conventions
- Includes proper error handling for file operations
- Provides user feedback during processing
- Handles cases where columns might not exist or files might be empty
- Uses appropriate data types for GUID comparison (string comparison)
- Case-insensitive column detection
- Supports various column name variations
- Correctly handles MiniExcel ExpandoObject data structures using IDictionary casting
- Robust column name extraction using dir() introspection to discover all available properties
- Dynamic column detection that works with any Excel file structure
- Two-pass data processing: discovery phase and extraction phase

## Column Detection
The script automatically detects columns using case-insensitive matching:

**GUID Columns:**
- Guid, ID, GlobalId, Global_Id

**Approver Columns:**
- Approver, Approved_By, ApprovedBy

**Reason Columns:**
- Reason, Comment, Remarks, Note

## Data Preservation Features
- **All Unique GUIDs**: Preserves all unique GUIDs from both old and new files
- **Approver Data**: Maintains existing Approver information with priority given to new file values
- **Reason Data**: Preserves Reason information with priority given to new file values
- **No Data Loss**: Ensures critical audit trail information is never lost during processing
- **Smart Merging**: Combines data intelligently, avoiding overwriting of valuable existing data

## Usage
1. Click the "Excel Comparison" button in the QA pulldown menu
2. Select the old Excel file (baseline) for comparison
3. Choose the appropriate sheet (if multiple sheets exist)
4. Select the new Excel file (updated version) for comparison
5. Choose the appropriate sheet (if multiple sheets exist)
6. The tool will process the files and show detailed statistics
7. Choose where to save the merged output file
8. Optionally open the output folder when complete

## Output
The output Excel file contains only the records that meet the filtering criteria:
- Records from the second file with GUIDs not present in the first file
- Records from the second file with GUIDs present in the first file but with incomplete Approver/Reason information

## Error Handling
- Validates file selection
- Checks for required columns
- Handles empty or corrupted Excel files
- Provides detailed error messages and processing statistics
