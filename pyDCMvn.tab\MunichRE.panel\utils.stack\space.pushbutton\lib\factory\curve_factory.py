# coding:utf8
from DCMvn.core import DB

class CurveFactory:

    def __init__(self):
        pass
    
    @staticmethod
    def get_boundary_curves_from_mesh(mesh, transform, tolerance=0.001, bottom=True):
        # type: (<PERSON>.<PERSON>, DB.Transform, float, bool) -> list[list[DB.Curve]]
        """
        Alternative approach: Find the lowest Z-level triangles and extract their boundary
        
        Arguments:
            mesh (DB.Mesh): The mesh from which to extract boundary curves.
            transform (DB.Transform): Transform to apply to the curves.
            tolerance (float): Tolerance for curve comparison.
            bottom (bool): If True, only consider triangles at the bottom level (Z normal pointing down).
        """
        vertices = mesh.Vertices
        triangles_count = mesh.NumTriangles
        
        # Find the minimum Z coordinate
        if bottom:
            # For bottom level, find the minimum Z coordinate
            target_z = min(vertex.Z for vertex in vertices)
        else:
            # For top level, find the maximum Z coordinate
            target_z = max(vertex.Z for vertex in vertices)

        z_threshold = target_z + tolerance  # Allow small tolerance for "bottom" level
        
        bottom_edges = []
        
        # Find triangles at the bottom level
        for i in range(triangles_count):
            triangle = mesh.get_Triangle(i)  # type: DB.MeshTriangle
            
            # Get triangle vertices
            v1 = vertices[triangle.get_Index(0)]  # noqa
            v2 = vertices[triangle.get_Index(1)]  # noqa
            v3 = vertices[triangle.get_Index(2)]  # noqa
            
            # Check if all vertices are at bottom level
            if v1.Z <= z_threshold and v2.Z <= z_threshold and v3.Z <= z_threshold:
                try:
                    edge1 = DB.Line.CreateTransformed(DB.Line.CreateBound(v1, v2), transform)
                    edge2 = DB.Line.CreateTransformed(DB.Line.CreateBound(v2, v3), transform)
                    edge3 = DB.Line.CreateTransformed(DB.Line.CreateBound(v3, v1), transform)
                    
                    bottom_edges.extend([edge1, edge2, edge3])
                except: # noqa: E722
                    continue
        
        if not bottom_edges:
            return []
        
        # Get boundary curves and group them
        boundary_curves = CurveFactory.get_boundary_curves(bottom_edges)
        curve_groups = CurveFactory.group_continuous_curves(boundary_curves, tolerance)

        return curve_groups

    @staticmethod
    def get_boundary_curves(curves):
        # type: (list[DB.Curve]) -> list[DB.Curve]
        """
        Find boundary curves by identifying curves that appear only once
        (not shared between adjacent faces).
        """
        if not curves or len(curves) == 0:
            return []
        
        # Dictionary to count curve occurrences
        curve_list = []
        
        tolerance = 0.001  # Small tolerance for comparing curves
        
        for curve in curves:
            found_match = False
            
            # Check if this curve is similar to any existing curve
            for i, (existing_curve, count) in enumerate(curve_list):
                if CurveFactory.are_curves_similar(curve, existing_curve, tolerance):
                    curve_list[i] = (existing_curve, count + 1)
                    found_match = True
                    break
            
            if not found_match:
                curve_list.append((curve, 1))
        
        # Return curves that appear only once (boundary curves)
        boundary_curves = [curve for curve, count in curve_list if count == 1]
        
        return boundary_curves

    @staticmethod
    def are_curves_similar(curve1, curve2, tolerance):
        # type: (DB.Curve, DB.Curve, float) -> bool
        """
        Check if two curves are similar (same geometry, possibly different direction)
        """
        try:
            # Compare start and end points
            start1 = curve1.GetEndPoint(0)
            end1 = curve1.GetEndPoint(1)
            start2 = curve2.GetEndPoint(0)
            end2 = curve2.GetEndPoint(1)
            
            # Check if curves are the same (same direction)
            if (start1.DistanceTo(start2) < tolerance and end1.DistanceTo(end2) < tolerance):
                return True
                
            # Check if curves are the same (opposite direction)
            if (start1.DistanceTo(end2) < tolerance and end1.DistanceTo(start2) < tolerance):
                return True
                
            return False
        except: # noqa: E722
            return False

    @staticmethod
    def get_boundary_curves_from_solid(solid, transform, tolerance=0.001, bottom=True):
        # type: (DB.Solid, DB.Transform, float, bool) -> list[list[DB.Curve]]
        """
        Alternative approach: Get boundary directly from face edges
        
        Arguments:
            solid (DB.Solid): The solid from which to extract boundary curves.
            tolerance (float): Tolerance for curve comparison.
            bottom (bool): If True, only consider bottom faces (Z normal pointing down).
        """
        if bottom:
            faces = [face for face in solid.Faces if isinstance(face, DB.PlanarFace) and face.FaceNormal.Z < -0.999] # Z normal pointing down
        else:
            faces = [face for face in solid.Faces if isinstance(face, DB.PlanarFace) and face.FaceNormal.Z > 0.999] # Z normal pointing up
        boundary_curves = []
        
        for face in faces:
            try:
                # Get all curve loops for this face
                curve_loops = face.GetEdgesAsCurveLoops()
                
                # The first curve loop is typically the outer boundary
                if curve_loops and len(curve_loops) > 0:
                    outer_loop = curve_loops[0]
                    for curve in outer_loop:
                        boundary_curves.append(DB.Line.CreateTransformed(curve, transform))
            except: # noqa: E722
                continue
        
        # Now filter to get only the true boundary curves
        curves = CurveFactory.get_boundary_curves(boundary_curves)
        return CurveFactory.group_continuous_curves(curves, tolerance)

    # @staticmethod
    # def group_continuous_curves(curves, tolerance=0.001):
    #     # type: (list[DB.Curve], float) -> list[list[DB.Curve]]
    #     """
    #     Group curves into continuous chains that can form curve loops.
    #     Returns a list of curve groups, each group contains connected curves.
    #     """
    #     if not curves or len(curves) == 0:
    #         return []
        
    #     curve_groups = []
    #     remaining_curves = list(curves)  # Copy the list
        
    #     while remaining_curves:
    #         # Start a new group with the first remaining curve
    #         current_group = [remaining_curves.pop(0)]
    #         group_changed = True
            
    #         # Keep adding connected curves until no more can be added
    #         while group_changed:
    #             group_changed = False
                
    #             # Get the endpoints of the current group
    #             group_start = current_group[0].GetEndPoint(0)
    #             group_end = current_group[-1].GetEndPoint(1)
                
    #             # Look for curves that connect to either end of the group
    #             for i in range(len(remaining_curves) - 1, -1, -1):  # Iterate backwards to safely remove
    #                 curve = remaining_curves[i]
    #                 curve_start = curve.GetEndPoint(0)
    #                 curve_end = curve.GetEndPoint(1)
                    
    #                 # Check if this curve connects to the end of the group
    #                 if group_end.DistanceTo(curve_start) < tolerance:
    #                     current_group.append(curve)
    #                     remaining_curves.pop(i)
    #                     group_changed = True
    #                     break
    #                 elif group_end.DistanceTo(curve_end) < tolerance:
    #                     # Reverse the curve direction by creating a new curve
    #                     try:
    #                         reversed_curve = curve.CreateReversed()
    #                         current_group.append(reversed_curve)
    #                         remaining_curves.pop(i)
    #                         group_changed = True
    #                         break
    #                     except: # noqa: E722
    #                         # If can't reverse, add as is
    #                         current_group.append(curve)
    #                         remaining_curves.pop(i)
    #                         group_changed = True
    #                         break
                    
    #                 # Check if this curve connects to the start of the group
    #                 elif group_start.DistanceTo(curve_end) < tolerance:
    #                     current_group.insert(0, curve)
    #                     remaining_curves.pop(i)
    #                     group_changed = True
    #                     break
    #                 elif group_start.DistanceTo(curve_start) < tolerance:
    #                     # Reverse the curve direction
    #                     try:
    #                         reversed_curve = curve.CreateReversed()
    #                         current_group.insert(0, reversed_curve)
    #                         remaining_curves.pop(i)
    #                         group_changed = True
    #                         break
    #                     except: # noqa: E722
    #                         # If can't reverse, add as is
    #                         current_group.insert(0, curve)
    #                         remaining_curves.pop(i)
    #                         group_changed = True
    #                         break
            
    #         curve_groups.append(current_group)
        
    #     return curve_groups
    
    @staticmethod
    def group_continuous_curves(curves, tolerance=0.001, min_curve_length=0.002625):
        # type: (list[DB.Curve], float, float) -> list[list[DB.Curve]]
        """
        Group curves into continuous chains that can form curve loops.
        Removes curves that are too short and extends adjacent curves to maintain continuity.
        
        Arguments:
            curves: List of curves to group
            tolerance: Distance tolerance for connecting curves
            min_curve_length: Minimum length for a curve to be valid in Revit (default 0.8 mm (0.002625 feet))
        
        Returns:
            List of curve groups, each group contains connected curves.
        """
        if not curves or len(curves) == 0:
            return []
        
        # First pass: Filter out curves that are too short and try to extend neighbors
        processed_curves = CurveFactory._process_short_curves(curves, min_curve_length, tolerance)
        
        if not processed_curves:
            return []
        
        curve_groups = []
        remaining_curves = list(processed_curves)  # Copy the list
        
        while remaining_curves:
            # Start a new group with the first remaining curve
            current_group = [remaining_curves.pop(0)]
            group_changed = True
            
            # Keep adding connected curves until no more can be added
            while group_changed:
                group_changed = False
                
                # Get the endpoints of the current group
                group_start = current_group[0].GetEndPoint(0)
                group_end = current_group[-1].GetEndPoint(1)
                
                # Look for curves that connect to either end of the group
                for i in range(len(remaining_curves) - 1, -1, -1):  # Iterate backwards to safely remove
                    curve = remaining_curves[i]
                    curve_start = curve.GetEndPoint(0)
                    curve_end = curve.GetEndPoint(1)
                    
                    # Check if this curve connects to the end of the group
                    if group_end.DistanceTo(curve_start) < tolerance:
                        current_group.append(curve)
                        remaining_curves.pop(i)
                        group_changed = True
                        break
                    elif group_end.DistanceTo(curve_end) < tolerance:
                        # Reverse the curve direction by creating a new curve
                        try:
                            reversed_curve = curve.CreateReversed()
                            current_group.append(reversed_curve)
                            remaining_curves.pop(i)
                            group_changed = True
                            break
                        except: # noqa: E722
                            # If can't reverse, add as is
                            current_group.append(curve)
                            remaining_curves.pop(i)
                            group_changed = True
                            break
                    
                    # Check if this curve connects to the start of the group
                    elif group_start.DistanceTo(curve_end) < tolerance:
                        current_group.insert(0, curve)
                        remaining_curves.pop(i)
                        group_changed = True
                        break
                    elif group_start.DistanceTo(curve_start) < tolerance:
                        # Reverse the curve direction
                        try:
                            reversed_curve = curve.CreateReversed()
                            current_group.insert(0, reversed_curve)
                            remaining_curves.pop(i)
                            group_changed = True
                            break
                        except: # noqa: E722
                            # If can't reverse, add as is
                            current_group.insert(0, curve)
                            remaining_curves.pop(i)
                            group_changed = True
                            break
            
            # Post-process the group to merge short adjacent curves
            current_group = CurveFactory._merge_short_curves_in_group(current_group, min_curve_length, tolerance)
            
            if current_group:  # Only add non-empty groups
                curve_groups.append(current_group)
        
        return curve_groups

    @staticmethod
    def _process_short_curves(curves, min_length, tolerance):
        # type: (list[DB.Curve], float, float) -> list[DB.Curve]
        """
        Process curves to handle short ones by extending adjacent curves or removing them.
        """
        if not curves:
            return []
        
        # Build connectivity map
        curve_connections = {}  # curve_index -> [connected_curve_indices]
        
        for i, curve1 in enumerate(curves):
            connections = []
            for j, curve2 in enumerate(curves):
                if i != j and CurveFactory._are_curves_connected(curve1, curve2, tolerance):
                    connections.append(j)
            curve_connections[i] = connections
        
        # Identify short curves and try to extend neighbors
        curves_to_remove = set()
        extended_curves = {}  # curve_index -> new_extended_curve
        
        for i, curve in enumerate(curves):
            if curve.Length < min_length:
                # Try to extend connected curves to bridge over this short curve
                connected_indices = curve_connections.get(i, [])
                
                if len(connected_indices) == 2:
                    # Short curve connects exactly two other curves - try to bridge them
                    curve1_idx, curve2_idx = connected_indices
                    curve1 = curves[curve1_idx]
                    curve2 = curves[curve2_idx]
                    
                    try:
                        # Create a direct connection between the two curves
                        bridged_curve = CurveFactory._create_bridge_curve(curve1, curve, curve2, tolerance)
                        if bridged_curve and bridged_curve.Length >= min_length:
                            extended_curves[curve1_idx] = bridged_curve
                            curves_to_remove.add(i)  # Remove the short curve
                            curves_to_remove.add(curve2_idx)  # Remove the second curve (replaced by extended)
                    except: # noqa: E722
                        # If bridging fails, just remove the short curve
                        curves_to_remove.add(i)
                else:
                    # Can't bridge, just remove the short curve
                    curves_to_remove.add(i)
        
        # Build result list
        result_curves = []
        for i, curve in enumerate(curves):
            if i in curves_to_remove:
                continue
            elif i in extended_curves:
                result_curves.append(extended_curves[i])
            else:
                result_curves.append(curve)
        
        return result_curves

    @staticmethod
    def _are_curves_connected(curve1, curve2, tolerance):
        # type: (DB.Curve, DB.Curve, float) -> bool
        """Check if two curves are connected at their endpoints."""
        start1, end1 = curve1.GetEndPoint(0), curve1.GetEndPoint(1)
        start2, end2 = curve2.GetEndPoint(0), curve2.GetEndPoint(1)
        
        return (start1.DistanceTo(start2) < tolerance or 
                start1.DistanceTo(end2) < tolerance or
                end1.DistanceTo(start2) < tolerance or
                end1.DistanceTo(end2) < tolerance)

    @staticmethod
    def _create_bridge_curve(curve1, short_curve, curve2, tolerance):
        # type: (DB.Curve, DB.Curve, DB.Curve, float) -> DB.Curve
        """
        Create a bridging curve that connects curve1 and curve2, skipping over short_curve.
        """
        try:
            # Find connection points
            start1, end1 = curve1.GetEndPoint(0), curve1.GetEndPoint(1)
            start_short, end_short = short_curve.GetEndPoint(0), short_curve.GetEndPoint(1)
            start2, end2 = curve2.GetEndPoint(0), curve2.GetEndPoint(1)
            
            # Determine which ends connect to the short curve
            curve1_connect_point = None
            curve2_connect_point = None
            curve1_far_point = None
            curve2_far_point = None
            
            # Find curve1's connection to short curve
            if start1.DistanceTo(start_short) < tolerance or start1.DistanceTo(end_short) < tolerance:
                curve1_connect_point = start1
                curve1_far_point = end1
            elif end1.DistanceTo(start_short) < tolerance or end1.DistanceTo(end_short) < tolerance:
                curve1_connect_point = end1
                curve1_far_point = start1
            
            # Find curve2's connection to short curve
            if start2.DistanceTo(start_short) < tolerance or start2.DistanceTo(end_short) < tolerance:
                curve2_connect_point = start2
                curve2_far_point = end2
            elif end2.DistanceTo(start_short) < tolerance or end2.DistanceTo(end_short) < tolerance:
                curve2_connect_point = end2
                curve2_far_point = start2
            
            if curve1_connect_point and curve2_connect_point and curve1_far_point and curve2_far_point:
                # Create extended curve from curve1's far point to curve2's far point
                return DB.Line.CreateBound(curve1_far_point, curve2_far_point)
            
        except: # noqa: E722
            pass
        
        return None

    @staticmethod
    def _merge_short_curves_in_group(curve_group, min_length, tolerance):
        # type: (list[DB.Curve], float, float) -> list[DB.Curve]
        """
        Merge adjacent short curves within a group to create longer, valid curves.
        """
        if not curve_group:
            return []
        
        result = []
        i = 0
        
        while i < len(curve_group):
            current_curve = curve_group[i]
            
            if current_curve.Length >= min_length:
                result.append(current_curve)
                i += 1
            else:
                # Try to merge with next curves until we get a valid length
                merged_points = [current_curve.GetEndPoint(0)]
                j = i
                total_length = 0
                
                while j < len(curve_group) and total_length < min_length:
                    curve = curve_group[j]
                    merged_points.append(curve.GetEndPoint(1))
                    total_length += curve.Length
                    j += 1
                
                # Create merged curve if we have enough length
                if len(merged_points) >= 2 and total_length >= min_length:
                    try:
                        merged_curve = DB.Line.CreateBound(merged_points[0], merged_points[-1])
                        if merged_curve.Length >= min_length:
                            result.append(merged_curve)
                    except: # noqa: E722
                        pass
                
                i = j
        
        return result
    
    @staticmethod
    def get_point_inside_largest_curve_array(curve_arrays):
        # type: (list[DB.CurveArray]) -> DB.XYZ | None
        """
        Get a point inside the largest CurveArray (by area).
        
        Arguments:
            curve_arrays: List of CurveArray objects
            
        Returns:
            DB.XYZ: Point inside the largest curve array, or None if no valid point found
        """
        if not curve_arrays or len(curve_arrays) == 0:
            return None
        
        largest_array = None
        largest_area = 0
        
        # Find the largest curve array by area
        for curve_array in curve_arrays:
            try:
                area = CurveFactory._calculate_curve_array_area(curve_array)
                if area > largest_area:
                    largest_area = area
                    largest_array = curve_array
            except: # noqa: E722
                continue
        
        if largest_array is None:
            return None
        
        # Get a point inside the largest curve array
        return CurveFactory._get_point_inside_curve_array(largest_array)
    
    @staticmethod
    def _calculate_curve_array_area(curve_array):
        # type: (DB.CurveArray) -> float
        """
        Calculate the approximate area of a CurveArray using the shoelace formula.
        Assumes the curves form a closed polygon.
        """
        if curve_array.Size == 0:
            return 0
        
        # Collect all points from the curve array
        points = []
        for curve in curve_array:
            points.append(curve.GetEndPoint(0))
        
        # Add the last point to close the polygon if not already closed
        first_point = curve_array.get_Item(0).GetEndPoint(0)
        last_point = curve_array.get_Item(curve_array.Size - 1).GetEndPoint(1)
        
        if first_point.DistanceTo(last_point) > 0.001:  # Not closed
            points.append(last_point)
        
        # Apply shoelace formula (assuming Z is constant)
        if len(points) < 3:
            return 0
        
        area = 0
        for i in range(len(points)):
            j = (i + 1) % len(points)
            area += points[i].X * points[j].Y
            area -= points[j].X * points[i].Y
        
        return abs(area) / 2.0
    
    @staticmethod
    def _get_point_inside_curve_array(curve_array):
        # type: (DB.CurveArray) -> DB.XYZ
        """
        Get a point inside the given CurveArray using centroid calculation.
        """
        if curve_array.Size == 0:
            return None
        
        try:
            # Method 1: Try to use Revit's built-in functionality if available
            # Create a curve loop from the curve array
            curve_loop = DB.CurveLoop.Create([curve for curve in curve_array])
            
            # Try to get the plane of the curve loop
            if curve_loop.HasPlane():
                plane = curve_loop.GetPlane()
                # Get a point slightly inside the boundary
                # This works for planar regions
                return plane.Origin
        except: # noqa: E722
            pass
        
        # Method 2: Calculate centroid of the polygon
        try:
            points = []
            total_length = 0
            
            # Collect points weighted by curve length
            for curve in curve_array:
                length = curve.Length
                mid_point = curve.Evaluate(0.5, True)  # Get midpoint of curve
                points.append((mid_point, length))
                total_length += length
            
            if total_length == 0 or len(points) == 0:
                return None
            
            # Calculate weighted centroid
            centroid_x = sum(point.X * weight for point, weight in points) / total_length
            centroid_y = sum(point.Y * weight for point, weight in points) / total_length
            centroid_z = sum(point.Z * weight for point, weight in points) / total_length
            
            return DB.XYZ(centroid_x, centroid_y, centroid_z)
            
        except: # noqa: E722
            pass
        
        # Method 3: Simple geometric center as fallback
        try:
            # Get all endpoints
            all_points = []
            for curve in curve_array:
                all_points.append(curve.GetEndPoint(0))
                all_points.append(curve.GetEndPoint(1))
            
            if len(all_points) == 0:
                return None
            
            # Calculate simple average
            avg_x = sum(point.X for point in all_points) / len(all_points)
            avg_y = sum(point.Y for point in all_points) / len(all_points)
            avg_z = sum(point.Z for point in all_points) / len(all_points)
            
            return DB.XYZ(avg_x, avg_y, avg_z)
            
        except: # noqa: E722
            return None
    
    @staticmethod
    def get_interior_point_with_offset(curve_arrays, offset_distance=1.0):
        # type: (list[DB.CurveArray], float) -> DB.XYZ
        """
        Alternative method: Get a point inside the largest CurveArray with inward offset.
        Useful when you need a point that's definitely inside and away from boundaries.
        
        Arguments:
            curve_arrays: List of CurveArray objects
            offset_distance: Distance to offset inward from boundaries
            
        Returns:
            DB.XYZ: Point inside the largest curve array with offset
        """
        if not curve_arrays or len(curve_arrays) == 0:
            return None
        
        largest_array = None
        largest_area = 0
        
        # Find the largest curve array by area
        for curve_array in curve_arrays:
            try:
                area = CurveFactory._calculate_curve_array_area(curve_array)
                if area > largest_area:
                    largest_area = area
                    largest_array = curve_array
            except: # noqa: E722
                continue
        
        if largest_array is None:
            return None
        
        try:
            # Create offset curves inward
            offset_curves = []
            for curve in largest_array:
                try:
                    # Offset the curve inward (negative offset for inward direction)
                    normal = DB.XYZ.BasisZ  # Assume horizontal plane
                    offset_curve = curve.CreateOffset(-offset_distance, normal)
                    offset_curves.append(offset_curve)
                except: # noqa: E722
                    continue
            
            if offset_curves:
                # Get centroid of offset curves
                all_points = []
                for curve in offset_curves:
                    all_points.append(curve.Evaluate(0.5, True))
                
                if all_points:
                    avg_x = sum(point.X for point in all_points) / len(all_points)
                    avg_y = sum(point.Y for point in all_points) / len(all_points)
                    avg_z = sum(point.Z for point in all_points) / len(all_points)
                    
                    return DB.XYZ(avg_x, avg_y, avg_z)
        except: # noqa: E722
            pass
        
        # Fallback to regular interior point
        return CurveFactory._get_point_inside_curve_array(largest_array)