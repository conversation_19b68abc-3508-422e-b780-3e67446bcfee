# coding: utf-8
from .base import TabooRule, RuleRegistry, register_rule
# Import built-in plugin rules to self-register
try:
    from .o1_beams_u1u2_rule import O1BeamsU1U2Rule  # noqa: F401
    from .hg_exist_beams_rule import HGExistBeamsRule  # noqa: F401
    from .hg_new_beams_rule import HGNewBeamsRule  # noqa: F401
    from .hg_new_wall_rule import HGNewWallRule  # noqa: F401
    from .hg_new_floors_rule import HGNewFloorsRule  # noqa: F401
    # Note: hg_new_walls_doors_rule was deleted
except Exception as ex:
    # Safe import guard to avoid breaking load in contexts without dependencies
    from DCMvn.core.framework import Debug
    Debug.WriteLine("Warning: Could not import some rules: {}".format(str(ex)))

__all__ = [
    'TabooRule', 'RuleRegistry', 'register_rule'
]

