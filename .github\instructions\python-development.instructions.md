---
applyTo: "**/*.py"
---

# DCMvn Python Development Guidelines

## File Headers and Python Engine Selection
- **IronPython 2.7 (default)**: Use `# coding: utf-8` for all scripts
- **Python 3 (when needed)**: Use `#! python3` shebang (no coding declaration needed)
- Follow pyRevit documentation for engine specifications

## Core Framework Integration
Always start with these imports for DCMvn applications:
```python
# coding: utf-8
import clr
import os
from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.core.framework import System, List, Debug, Trace, ObservableCollection
from DCMvn.forms.mvvm import ViewModelBase, RelayCommand
from DCMvn.forms import alert

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)
```

## MVVM Architecture Implementation
- **ViewModels**: Always inherit from `ViewModelBase`
- **Commands**: Use `RelayCommand(execute_method, can_execute_method)` pattern
- **Collections**: Use `ObservableCollection[T]()` for UI-bound data
- **Property updates**: Always call `RaisePropertyChanged("property_name")`
- **Thread safety**: Use external events for Revit API operations

## External Events and Thread Safety
- All Revit API operations must occur on the main thread
- Use `IExternalEventHandler` pattern for safe API calls from UI
- Wrap all document modifications in transactions
- Use this pattern for external event handlers:

```python
class ActionEventHandler(UI.IExternalEventHandler):
    def __init__(self):
        self._external_event = UI.ExternalEvent.Create(self)
        self._action = None
        
    def Execute(self, application):
        if self._action:
            try:
                self._action(application)
            except Exception as e:
                Debug.WriteLine("External event error: {}".format(e))
            finally:
                self._action = None
```

## Error Handling Best Practices
- Always validate document requirements before proceeding
- Use try-catch blocks around all Revit API operations
- Provide user-friendly error messages via `alert()`
- Log detailed errors using `Debug.WriteLine()`
- Check `element.IsValidObject` before element operations

## Service Layer Pattern
- **CollectorService**: Element collection and filtering with caching
- **DetectionService**: Spatial relationship and geometric calculations
- **ReportService**: Data processing and output generation
- Initialize services with document reference in ViewModels
