# coding: utf-8
from pydoc_data.topics import topics

import clr
from DCMvn.core import DB
from DCMvn.core.framework import List, System  # noqa

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq) # noqa


def _get_level_at_point(location_point, current_transform, ordered_levels, use_offset, offset_value):
    # type: (DB.XYZ, DB.Transform, List[DB.Level], bool, float) -> DB.Level | List[DB.Level]

    location_height = current_transform.OfPoint(location_point).Z
    actual_level = ordered_levels.Where(lambda x: x.Elevation <= location_height).FirstOrDefault()

    if use_offset:
        below_or_at_levels = ordered_levels.Where(lambda x: x.Elevation - offset_value <= location_height).ToList()
        if below_or_at_levels.Count == 0:
            true_lev = ordered_levels.FirstOrDefault()
        else:
            true_lev = below_or_at_levels.FirstOrDefault()
        return [true_lev, actual_level] if not true_lev.Equals(actual_level) else true_lev
    else:
        true_lev = actual_level if actual_level else ordered_levels.FirstOrDefault()
        return true_lev


def _get_levels_spanned_by_curve(location_curve, current_transform, ordered_levels, use_offset, offset_value):
    # type: (DB.Curve, DB.Transform, List[DB.Level], bool, float) -> List[DB.Level] | DB.Level
    """
    Get all levels spanned by a curve element.
    """
    sorted_points = sorted(location_curve.Tessellate(), key=lambda x: (x.X, x.Y, x.Z))
    start_point = sorted_points[0]
    end_point = sorted_points[-1]

    # only use offset for end point
    start_level = _get_level_at_point(start_point, current_transform, ordered_levels, use_offset, offset_value)  # type: DB.Level | List[DB.Level]
    top_level = _get_level_at_point(end_point, current_transform, ordered_levels, False, offset_value)  # type: DB.Level

    # Collect all levels efficiently
    all_relevant_levels = []
    if hasattr(start_level, "__iter__"):
        all_relevant_levels.extend(start_level)
        all_relevant_levels.append(top_level)
    else:
        all_relevant_levels.append(start_level)
        all_relevant_levels.append(top_level)

    # Remove duplicates and get min/max elevations in one pass
    unique_levels = list(set(all_relevant_levels))
    elevations = [level.Elevation for level in unique_levels]
    min_elevation = min(elevations)
    max_elevation = max(elevations)

    spanned_levels = ordered_levels.Where(lambda x: min_elevation <= x.Elevation <= max_elevation).ToList()

    return spanned_levels


def get_element_level(element, current_transform, ordered_levels, offset_value, system_classification):
    # type: (DB.Element, DB.Transform, list[DB.Level], float, str) -> DB.Level | list[DB.Level] | None
    """
    Get the level of an element based on its location.

    Args:
        element (Element): Element to get level for.
        current_transform (Transform): Current transform to use.
        ordered_levels (list[Level]): List of ordered levels by elevation.
        offset_value (float): Offset down value from the actual level.
        system_classification (str): System classification to check for offset.

    Returns:
        Level | list[Level] | None: Level of the element.
    """
    location = element.Location
    if location is None:
        return None

    use_offset = False
    if _is_element_match_system(element, system_classification):
        use_offset = True

    if isinstance(location, DB.LocationCurve):
        return _get_levels_spanned_by_curve(location.Curve, current_transform, ordered_levels, use_offset, offset_value)
    else:
        return _get_level_at_point(location.Point, current_transform, ordered_levels, use_offset, offset_value)


def _is_element_match_system(element, system_classification_register):
    # type: (DB.Element, str) -> bool
    system_classification = element.get_Parameter(DB.BuiltInParameter.RBS_SYSTEM_CLASSIFICATION_PARAM)
    if system_classification is None:
        return False
    value = system_classification.AsValueString()
    if not value:
        return False
    return value in system_classification_register

