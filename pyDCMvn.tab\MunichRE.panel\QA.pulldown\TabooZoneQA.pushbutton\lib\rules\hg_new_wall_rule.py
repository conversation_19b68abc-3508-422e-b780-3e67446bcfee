# coding: utf-8
"""
Plugin rule for HG New Walls
Wraps HG new wall element collectors in a TabooRule.
"""
from DCMvn.core import DB
from DCMvn.core.framework import Debug
from DCMvn.revit.query.query import DQuery

from .base import TabooRule, register_rule


@register_rule
class HGNewWallRule(TabooRule):
    rule_id = "HG_NEW_WALL"
    display_name = "HG New Walls"

    def __init__(self, tolerance_mm=10.0):
        self.tolerance_mm = tolerance_mm
        self._arc_query = None  # type: DQuery | None
        self._mass_query = None  # type: DQuery | None

    def collect_arc_elements(self, arc_doc):
        try:
            self._arc_query = DQuery(arc_doc) if self._arc_query is None else self._arc_query
            if isinstance(arc_doc, DB.RevitLinkInstance):
                arc_doc = arc_doc.GetLinkDocument()
            return self._arc_query.get_by_workset("000_LinkIFC_HG-ARC_NewConstruction_WallLoadBearing")
        except Exception as ex:
            Debug.WriteLine("HGNewWallRule.collect_arc_elements error: {}".format(str(ex)))
            return []

    def collect_mass_elements(self, mass_doc):
        try:
            self._mass_query = DQuery(mass_doc) if self._mass_query is None else self._mass_query
            return self._mass_query.get_by_workset("400_TBZ_NewConstruction_WallLoadBearing")
        except Exception as ex:
            Debug.WriteLine("HGNewWallRule.collect_mass_elements error: {}".format(str(ex)))
            return []

    @property
    def mass_guid_param_spec(self):
        return "SI_TE_DM_IfcGUID"

    @property
    def tolerance_mm(self):
        return self.tolerance_mm

    @property
    def extra_excel_columns(self):
        return {"Guid Validation": "Matched", "Centroid Result": "Passed"}