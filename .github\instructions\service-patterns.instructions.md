---
applyTo: "**/services/**/*.py"
---

# Service Layer Patterns

## Service Base Pattern
All services should follow this structure:
```python
class BaseService(object):
    def __init__(self, document):
        self.document = document
        self._cache = {}
    
    def clear_cache(self):
        self._cache.clear()
```

## CollectorService Pattern
Use this pattern for element collection and filtering:
```python
class CollectorService(BaseService):
    def __init__(self, document):
        super(CollectorService, self).__init__(document)
    
    def get_elements_by_category(self, category, use_cache=True):
        cache_key = "category_{}".format(category)
        
        if use_cache and cache_key in self._cache:
            return self._cache[cache_key]
        
        collector = (DB.FilteredElementCollector(self.document)
                    .OfCategory(category)
                    .WhereElementIsNotElementType())
        
        elements = collector.ToElements()
        
        if use_cache:
            self._cache[cache_key] = elements
        
        return elements
    
    def get_physical_elements(self, use_cache=True):
        cache_key = "physical_elements"
        
        if use_cache and cache_key in self._cache:
            return self._cache[cache_key]
        
        physical_filter = get_element_physical_filter(self.document)
        elements = (DB.FilteredElementCollector(self.document)
                   .WhereElementIsNotElementType()
                   .WherePasses(physical_filter)
                   .ToElements())
        
        if use_cache:
            self._cache[cache_key] = elements
        
        return elements
```

## DetectionService Pattern
Use this for spatial relationships and calculations:
```python
class DetectionService(BaseService):
    def __init__(self, document):
        super(DetectionService, self).__init__(document)
        self.tolerance = 0.1  # feet
    
    def detect_intersections(self, element1, element2):
        try:
            # Implement intersection logic
            return True
        except Exception as e:
            Debug.WriteLine("Intersection detection error: {}".format(e))
            return False
```

## ReportService Pattern
Use this for data processing and export:
```python
class ReportService(BaseService):
    def __init__(self, document):
        super(ReportService, self).__init__(document)
    
    def generate_report(self, elements, file_path):
        try:
            # Process data and export
            return True
        except Exception as e:
            Debug.WriteLine("Report generation error: {}".format(e))
            return False
```

## Service Best Practices
- Implement caching for expensive operations
- Provide cache management methods
- Use defensive programming
- Log errors appropriately
- Return meaningful results
