import clr
from DCMvn.core import DB, UI
from DCMvn.core.framework import System, List
from DCMvn.forms.mvvm import ViewModelBase, RelayCommand
from DCMvn.io.config import get_config_property, set_config_property

from ..interaction import set_scope
from ..search_engine import search_by_ifc_guid, parse_raw_request, get_element_by_id
from ..external.external_event import ExternalEventSolver

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

SELECT_GUID_SECTION = "pyDCMvn.MunichRE.SelectGuid"
SELECT_MODEL_INDEX = "select_model_index"

class SearchElementsViewModel(ViewModelBase):
    def __init__(self, uidoc, external_event_solver):
        # type: (UI.UIDocument, ExternalEventSolver) -> None
        ViewModelBase.__init__(self)
        self._uidoc = uidoc
        self._doc = uidoc.Document  # type: DB.Document
        self._external_event_solver = external_event_solver

        index = get_config_property(SELECT_GUID_SECTION, SELECT_MODEL_INDEX)
        if not index:
            self._select_model_index = 0
        else:
            try:
                self._select_model_index = int(index)
            except ValueError:
                self._select_model_index = 0

        self._guids_input = ""
        self._revit_link_instances = (DB.FilteredElementCollector(self._doc).OfClass(DB.RevitLinkInstance) # noqa
                         .Where(lambda x: DB.RevitLinkType.IsLoaded(self._doc, x.GetTypeId())).ToList())
        self._models = (self._revit_link_instances.Select(lambda x: x.GetLinkDocument()).ToList())
        self._is_section_box = False
        self._select_command = RelayCommand(self.execute_select_command, self.can_execute_select_command)

    @property
    def models(self):
        return self._models

    @property
    def is_section_box(self):
        return self._is_section_box

    @is_section_box.setter
    def is_section_box(self, value):
        self._is_section_box = value
        self.RaisePropertyChanged("is_section_box")

    @property
    def selected_model_index(self):
        if not self._select_model_index or self._select_model_index >= len(self._models):
            self._select_model_index = 0
        return self._select_model_index

    @selected_model_index.setter
    def selected_model_index(self, value):
        self._select_model_index = value
        self.RaisePropertyChanged("selected_model_index")
        set_config_property(SELECT_GUID_SECTION, SELECT_MODEL_INDEX, value)

    @property
    def guids_input(self):
        return self._guids_input

    @guids_input.setter
    def guids_input(self, value):
        self._guids_input = value
        self._select_command.RaiseCanExecuteChanged()
        self.RaisePropertyChanged("guids_input")

    @property
    def select_command(self):
        return self._select_command

    def can_execute_select_command(self, param):  # noqa
        return self._guids_input != ""

    def execute_select_command(self, param):  # noqa
        target_doc = None
        if self._models and 0 <= self.selected_model_index < len(self._models):
            target_doc = self._models[self.selected_model_index]
        if target_doc is None:
            target_doc = self._doc

        lines = self._guids_input.splitlines()
        raw_items = parse_raw_request(lines)
        found_elements = []

        for raw_id in raw_items:
            # 1. Try parse as integer (ElementId)
            element_by_id = get_element_by_id(raw_id, target_doc)
            if element_by_id:
                found_elements.append(element_by_id)
                continue

            # 2. Check if 45 chars w/ 5 dashes => UniqueId
            if len(raw_id) == 45 and raw_id.count('-') == 5:
                element_by_unique_id = target_doc.GetElement(raw_id)
                if element_by_unique_id:
                    found_elements.append(element_by_unique_id)
                    continue

            # 3. Otherwise, search by IFC GUID
            elements_ifc = search_by_ifc_guid(raw_id, target_doc)
            for el in elements_ifc:
                found_elements.append(el)

        # Now select these in the active UI
        if found_elements:
            link_elements = [el for el in found_elements if el.Document.IsLinked]
            elements = [el for el in found_elements if not el.Document.IsLinked]

            if elements:
                self._uidoc.ShowElements(List[DB.ElementId]([ele.Id for ele in elements]))
            if link_elements:
                target_link_instance = [inst for inst in self._revit_link_instances if target_doc.Title in inst.Name][0]  # type: DB.RevitLinkInstance
                link_refs = [DB.Reference(ele).CreateLinkReference(target_link_instance) for ele in link_elements]
                self._uidoc.Selection.SetReferences(List[DB.Reference](link_refs))
                self._external_event_solver.set_args(
                    self._uidoc,
                    found_elements,
                    target_link_instance.GetTotalTransform(),
                    create_section_box=self.is_section_box
                )
                self._external_event_solver.raise_event()




