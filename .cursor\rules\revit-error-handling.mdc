---
description: Error handling and robustness for Revit API with pyRevit + DCMvn (logger, Debug/Trace, transactions, safe defaults)
globs: *.py
---

### Goals
- Follow pyRevit error-handling patterns and use DCMvn utilities for lightweight logging.
- Prefer pyRevit transaction/context helpers and structured logging; return safe defaults.

### Logging
- Preferred (pyRevit):
```python
from pyrevit import script
logger = script.get_logger()
# Usage
logger.debug("Collecting elements…")
logger.info("Processed %s elements", count)
logger.warning("Skipped invalid element %s", elid)
logger.error("Failure: %s", err)
```
- Lightweight (DCMvn fallback):
```python
from DCMvn.core.framework import Debug, Trace
Debug.WriteLine("Collecting elements…")
Trace.Write("Processing details…")
```
- Do not prepend level tags (e.g., "[INFO]", "[ERROR]") to Debug/Trace output. Use plain messages. For leveled, formatted output, prefer the pyRevit logger ([pyRevit API](https://docs.pyrevitlabs.io/reference/pyrevit/api/)).

### Transactions (pyRevit custom helpers)
- Use pyRevit transaction context managers; they auto-commit on success and roll back on exceptions:
```python
from pyrevit import revit, DB
from pyrevit import script
logger = script.get_logger()

with revit.Transaction("My Operation"):
    # Revit API writes here
    pass
```
- Grouped operations with partial commit/rollback:
```python
from pyRevit import revit  # alias ok: from pyrevit import revit

with revit.TransactionGroup("Batch Update") as tg:
    tg.Start()
    try:
        with revit.Transaction("Step 1"):
            pass
        with revit.Transaction("Step 2"):
            pass
        tg.Assimilate()  # commit group
    except Exception as ex:
        logger.exception(ex)
        tg.RollBack()    # roll back group
```
- pyRevit transaction helpers mirror Revit API transactions but are simpler to use; see pyRevit’s custom transaction utilities in the API docs ([pyRevit API](https://docs.pyrevitlabs.io/reference/pyrevit/api/)).

### Exception management
- Wrap all Revit API calls that write/modify model state in try/except.
- Log the exception with context (element ids, categories) and return safe defaults (False, None, empty lists).
```python
from Autodesk.Revit.DB import ElementId

def try_get_param_value(element, name):
    try:
        if element and element.IsValidObject:
            param = element.LookupParameter(name)
            return param.AsString() if param else None
        return None
    except Exception as ex:
        logger.exception(ex)
        return None
```

### Defensive programming
- Validate inputs (None/type checks); verify `element.IsValidObject` before access.
- Check for null returns from API calls; short-circuit early.
- Provide fallbacks for missing data; prefer early returns to deep nesting.

### Thread safety
- UI-bound commands must marshal Revit API writes to valid Revit context (External Events/transaction contexts). Don’t call modifying API from background threads.

### Safe defaults pattern
```python
def safe_collect_ids(doc, collector):
    try:
        return [el.Id for el in collector]
    except Exception as ex:
        logger.error("Collection failed: %s", ex)
        return []
```

### When to use DCMvn Debug/Trace versus pyRevit logger
- Use `pyrevit.script.get_logger()` when running under pyRevit for leveled, colorized logs in the output pane.
- Use `DCMvn.core.framework.Debug/Trace` for lightweight, direct messages without level prefixes, or inside shared utility modules.

### References
- pyRevit API reference (logger, contexts): [pyRevit API](https://docs.pyrevitlabs.io/reference/pyrevit/api/)
- pyRevit logging facilities overview: [Python Script Facilities](https://pyrevitlabs.notion.site/Python-Script-Facilities-dcaf1e4660134974ba69e023b3714ddc)
- General robustness patterns align with DCMvn workspace rules and Revit best practices.