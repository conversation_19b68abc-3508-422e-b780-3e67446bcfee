# coding: utf-8
"""
HG Rule Executor - Interactive rule selection and execution
Allows users to select and execute individual HG rules with validation workflow.
"""
import clr
import System

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)  # noqa: F401

from DCMvn.core import DB, HOST_APP
from DCMvn.core.framework import Debug
from DCMvn.forms import wpfforms, alert
from DCMvn.revit.ui import get_mainwindow

from ..validation import ValidationWorkflow
from ..reporting import ExcelReporter, HtmlReporter


class RuleExecutor(object):
    """Interactive rule executor with user selection interface for HG and O1 rules."""
    
    def __init__(self):
        self.doc = HOST_APP.doc
        
    def get_available_rules(self):
        # type: () -> dict[str, object]
        """Create all rule instances and return them by rule_id."""
        rules = {}
        
        try:
            # Import and create HG rules
            from .hg_exist_beams_rule import HGExistBeamsRule
            rule = HGExistBeamsRule()
            rules[rule.rule_id] = rule
            Debug.WriteLine("Created rule: {}".format(rule.rule_id))
        except Exception as ex:
            Debug.WriteLine("Could not create HGExistBeamsRule: {}".format(str(ex)))
        
        try:
            from .hg_new_beams_rule import HGNewBeamsRule
            rule = HGNewBeamsRule()
            rules[rule.rule_id] = rule
            Debug.WriteLine("Created rule: {}".format(rule.rule_id))
        except Exception as ex:
            Debug.WriteLine("Could not create HGNewBeamsRule: {}".format(str(ex)))
        
        try:
            from .hg_new_wall_rule import HGNewWallRule
            rule = HGNewWallRule()
            rules[rule.rule_id] = rule
            Debug.WriteLine("Created rule: {}".format(rule.rule_id))
        except Exception as ex:
            Debug.WriteLine("Could not create HGNewWallRule: {}".format(str(ex)))
        
        try:
            from .hg_new_floors_rule import HGNewFloorsRule
            rule = HGNewFloorsRule()
            rules[rule.rule_id] = rule
            Debug.WriteLine("Created rule: {}".format(rule.rule_id))
        except Exception as ex:
            Debug.WriteLine("Could not create HGNewFloorsRule: {}".format(str(ex)))
        
        try:
            # Import and create O1 rule
            from .o1_beams_u1u2_rule import O1BeamsU1U2Rule
            rule = O1BeamsU1U2Rule()
            rules[rule.rule_id] = rule
            Debug.WriteLine("Created rule: {}".format(rule.rule_id))
        except Exception as ex:
            Debug.WriteLine("Could not create O1BeamsU1U2Rule: {}".format(str(ex)))
        
        Debug.WriteLine("Available rule IDs: {}".format(list(rules.keys())))
        return rules
    
    def get_link_documents_for_rule(self, rule_id):
        # type: (str) -> list[DB.RevitLinkInstance]
        """Get all loaded link documents appropriate for the selected rule."""
        link_instances = (DB.FilteredElementCollector(self.doc)
                         .OfClass(DB.RevitLinkInstance)
                         .WhereElementIsNotElementType()
                         .Where(lambda x: DB.RevitLinkType.IsLoaded(self.doc, x.GetTypeId()))
                         .ToList())
        
        # Filter based on rule type
        if rule_id.startswith("HG_"):
            # For HG rules, look for HG in the name
            filtered_links = [link for link in link_instances if "HG" in link.Name]
        elif rule_id.startswith("O1_"):
            # For O1 rules, look for O1 in the name
            filtered_links = [link for link in link_instances if "O1" in link.Name]
        else:
            # Fallback: return all loaded links
            filtered_links = list(link_instances)
        
        return filtered_links
    
    def select_rule(self):
        # type: () -> object | None
        """Let user select a rule to execute (HG or O1)."""
        available_rules = self.get_available_rules()
        
        if not available_rules:
            alert("No rules found", warn_icon=True)
            return None
        
        # Get rule IDs and sort them
        rule_ids = list(available_rules.keys())
        rule_ids.sort(key=lambda x: (0 if x.startswith('HG_') else 1, x))
        
        Debug.WriteLine("Available rule IDs: {}".format(rule_ids))
        
        # Show selection dialog with simple list of rule IDs
        selected_items = wpfforms.SelectFromList.show(
            context=rule_ids,
            title="Select Rule to Execute",
            multiselect=False,
            show_min_button=False,
            show_maxrestore_button=False,
            owner=get_mainwindow()
        )
        
        if selected_items:
            selected_rule_id = selected_items[0]  # SelectFromList returns a list even for single selection
            Debug.WriteLine("Selected rule ID: '{}'".format(selected_rule_id))
            return available_rules[selected_rule_id]  # Return the rule instance
        
        return None
    
    def select_link_document(self, rule_id):
        # type: (str) -> DB.RevitLinkInstance | None
        """Let user select a link document appropriate for the selected rule."""
        link_instances = self.get_link_documents_for_rule(rule_id)
        
        rule_type = "HG" if rule_id.startswith("HG_") else "O1" if rule_id.startswith("O1_") else "Unknown"
        
        if not link_instances:
            alert("No {} link documents found or loaded".format(rule_type), warn_icon=True)
            return None
        
        if len(link_instances) == 1:
            # Only one link, use it automatically
            Debug.WriteLine("Auto-selecting single {} link: {}".format(rule_type, link_instances[0].Name))
            return link_instances[0]
        
        # Multiple links, let user choose
        selected_link = wpfforms.SelectFromList.show(
            context=link_instances,
            name_attr='Name',
            title="Select {} Link Document".format(rule_type),
            multiselect=False,
            show_min_button=False,
            show_maxrestore_button=False,
            owner=get_mainwindow()
        )
        
        if selected_link:
            return selected_link[0]  # SelectFromList returns a list even for single selection
        
        return None
    
    def execute_rule(self, rule, link_instance):
        # type: (object, DB.RevitLinkInstance) -> bool
        """Execute the selected rule with validation workflow."""
        try:
            # Get display name safely
            display_name = getattr(rule, 'display_name', rule.rule_id)
            
            Debug.WriteLine("Executing rule: {} ({})".format(display_name, rule.rule_id))
            Debug.WriteLine("Using link document: {}".format(link_instance.Name))
            
            # Collect elements using the rule
            arc_elements = rule.collect_arc_elements(link_instance)
            mass_elements = rule.collect_mass_elements(self.doc)
            
            Debug.WriteLine("Found {} ARC elements and {} mass elements".format(
                len(arc_elements), len(mass_elements)))
            
            if not arc_elements and not mass_elements:
                alert("No elements found for rule: {}".format(display_name), warn_icon=True)
                return False
            
            # Setup validation workflow
            tolerance_mm = rule.tolerance_mm
            wf = ValidationWorkflow()
            excel_reporter = ExcelReporter()
            html_reporter = HtmlReporter()
            
            # Determine element names based on rule type
            if rule.rule_id.startswith("HG_"):
                arc_name = "HG ARC Elements"
                mass_name = "HG Mass Elements"
            elif rule.rule_id.startswith("O1_"):
                arc_name = "O1 ARC Elements"
                mass_name = "O1 Mass Elements"
            else:
                arc_name = "ARC Elements"
                mass_name = "Mass Elements"
            
            # Execute validation
            result = wf.execute_validation(
                arc_elements=arc_elements,
                mass_elements=mass_elements,
                arc_name=arc_name,
                mass_name=mass_name,
                tolerance_mm=tolerance_mm,
                arc_guid_param=rule.arc_guid_param_spec,
                mass_guid_param=rule.mass_guid_param_spec,
                html_reporter=html_reporter,
                excel_reporter=excel_reporter,
                rule_name=display_name,
                extra_columns=rule.extra_excel_columns,
            )
            
            # Report results
            if result.has_issues:
                Debug.WriteLine("\n❌ VALIDATION FAILED - Issues found!")
                alert("Validation completed with issues. Check output for details.", warn_icon=True)
            else:
                Debug.WriteLine("\n✅ VALIDATION PASSED - All checks passed!")
                alert("Validation completed successfully!", title="Success")
            
            return True
            
        except Exception as ex:
            Debug.WriteLine("Error executing rule: {}".format(str(ex)))
            alert("Error executing rule: {}".format(str(ex)), warn_icon=True)
            return False
    
    def run(self):
        # type: () -> None
        """Main execution flow - select rule first, then link, then execute."""
        try:
            # Step 1: Select rule (HG or O1)
            rule = self.select_rule()
            if not rule:
                return
            
            # Step 2: Select appropriate link document based on rule
            link_instance = self.select_link_document(rule.rule_id)
            if not link_instance:
                return
            
            # Step 3: Execute rule
            self.execute_rule(rule, link_instance)
            
        except Exception as ex:
            Debug.WriteLine("Error in rule executor: {}".format(str(ex)))
            alert("Error in rule executor: {}".format(str(ex)), warn_icon=True)


def main():
    """Entry point for the rule executor."""
    Debug.WriteLine("Starting rule executor (direct import mode)")
    executor = RuleExecutor()
    executor.run()


if __name__ == "__main__":
    main()
