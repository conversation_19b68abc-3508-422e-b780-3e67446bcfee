# coding: utf-8
"""Excel File Comparison and Merging Tool"""
import clr
import System
import os
from pyrevit import script
from DCMvn.core import get_output
from DCMvn.io import pick_excel_file, save_excel_file
from DCMvn.forms import alert
from MunichRE.excel_reader import MiniExcel

clr.AddReference("System.Collections.Specialized")
from System.Collections.Specialized import OrderedDictionary

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

output = get_output()
logger = script.get_logger()

def read_excel_data(file_path, sheet_name=None):
    """
    Read Excel data using MiniExcel library.

    Args:
        file_path (str): Path to the Excel file
        sheet_name (str): Name of the sheet to read (optional)

    Returns:
        list: List of dictionaries containing the Excel data
    """
    try:
        # Import required framework types
        from DCMvn.core.framework import IDictionary

        if sheet_name:
            data = MiniExcel.Query(file_path, useHeaderRow=True, sheetName=sheet_name).Cast[IDictionary[str, object]]()
        else:
            data = MiniExcel.Query(file_path, useHeaderRow=True).Cast[IDictionary[str, object]]()

        # Convert to list of dictionaries for easier processing
        result = []
        column_names_set = set()

        # First pass: collect all possible column names from all rows
        for row in data:
            # Try different methods to get column names
            try:
                # Method 1: Try using dir() to get all non-private attributes
                all_attrs = [attr for attr in dir(row)
                           if not attr.startswith('_')
                           and not attr.startswith('get_')
                           and not attr.startswith('set_')
                           and attr not in ['Keys', 'Values', 'Count', 'Item', 'ContainsKey', 'TryGetValue']]

                # Filter out methods and keep only properties that might be column names
                for attr in all_attrs:
                    try:
                        value = getattr(row, attr)
                        # If we can get a value and it's not a method, it's likely a column
                        if not callable(value):
                            column_names_set.add(attr)
                    except Exception:
                        continue

            except Exception:
                continue

        # Convert set to list for consistent ordering
        column_names = sorted(list(column_names_set))

        if not column_names:
            logger.error("No column names found in Excel file: {}".format(file_path))
            return []

        # Second pass: extract data using discovered column names
        for row in data:
            row_dict = {}
            for column_name in column_names:
                try:
                    value = getattr(row, column_name)
                    row_dict[str(column_name)] = value if value is not None else ""
                except AttributeError:
                    # Handle case where column might not exist in this row
                    row_dict[str(column_name)] = ""
            result.append(row_dict)

        return result
    except Exception as e:
        logger.error("Error reading Excel file {}: {}".format(file_path, str(e)))
        return []

def get_sheet_names(file_path):
    """
    Get all sheet names from an Excel file.
    
    Args:
        file_path (str): Path to the Excel file
    
    Returns:
        list: List of sheet names
    """
    try:
        return list(MiniExcel.GetSheetNames(file_path))
    except Exception as e:
        logger.error("Error getting sheet names from {}: {}".format(file_path, str(e)))
        return []

def select_sheet(file_path, file_description):
    """
    Allow user to select a sheet from the Excel file.
    
    Args:
        file_path (str): Path to the Excel file
        file_description (str): Description for the file selection dialog
    
    Returns:
        str: Selected sheet name or None if cancelled
    """
    sheet_names = get_sheet_names(file_path)
    if not sheet_names:
        alert("No sheets found in {}".format(file_description))
        return None
    
    if len(sheet_names) == 1:
        return sheet_names[0]
    
    from DCMvn.forms.wpfforms import SelectFromList
    selected_sheet = SelectFromList.show(
        context=sheet_names,
        title="Select Sheet from {}".format(file_description),
        width=400,
        height=300
    )
    
    return selected_sheet

def is_empty_or_null(value):
    """
    Check if a value is empty, null, or whitespace.
    
    Args:
        value: Value to check
    
    Returns:
        bool: True if empty/null, False otherwise
    """
    if value is None:
        return True
    
    str_value = str(value).strip()
    return str_value == "" or str_value.lower() in ["null", "none", "n/a", "#n/a"]

def compare_and_filter_excel_files():
    """
    Main function to compare two Excel files and generate filtered output.
    Compares an old Excel file (baseline) with a new Excel file (updated version).
    """
    try:
        # Step 1: Select old Excel file (baseline)
        output.print_html('<h3>Step 1: Select Old Excel File (Baseline)</h3>')
        old_file_path = pick_excel_file(title="Select Old Excel File (Baseline) for Comparison")
        if not old_file_path:
            output.print_html('<strong style="color:red;">✗ No old Excel file selected. Exiting.</strong>')
            return

        output.print_html('<strong style="color:green;">✓ Old file selected:</strong> {}'.format(os.path.basename(old_file_path)))

        # Select sheet from old file
        old_sheet = select_sheet(old_file_path, "Old Excel File (Baseline)")
        if not old_sheet:
            output.print_html('<strong style="color:red;">✗ No sheet selected from old file. Exiting.</strong>')
            return

        # Step 2: Select new Excel file (updated version)
        output.print_html('<h3>Step 2: Select New Excel File (Updated Version)</h3>')
        new_file_path = pick_excel_file(title="Select New Excel File (Updated Version) for Comparison")
        if not new_file_path:
            output.print_html('<strong style="color:red;">✗ No new Excel file selected. Exiting.</strong>')
            return

        output.print_html('<strong style="color:green;">✓ New file selected:</strong> {}'.format(os.path.basename(new_file_path)))

        # Select sheet from new file
        new_sheet = select_sheet(new_file_path, "New Excel File (Updated Version)")
        if not new_sheet:
            output.print_html('<strong style="color:red;">✗ No sheet selected from new file. Exiting.</strong>')
            return

        # Step 3: Read data from both files
        output.print_html('<h3>Step 3: Reading Excel Data</h3>')
        old_data = read_excel_data(old_file_path, old_sheet)
        new_data = read_excel_data(new_file_path, new_sheet)

        if not old_data:
            output.print_html('<strong style="color:red;">✗ No data found in old Excel file.</strong>')
            return

        if not new_data:
            output.print_html('<strong style="color:red;">✗ No data found in new Excel file.</strong>')
            return

        output.print_html('<strong style="color:green;">✓ Old file data loaded:</strong> {} records'.format(len(old_data)))
        output.print_html('<strong style="color:green;">✓ New file data loaded:</strong> {} records'.format(len(new_data)))
        
        # Check for required columns
        old_columns = set(old_data[0].keys()) if old_data else set()
        new_columns = set(new_data[0].keys()) if new_data else set()

        # Look for GUID column (case-insensitive)
        guid_col_old = None
        guid_col_new = None

        for col in old_columns:
            if col.lower() in ['guid', 'id', 'globalid', 'global_id']:
                guid_col_old = col
                break

        for col in new_columns:
            if col.lower() in ['guid', 'id', 'globalid', 'global_id']:
                guid_col_new = col
                break

        if not guid_col_old:
            output.print_html('<strong style="color:red;">✗ No GUID column found in old file. Looking for: Guid, ID, GlobalId, Global_Id</strong>')
            return

        if not guid_col_new:
            output.print_html('<strong style="color:red;">✗ No GUID column found in new file. Looking for: Guid, ID, GlobalId, Global_Id</strong>')
            return

        # Look for Approver and Reason columns in both files (prioritize new file, but check old file too)
        approver_col_new = None
        reason_col_new = None
        approver_col_old = None
        reason_col_old = None

        for col in new_columns:
            if col.lower() in ['approver', 'approved_by', 'approvedby']:
                approver_col_new = col
            elif col.lower() in ['reason', 'comment', 'remarks', 'note']:
                reason_col_new = col

        for col in old_columns:
            if col.lower() in ['approver', 'approved_by', 'approvedby']:
                approver_col_old = col
            elif col.lower() in ['reason', 'comment', 'remarks', 'note']:
                reason_col_old = col

        if not approver_col_new and not approver_col_old:
            output.print_html('<strong style="color:orange;">⚠ No Approver column found in either file. Looking for: Approver, Approved_By, ApprovedBy</strong>')

        if not reason_col_new and not reason_col_old:
            output.print_html('<strong style="color:orange;">⚠ No Reason column found in either file. Looking for: Reason, Comment, Remarks, Note</strong>')

        output.print_html('<strong>Column mapping:</strong>')
        output.print_html('- Old file GUID column: <strong>{}</strong>'.format(guid_col_old))
        output.print_html('- New file GUID column: <strong>{}</strong>'.format(guid_col_new))
        if approver_col_new:
            output.print_html('- New file Approver column: <strong>{}</strong>'.format(approver_col_new))
        if approver_col_old:
            output.print_html('- Old file Approver column: <strong>{}</strong>'.format(approver_col_old))
        if reason_col_new:
            output.print_html('- New file Reason column: <strong>{}</strong>'.format(reason_col_new))
        if reason_col_old:
            output.print_html('- Old file Reason column: <strong>{}</strong>'.format(reason_col_old))
        
        # Step 4: Process data and merge records from both files
        output.print_html('<h3>Step 4: Processing and Merging Data</h3>')

        # Create GUID dictionaries for efficient lookup
        old_guids_dict = {}
        for row in old_data:
            guid = str(row[guid_col_old]).strip() if row[guid_col_old] else ""
            if guid:
                old_guids_dict[guid] = row

        new_guids_dict = {}
        for row in new_data:
            guid = str(row[guid_col_new]).strip() if row[guid_col_new] else ""
            if guid:
                new_guids_dict[guid] = row

        # Get all unique GUIDs from both files
        all_guids = set(old_guids_dict.keys()) | set(new_guids_dict.keys())

        merged_records = []
        stats = {
            'total_unique_guids': len(all_guids),
            'only_in_old': 0,
            'only_in_new': 0,
            'in_both_files': 0,
            'preserved_approver_reason': 0
        }

        for guid in all_guids:
            old_row = old_guids_dict.get(guid)
            new_row = new_guids_dict.get(guid)

            # Create merged record starting with new data if available, otherwise old data
            if new_row:
                merged_record = dict(new_row)
                if old_row:
                    stats['in_both_files'] += 1
                else:
                    stats['only_in_new'] += 1
            else:
                merged_record = dict(old_row)
                stats['only_in_old'] += 1

            # Ensure the GUID column is standardized as "Guid"
            if guid_col_new and guid_col_new != "Guid" and guid_col_new in merged_record:
                merged_record["Guid"] = merged_record.pop(guid_col_new)
            elif guid_col_old and guid_col_old != "Guid" and guid_col_old in merged_record:
                merged_record["Guid"] = merged_record.pop(guid_col_old)
            elif "Guid" not in merged_record:
                merged_record["Guid"] = guid

            # Preserve Approver and Reason values with priority logic
            # Priority: New file values > Old file values > Empty
            approver_preserved = False
            reason_preserved = False

            # Handle Approver column
            if approver_col_new and new_row:
                new_approver = new_row.get(approver_col_new, "")
                if not is_empty_or_null(new_approver):
                    merged_record['Approver'] = new_approver
                    approver_preserved = True
                elif approver_col_old and old_row:
                    old_approver = old_row.get(approver_col_old, "")
                    if not is_empty_or_null(old_approver):
                        merged_record['Approver'] = old_approver
                        approver_preserved = True
            elif approver_col_old and old_row:
                old_approver = old_row.get(approver_col_old, "")
                if not is_empty_or_null(old_approver):
                    merged_record['Approver'] = old_approver
                    approver_preserved = True

            # Handle Reason column
            if reason_col_new and new_row:
                new_reason = new_row.get(reason_col_new, "")
                if not is_empty_or_null(new_reason):
                    merged_record['Reason'] = new_reason
                    reason_preserved = True
                elif reason_col_old and old_row:
                    old_reason = old_row.get(reason_col_old, "")
                    if not is_empty_or_null(old_reason):
                        merged_record['Reason'] = old_reason
                        reason_preserved = True
            elif reason_col_old and old_row:
                old_reason = old_row.get(reason_col_old, "")
                if not is_empty_or_null(old_reason):
                    merged_record['Reason'] = old_reason
                    reason_preserved = True

            # Ensure Approver and Reason columns exist in output
            if 'Approver' not in merged_record:
                merged_record['Approver'] = ""
            if 'Reason' not in merged_record:
                merged_record['Reason'] = ""

            if approver_preserved or reason_preserved:
                stats['preserved_approver_reason'] += 1

            merged_records.append(merged_record)
        
        # Display statistics
        output.print_html('<strong>Processing Statistics:</strong>')
        output.print_html('- Total unique GUIDs across both files: <strong>{}</strong>'.format(stats['total_unique_guids']))
        output.print_html('- Records only in old file: <strong style="color:orange;">{}</strong>'.format(stats['only_in_old']))
        output.print_html('- Records only in new file: <strong style="color:blue;">{}</strong>'.format(stats['only_in_new']))
        output.print_html('- Records in both files: <strong style="color:green;">{}</strong>'.format(stats['in_both_files']))
        output.print_html('- Records with preserved Approver/Reason data: <strong style="color:purple;">{}</strong>'.format(stats['preserved_approver_reason']))
        output.print_html('- <strong>Total records for output: {}</strong>'.format(len(merged_records)))

        if not merged_records:
            output.print_html('<strong style="color:orange;">⚠ No records found for export. No output file will be generated.</strong>')
            return

        # Step 5: Generate output Excel file
        output.print_html('<h3>Step 5: Generating Merged Excel File</h3>')

        output_file_path = save_excel_file(title="Save Merged Excel Comparison Results")
        if not output_file_path:
            output.print_html('<strong style="color:red;">✗ No output file selected. Results not saved.</strong>')
            return

        # Convert merged records to OrderedDictionary format for MiniExcel with specific column order
        output_data = []
        # Define the desired column order
        desired_column_order = ["Guid", "Category", "CorrectLevel", "CurrentLevel", "Approver", "Reason"]

        for record in merged_records:
            row_data = OrderedDictionary()

            # First, add columns in the desired order if they exist
            for col_name in desired_column_order:
                if col_name in record:
                    row_data[col_name] = record[col_name] if record[col_name] is not None else ""
                else:
                    # Add empty column if it doesn't exist but is in desired order
                    row_data[col_name] = ""

            # Then add any remaining columns that weren't in the desired order
            for key, value in record.items():
                if key not in desired_column_order:
                    row_data[key] = value if value is not None else ""

            output_data.append(row_data)
        
        # Save to Excel
        if os.path.exists(output_file_path):
            os.remove(output_file_path)
        
        MiniExcel.SaveAs(output_file_path, output_data)

        output.print_html('<strong style="color:green;">✓ Merged results saved successfully:</strong>')
        output.print_html('<strong>{}</strong>'.format(output_file_path))

        # Ask user if they want to open the output folder
        open_folder = alert("Excel comparison and merge completed successfully!\n\nOutput file: {}\n\nOpen containing folder?".format(os.path.basename(output_file_path)), yes=True, no=True)
        if open_folder:
            folder_path = os.path.dirname(output_file_path)
            os.startfile(folder_path)

    except Exception as e:
        logger.error("Error in Excel comparison: {}".format(str(e)))
        output.print_html('<strong style="color:red;">✗ Error during Excel comparison: {}</strong>'.format(str(e)))

# Main execution
if __name__ == "__main__":
    output.print_html('<h2>Excel File Comparison and Merging Tool</h2>')
    output.print_html('<p>This tool compares an old Excel file (baseline) with a new Excel file (updated version), merges all unique GUIDs, and preserves Approver and Reason data from both files.</p>')

    compare_and_filter_excel_files()

    output.close_others()
