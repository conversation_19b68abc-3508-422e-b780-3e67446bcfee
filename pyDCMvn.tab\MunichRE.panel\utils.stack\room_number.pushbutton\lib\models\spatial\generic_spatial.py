# coding: utf-8
from DCMvn.core import DB
from DCMvn.revit.geometry import GetElementMergedSolid, GetElementMeshes
from .base_spatial import BaseSpatial
from ..document_wrapper import DocumentWrapper

class GenericSpatial(BaseSpatial):
    """Wrapper for IFC-imported generic spatial elements (DirectShape, GenericModel).

    This class provides specialized functionality for IFC-imported spatial elements
    that represent spaces, rooms, or zones. It handles custom parameters commonly
    found in IFC imports and provides geometry-based calculations.
    """

    def __init__(self, generic_element, document_wrapper):
        # type: (DB.Element, DocumentWrapper) -> None
        """Initialize a Generic Spatial wrapper.

        Args:
            generic_element (DB.Element): The generic spatial element (DirectShape, GenericModel)
            document_wrapper (DocumentWrapper): Optional document wrapper for transformation
        """
        super(GenericSpatial, self).__init__(generic_element, document_wrapper, None)

    def is_point_inside(self, point):
        # type: (DB.XYZ) -> bool
        """Check if point is inside this generic spatial element using proper geometry methods.
        
        Uses solid or mesh geometry depending on what's available, with bounding box fallback.
        
        Args:
            point (DB.XYZ): Point to check
            
        Returns:
            bool: True if point is inside the spatial element, False otherwise
        """
        try:
            geometry = self.geometry
            if geometry:
                # Import here to avoid circular imports
                from ...utils.geometry_utils import is_point_in_solid, is_point_in_meshes

                if isinstance(geometry, DB.Solid):
                    return is_point_in_solid(point, geometry)
                elif isinstance(geometry, list):
                    return is_point_in_meshes(point, geometry)
            
            # Fallback to bounding box if geometry not available
            bbox = self.bounding_box
            if bbox:
                return (bbox.Min.X <= point.X <= bbox.Max.X and
                       bbox.Min.Y <= point.Y <= bbox.Max.Y and
                       bbox.Min.Z <= point.Z <= bbox.Max.Z)
                       
            return False
        except:  # noqa
            return False

    def __str__(self):
        # type: () -> str
        """String representation of the generic spatial element.

        Returns:
            str: String representation
        """
        return "Generic Spatial (Id={}, Category={})".format(
            self._get_elementid_value(self.id), self.get_category_name())
