{"cells": [{"cell_type": "code", "execution_count": null, "id": "0", "metadata": {}, "outputs": [], "source": ["# MEP Pipes Analysis\n", "# API: http://localhost:48884/pyDCMvn.MunichRE/api/mep/pipes\n", "\n", "import json\n", "import requests\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Fetch data from API\n", "api_url = \"http://localhost:48884/pyDCMvn.MunichRE/api/mep/pipes\"\n", "\n", "try:\n", "    response = requests.get(api_url, timeout=30)\n", "    \n", "    if response.status_code == 200:\n", "        data = response.json()\n", "        \n", "        if 'pipes' in data and data['pipes']:\n", "            pipes_df = pd.DataFrame(data['pipes'])\n", "        else:\n", "            pipes_df = pd.DataFrame()\n", "    else:\n", "        pipes_df = pd.DataFrame()\n", "        \n", "except:\n", "    pipes_df = pd.DataFrame()\n", "\n", "# Display the dataframe\n", "pipes_df"]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["# Analysis Summary\n", "\n", "if not pipes_df.empty:\n", "    # Basic info\n", "    display(pipes_df.info())\n", "    \n", "    # Summary statistics\n", "    display(pipes_df.describe())\n", "    \n", "    # System distribution\n", "    if 'system_name' in pipes_df.columns:\n", "        system_summary = pipes_df['system_name'].value_counts()\n", "        display(system_summary)\n", "    \n", "    # Length by system\n", "    if 'system_name' in pipes_df.columns and 'length' in pipes_df.columns:\n", "        length_by_system = pipes_df.groupby('system_name')['length'].agg(['count', 'sum', 'mean']).round(2)\n", "        display(length_by_system)\n", "    \n", "    # Visualizations\n", "    if 'system_name' in pipes_df.columns:\n", "        plt.figure(figsize=(10, 6))\n", "        pipes_df['system_name'].value_counts().plot(kind='bar')\n", "        plt.title('Pipes by System')\n", "        plt.xticks(rotation=45)\n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    if 'diameter' in pipes_df.columns:\n", "        plt.figure(figsize=(10, 4))\n", "        pipes_df['diameter'].dropna().hist(bins=20, edgecolor='black')\n", "        plt.title('Pipe Diameter Distribution')\n", "        plt.xlabel('Diameter')\n", "        plt.tight_layout()\n", "        plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Ifc312", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}, "polyglot_notebook": {"kernelInfo": {"defaultKernelName": "csharp", "items": [{"aliases": [], "name": "csharp"}]}}}, "nbformat": 4, "nbformat_minor": 5}