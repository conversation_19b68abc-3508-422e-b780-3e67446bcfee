import clr
from DCMvn.core import DB, HOST_APP
from DCMvn.core.framework import System
from DCMvn.forms.wpfforms import SelectFromList
from DCMvn.forms import alert
from space_props import GenericSpaceProperties

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

class CachedCollector:
    """
    CachedCollector class to store and manage cached elements in Revit.
    This class collects various Revit elements and stores them in dictionaries
    for quick access.
    """

    def __init__(self):
        self.__level_dicts = self._collect_levels()
        self.__views_dicts = self._collect_views()
        self.__link_space = self._collect_linked_space()
        self.__linked_generic_spaces = self.collect_linked_generic_spaces()
        
    @property
    def linked_id(self):
        return self.__link_space[2].Id
    
    @property
    def level_dicts(self):
        return self.__level_dicts
    
    @property
    def views_dicts(self):
        return self.__views_dicts
    
    @property
    def link_transform(self):
        return self.__link_space[0]
    
    @property
    def link_generic_space_doc(self):
        return self.__link_space[1]
    
    @property
    def linked_generic_spaces(self):
        """
        Get the linked generic spaces from the cached collector.
        
        Returns:
            list: A list of linked generic spaces.
        """
        return self.__linked_generic_spaces
    
    def collect_linked_generic_spaces(self, active_view=False):
        """
        Collect linked generic space from the Revit document.
        
        Args:
            active_view (bool): If True, collect generic spaces from the active view.
        
        Returns:
            tuple: A tuple containing the linked generic space, its transform, and the linked document.
        """
        if active_view:
            return DB.FilteredElementCollector(HOST_APP.doc, HOST_APP.active_view.Id, self.linked_id) \
                .OfCategory(DB.BuiltInCategory.OST_GenericModel) \
                .WhereElementIsNotElementType() \
                .Where(lambda x: x.get_Parameter(GenericSpaceProperties.ExportToIfcAs).AsString() == GenericSpaceProperties.IfcSpaceType) \
                .ToList()
        return DB.FilteredElementCollector(self.link_generic_space_doc) \
            .OfCategory(DB.BuiltInCategory.OST_GenericModel) \
            .WhereElementIsNotElementType() \
            .Where(lambda x: x.get_Parameter(GenericSpaceProperties.ExportToIfcAs).AsString() == GenericSpaceProperties.IfcSpaceType) \
            .ToList()

    def _collect_levels(self):
        return {
            i.Name: i
            for i in DB.FilteredElementCollector(HOST_APP.doc)
            .OfClass(DB.Level)
            .WhereElementIsNotElementType()
            .ToElements()
        }

    def _collect_views(self):
        return {
            i.Name: i
            for i in DB.FilteredElementCollector(HOST_APP.doc)
            .OfClass(DB.View)
            .Where(lambda x: x.ViewType == DB.ViewType.FloorPlan)
            .ToList()
        }

    def _collect_linked_space(self):
        link_generic_spaces = DB.FilteredElementCollector(HOST_APP.doc) \
                                .OfClass(DB.RevitLinkInstance) \
                                .WhereElementIsNotElementType() \
                                .ToElements()
        link_generic_space = SelectFromList.show(link_generic_spaces, title="Select Linked Generic Space", name_attr="Name", multiselect=False)
        if not link_generic_space:
            alert("No linked selected", exitscript=True, warn_icon=True)
        link_transform = link_generic_space.GetTotalTransform() if link_generic_space else None
        link_generic_space_doc = link_generic_space.GetLinkDocument() if link_generic_space else None
        # link_transform = DB.Transform.Identity
        # link_generic_space_doc = HOST_APP.doc
        # link_generic_space = None

        return link_transform, link_generic_space_doc, link_generic_space