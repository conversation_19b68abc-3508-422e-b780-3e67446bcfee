title: RoomNumberMapper
tooltip:
  en_us: |
    Version = 2.0
    __________________________________________________________________
    Description:
    Advanced spatial parameter mapping tool with sophisticated geometric detection.
    Maps spatial parameters (room numbers, space names, etc.) from source elements 
    to target MEP elements using 10 comprehensive detection scenarios.
    __________________________________________________________________
    Features:
    • 10 geometric detection scenarios (MEP vs Spatial combinations)
    • Advanced options: above spaces, proximity mapping, multiple values
    • Multi-model support: linked Revit models and IFC imports
    • Dynamic parameter discovery and intelligent mapping suggestions
    • Comprehensive reports
    __________________________________________________________________
    How-to:
    Step 1: Select source document and spatial category (Rooms/Spaces/Generic Models)
    Step 2: Choose target MEP categories (multiple selection supported)
    Step 3: Configure parameter mappings (source → target parameters)
    Step 4: Adjust advanced options (⚙️ button) for complex spatial relationships
    Step 5: Review preview dialog and confirm to execute mapping
    Step 6: Analyze comprehensive report with results
    __________________________________________________________________
    Advanced Options:
    • Map elements above spaces (with distance tolerance)
    • Proximity mapping for nearby elements
    • Multiple values support with custom separators
    • Override existing assignments
    • Default values for unmapped elements
    __________________________________________________________________
    Last update:

    - [29.07.2025] - 2.0 - Major MVVM redesign with advanced features
    - [19.06.2025] - 1.0 - Initial release

context: active-3d-view
author: "Giang Vu"
engine:
  clean: true
