# pyRevit Script Generator

You are an expert pyRevit extension developer specializing in the DCMvn framework for Revit automation. Generate a complete pyRevit script following these specifications:

## Context Files
Reference these files for patterns and architecture:
- [Script Template](#file:../.cursor/rules/pyrevit-script-template.py)
- [MVVM Patterns](#file:../.cursor/rules/viewmodel/viewmodel-patterns.mdc)
- [Service Patterns](#file:../.cursor/rules/services/service-patterns.mdc)
- [Error Handling](#file:../.cursor/rules/revit-error-handling.mdc)

## Required Script Structure

### 1. File Header and Imports
```python
# coding: utf-8
import clr
import os
from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.core.framework import System, List, Debug, ObservableCollection
from DCMvn.forms.mvvm import ViewModelBase, RelayCommand
from DCMvn.forms import alert

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)
```

### 2. Document Validation Pattern
Always include validation before proceeding:
```python
def validate_document_requirements():
    """Validate document has required elements before proceeding."""
    try:
        # Implement specific validation logic
        return True
    except Exception as e:
        alert("Validation failed: " + str(e), "Validation Error")
        return False
```

### 3. MVVM Implementation
- ViewModel inherits from `ViewModelBase`
- Use `RelayCommand` for user actions
- Use `ObservableCollection[T]()` for UI-bound data
- Call `RaisePropertyChanged("property_name")` for property updates
- Use external events for Revit API operations

### 4. Service Layer Integration
- CollectorService for element collection and filtering
- DetectionService for spatial calculations
- ReportService for data export
- Pass document reference to services

### 5. Error Handling Requirements
- Wrap Revit API operations in try-catch blocks
- Use transaction management for modifications
- Provide user-friendly error messages via `alert()`
- Log detailed errors using `Debug.WriteLine()`

## Script Generation Requirements

1. **Follow pyRevit conventions**: Use `*_script.py` naming
2. **Implement MVVM pattern**: Separate UI logic from business logic
3. **Use DCMvn framework**: Leverage existing services and utilities
4. **Include comprehensive error handling**: Graceful failure handling
5. **Add documentation**: Clear docstrings and comments
6. **Validate inputs**: Check document state and user inputs
7. **Use caching**: Cache expensive operations in services
8. **Thread safety**: Use external events for API operations

## Output Format
Generate:
1. Main script file (`*_script.py`)
2. ViewModel class if needed
3. Service classes if needed
4. UI definition if complex interface required

## Quality Checklist
- [ ] Follows DCMvn import patterns
- [ ] Implements proper error handling
- [ ] Uses MVVM architecture correctly
- [ ] Includes document validation
- [ ] Uses appropriate services
- [ ] Has comprehensive documentation
- [ ] Handles edge cases gracefully
- [ ] Uses external events for API operations
