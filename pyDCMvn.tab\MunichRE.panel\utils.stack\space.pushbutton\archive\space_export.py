# coding: utf8
import clr
import os

from DCMvn.core import DB, HOST_APP
from DCMvn.io import save_excel_file
from DCMvn.forms import alert
from DCMvn.core.framework import System
from DCMvn.coreutils.assemblyhandler import load_miniexcel

# Load MiniExcel assemblies
load_miniexcel()
from MiniExcelLibs import MiniExcel

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

# Add Dictionary and OrderedDictionary support
clr.AddReference("System.Collections.Specialized")
from System.Collections.Generic import Dictionary
from System.Collections.Specialized import OrderedDictionary

doc = HOST_APP.doc
uidoc = HOST_APP.uidoc

# MreProjectIdentifier = "0061721_MRE"
MreProjectIdentifier = "_AWA_"
IfcSpaceTypeParameter = "Export to IFC As"
IfcSpaceTypeValue = "IfcSpaceType"
# LevelParameter = "AW_ARC.Geschossnummer"
LevelParameter = "IfcDecomposes"
# SpaceNameParameter = "AW_ARC.Raumname"
SpaceNameParameter = "MRE_ARC.Raumname"
# SpaceNumberParameter = "AW_ARC.Raumnummer ARC"
# SpaceNumberParameter = "AW_ARC.Raumnummer"
SpaceNumberParameter = "MRE_ARC.Raunummer Türschild"
# SpaceAreaParameter = "AW_ARC.Raumfläche Berechnet WoFlV"
SpaceAreaParameter_GrossFloor = "Qto_SpaceBaseQuantities.GrossFloorArea"
SpaceAreaParameter_GrossCeiling = "Qto_SpaceBaseQuantities.GrossCeilingArea"
SpaceAreaParameter_NetFloor = "Qto_SpaceBaseQuantities.NetFloorArea"
SpaceAreaParameter_NetCeiling = "Qto_SpaceBaseQuantities.NetCeilingArea"
SpaceAreaParameter = SpaceAreaParameter_NetFloor

# Volume parameters for IFC spaces
SpaceVolumeParameter_GrossVolume = "Qto_SpaceBaseQuantities.GrossVolume"
SpaceVolumeParameter_NetVolume = "Qto_SpaceBaseQuantities.NetVolume"
SpaceVolumeParameter = SpaceVolumeParameter_NetVolume

# Perimeter parameters for IFC spaces
SpacePerimeterParameter_GrossPerimeter = "Qto_SpaceBaseQuantities.GrossPerimeter"
SpacePerimeterParameter_NetPerimeter = "Qto_SpaceBaseQuantities.NetPerimeter"
SpacePerimeterParameter = SpacePerimeterParameter_NetPerimeter

# Perimeter parameters for IFC spaces
SpacePerimeterParameter_GrossPerimeter = "Qto_SpaceBaseQuantities.GrossPerimeter"
SpacePerimeterParameter_NetPerimeter = "Qto_SpaceBaseQuantities.NetPerimeter"
SpacePerimeterParameter = SpacePerimeterParameter_NetPerimeter


def get_link_data(document):
    link_instance = (DB.FilteredElementCollector(document)
                     .OfClass(DB.RevitLinkInstance)
                     .WhereElementIsNotElementType()
                     .FirstOrDefault(lambda x: x.Name.Contains(MreProjectIdentifier)))  # type: DB.RevitLinkInstance

    return link_instance.GetLinkDocument()

def get_generic_spaces(document):
    collector = DB.FilteredElementCollector(document)
    spaces = (collector
              .OfCategory(DB.BuiltInCategory.OST_GenericModel)
              .WhereElementIsNotElementType()
              .Where(lambda x: x.LookupParameter(IfcSpaceTypeParameter).AsString() == IfcSpaceTypeValue)
              .ToList())
    return spaces

def get_space_data(space):
    space_guid = space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
    space_name = space.get_Parameter(DB.BuiltInParameter.ROOM_NAME).AsString()
    space_number = space.get_Parameter(DB.BuiltInParameter.ROOM_NUMBER).AsString()
    space_area = space.get_Parameter(DB.BuiltInParameter.ROOM_AREA).AsDouble()
    # Convert area to m² with 3 decimal places
    space_area_formatted = "{:.3f} m2".format(DB.UnitUtils.ConvertFromInternalUnits(space_area, DB.UnitTypeId.SquareMeters))
    space_level = space.get_Parameter(DB.BuiltInParameter.LEVEL_NAME).AsString()

    # Get space volume
    space_volume = space.get_Parameter(DB.BuiltInParameter.ROOM_VOLUME).AsDouble()
    # Convert volume to m³ with 3 decimal places
    space_volume_formatted = "{:.3f} m3".format(DB.UnitUtils.ConvertFromInternalUnits(space_volume, DB.UnitTypeId.CubicMeters))

    # Get space perimeter
    space_perimeter_param = space.get_Parameter(DB.BuiltInParameter.ROOM_PERIMETER)
    if space_perimeter_param:
        space_perimeter = space_perimeter_param.AsDouble()
        # Convert perimeter to meters with 3 decimal places
        space_perimeter_formatted = "{:.3f} m".format(DB.UnitUtils.ConvertFromInternalUnits(space_perimeter, DB.UnitTypeId.Meters))
    else:
        space_perimeter = 0.0
        space_perimeter_formatted = "0.000 m"

    # Get space comments for status determination
    space_comments_param = space.get_Parameter(DB.BuiltInParameter.ALL_MODEL_INSTANCE_COMMENTS)
    space_comments = space_comments_param.AsString() if space_comments_param else ""

    # Determine status based on comments instead of LastChangedBy
    if space_comments and "Manual" in space_comments:
        status = "Manual Place or Edit"
    else:
        status = "Auto Place"

    return {
        space_guid: {
            "Space Name": space_name,
            "Space Number": space_number,
            "Guid": space_guid,
            "Level Reference": space_level,
            "Area": space_area_formatted,
            "Raw Area": space_area,  # Keep raw value for internal calculations
            "Volume": space_volume_formatted,
            "Raw Volume": space_volume,  # Keep raw value for internal calculations
            "Perimeter": space_perimeter_formatted,
            "Raw Perimeter": space_perimeter,  # Keep raw value for internal calculations
            "status": status,
            "Comments": space_comments  # Store comments for warning data enhancement
        }
    }
    
def get_ifcSpace_data(space):
    space_guid = space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
    space_name = space.LookupParameter(SpaceNameParameter).AsString()
    space_number = space.LookupParameter(SpaceNumberParameter).AsString()
    space_area = space.LookupParameter(SpaceAreaParameter).AsDouble()
    # Convert area to m² with 3 decimal places and Convert to Square Meters
    space_area_formatted = "{:.3f} m2".format(DB.UnitUtils.ConvertFromInternalUnits(space_area, DB.UnitTypeId.SquareMeters))
    space_level = space.LookupParameter(LevelParameter).AsString()

    # Get space volume
    space_volume_param = space.LookupParameter(SpaceVolumeParameter)
    if space_volume_param:
        space_volume = space_volume_param.AsDouble()
        # Convert volume to m³ with 3 decimal places
        space_volume_formatted = "{:.3f} m3".format(DB.UnitUtils.ConvertFromInternalUnits(space_volume, DB.UnitTypeId.CubicMeters))
    else:
        space_volume = 0.0
        space_volume_formatted = "0.000 m3"

    # Get space perimeter
    space_perimeter_param = space.LookupParameter(SpacePerimeterParameter)
    if space_perimeter_param:
        space_perimeter = space_perimeter_param.AsDouble()
        # Convert perimeter to meters with 3 decimal places
        space_perimeter_formatted = "{:.3f} m".format(DB.UnitUtils.ConvertFromInternalUnits(space_perimeter, DB.UnitTypeId.Meters))
    else:
        space_perimeter = 0.0
        space_perimeter_formatted = "0.000 m"

    return {
        space_guid: {
            "Space Name": space_name,
            "Space Number": space_number,
            "RGuid": space_guid,
            "Level Reference": space_level,
            "Area": space_area_formatted,
            "Raw Area": space_area,  # Keep raw value for internal calculations
            "Volume": space_volume_formatted,
            "Raw Volume": space_volume,  # Keep raw value for internal calculations
            "Perimeter": space_perimeter_formatted,
            "Raw Perimeter": space_perimeter,  # Keep raw value for internal calculations
            "status": "IFC Space"
        }
    }

def get_warning_data(warning):
    """
    Convert a FailureMessage warning to a dictionary with warning details
    """
    warning_data = {
        "Description": warning.GetDescriptionText(),
        "Severity": warning.GetSeverity().ToString(),
        "Resolution": warning.GetDefaultResolutionCaption() if warning.HasResolutions() else ""
    }
    
    # Get failing elements
    failing_elements = warning.GetFailingElements()
    additional_elements = warning.GetAdditionalElements()
    
    if failing_elements.Count > 0:
        element_ids = [str(elem_id.IntegerValue) for elem_id in failing_elements]
        warning_data["Failing Elements"] = " - ".join(element_ids)
    elif additional_elements.Count > 0:
        element_ids = [str(elem_id.IntegerValue) for elem_id in additional_elements]
        warning_data["Failing Elements"] = " - ".join(element_ids)
    else:
        warning_data["Failing Elements"] = ""
    
    return warning_data

def get_space_warnings(document):
    """
    Get all warnings from the document and map them to space elements
    """
    print("=== ANALYZING REVIT WARNINGS ===")

    # Get all warnings from document
    warnings = document.GetWarnings()
    print("Total warnings found: {}".format(len(warnings)))

    # Dictionary to store space ID -> list of warning descriptions
    space_warnings = {}

    # Get all MEP spaces for reference
    spaces = DB.FilteredElementCollector(document).OfCategory(DB.BuiltInCategory.OST_MEPSpaces).WhereElementIsNotElementType().ToElements()
    space_ids = set([space.Id.IntegerValue for space in spaces])

    # Create a mapping of space ID to space element for comment checking
    space_id_to_element = {space.Id.IntegerValue: space for space in spaces}
    
    warning_count = 0
    space_warning_count = 0
    
    for warning in warnings:
        warning_data = get_warning_data(warning)
        warning_count += 1
        
        # Get all failing and additional elements
        all_failing_elements = []
        
        failing_elements = warning.GetFailingElements()
        for elem_id in failing_elements:
            all_failing_elements.append(elem_id.IntegerValue)
        
        additional_elements = warning.GetAdditionalElements()
        for elem_id in additional_elements:
            all_failing_elements.append(elem_id.IntegerValue)
        
        # Check if any failing element is a space
        space_found = False
        for element_id in all_failing_elements:
            if element_id in space_ids:
                space_found = True
                if element_id not in space_warnings:
                    space_warnings[element_id] = []
                
                # Extract key information from warning description
                description = warning_data["Description"]
                severity = warning_data["Severity"]
                
                # Create a concise warning summary
                warning_summary = description
                if "overlap" in description.lower():
                    warning_summary = "Overlap"
                elif "duplicate" in description.lower():
                    warning_summary = "Duplicate Number"
                elif "enclosed" in description.lower() or "not properly enclosed" in description.lower():
                    # Check if space has "Not Enclosed" in comments for enhanced warning data
                    space_element = space_id_to_element.get(element_id)
                    if space_element:
                        comments_param = space_element.get_Parameter(DB.BuiltInParameter.ALL_MODEL_INSTANCE_COMMENTS)
                        comments = comments_param.AsString() if comments_param else ""
                        if comments and "Not Enclosed" in comments:
                            # Use the value from comments instead of default text
                            warning_summary = comments
                        else:
                            warning_summary = "Not Enclosed"
                    else:
                        warning_summary = "Not Enclosed"
                elif "redundant" in description.lower():
                    warning_summary = "Redundant"
                elif "unplaced" in description.lower():
                    warning_summary = "Unplaced"
                elif "area" in description.lower() and "computation" in description.lower():
                    warning_summary = "Area Computation Issue"
                elif "boundary" in description.lower():
                    warning_summary = "Boundary Issue"
                else:
                    # Use first 30 characters if no specific pattern
                    warning_summary = description[:30] + "..." if len(description) > 50 else description
                
                space_warnings[element_id].append(warning_summary)
        
        if space_found:
            space_warning_count += 1
    
    print("Warnings affecting spaces: {}".format(space_warning_count))
    print("Spaces with warnings: {}".format(len(space_warnings)))
    
    # Convert space warnings to GUID-based dictionary
    space_warnings_by_guid = {}
    
    for space in spaces:
        space_id = space.Id.IntegerValue
        if space_id in space_warnings:
            try:
                space_guid = space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
                if space_guid and space_guid.strip():
                    # Join warnings with semicolon
                    unique_warnings = list(set(space_warnings[space_id]))  # Remove duplicates
                    warning_text = ";".join(unique_warnings)
                    space_warnings_by_guid[space_guid] = warning_text
            except:
                continue  # Skip spaces without GUID
    
    print("Spaces with warnings (by GUID): {}".format(len(space_warnings_by_guid)))
    print("=" * 50)
    
    return space_warnings_by_guid

def create_ordered_row(key_value_pairs):
    """
    Create an OrderedDictionary from a list of (key, value) tuples to ensure column order
    """
    ordered_dict = OrderedDictionary()
    for key, value in key_value_pairs:
        ordered_dict[key] = value
    return ordered_dict

def find_space_matches(ifc_spaces_data, spaces_data):
    """
    Find matches between IFC and Revit spaces using multiple criteria:
    1. GUID match (highest priority)
    2. Number + Name match (medium priority)
    3. Name match only (lowest priority)
    
    Returns dictionaries with match information and match types
    """
    # Flatten data structures
    ifc_spaces_dict = {}
    revit_spaces_dict = {}
    
    for ifc_space_dict in ifc_spaces_data:
        for guid, data in ifc_space_dict.items():
            ifc_spaces_dict[guid] = data
    
    for revit_space_dict in spaces_data:
        for guid, data in revit_space_dict.items():
            revit_spaces_dict[guid] = data
    
    # Create lookup indices for Revit spaces
    revit_by_number_name = {}  # (number, name) -> [guids]
    revit_by_name = {}         # name -> [guids]
    
    for guid, data in revit_spaces_dict.items():
        # Index by number + name combination
        number_name_key = (data["Space Number"], data["Space Name"])
        if number_name_key not in revit_by_number_name:
            revit_by_number_name[number_name_key] = []
        revit_by_number_name[number_name_key].append(guid)
        
        # Index by name only
        name_key = data["Space Name"]
        if name_key not in revit_by_name:
            revit_by_name[name_key] = []
        revit_by_name[name_key].append(guid)
    
    # Track matches and their types
    ifc_to_revit_matches = {}  # ifc_guid -> (revit_guid, match_type)
    revit_matched = set()      # Track which Revit spaces have been matched
    
    # Phase 1: GUID matches (highest priority)
    for ifc_guid, ifc_data in ifc_spaces_dict.items():
        if ifc_guid in revit_spaces_dict:
            ifc_to_revit_matches[ifc_guid] = (ifc_guid, "GUID")
            revit_matched.add(ifc_guid)
    
    # Phase 2: Number + Name matches (medium priority)
    for ifc_guid, ifc_data in ifc_spaces_dict.items():
        if ifc_guid in ifc_to_revit_matches:
            continue  # Already matched by GUID
        
        number_name_key = (ifc_data["Space Number"], ifc_data["Space Name"])
        if number_name_key in revit_by_number_name:
            # Find first unmatched Revit space with this number+name
            for revit_guid in revit_by_number_name[number_name_key]:
                if revit_guid not in revit_matched:
                    ifc_to_revit_matches[ifc_guid] = (revit_guid, "NUMBER_NAME")
                    revit_matched.add(revit_guid)
                    break
    
    # Phase 3: Name-only matches (lowest priority)
    for ifc_guid, ifc_data in ifc_spaces_dict.items():
        if ifc_guid in ifc_to_revit_matches:
            continue  # Already matched
        
        name_key = ifc_data["Space Name"]
        if name_key in revit_by_name:
            # Find first unmatched Revit space with this name
            for revit_guid in revit_by_name[name_key]:
                if revit_guid not in revit_matched:
                    ifc_to_revit_matches[ifc_guid] = (revit_guid, "NAME_ONLY")
                    revit_matched.add(revit_guid)
                    break
    
    # Create result dictionaries
    matched_pairs = {}  # ifc_guid -> (ifc_data, revit_data, match_type)
    unmatched_ifc = {}  # ifc_guid -> ifc_data
    unmatched_revit = {}  # revit_guid -> revit_data
    
    # Process matched pairs
    for ifc_guid, (revit_guid, match_type) in ifc_to_revit_matches.items():
        ifc_data = ifc_spaces_dict[ifc_guid]
        revit_data = revit_spaces_dict[revit_guid]
        matched_pairs[ifc_guid] = (ifc_data, revit_data, match_type)
    
    # Process unmatched IFC spaces
    for ifc_guid, ifc_data in ifc_spaces_dict.items():
        if ifc_guid not in ifc_to_revit_matches:
            unmatched_ifc[ifc_guid] = ifc_data
    
    # Process unmatched Revit spaces
    for revit_guid, revit_data in revit_spaces_dict.items():
        if revit_guid not in revit_matched:
            unmatched_revit[revit_guid] = revit_data
    
    return {
        "matched_pairs": matched_pairs,
        "unmatched_ifc": unmatched_ifc,
        "unmatched_revit": unmatched_revit,
        "match_stats": {
            "guid_matches": len([m for m in ifc_to_revit_matches.values() if m[1] == "GUID"]),
            "number_name_matches": len([m for m in ifc_to_revit_matches.values() if m[1] == "NUMBER_NAME"]),
            "name_only_matches": len([m for m in ifc_to_revit_matches.values() if m[1] == "NAME_ONLY"]),
            "total_matches": len(ifc_to_revit_matches),
            "unmatched_ifc_count": len(unmatched_ifc),
            "unmatched_revit_count": len(unmatched_revit)
        }
    }

def compare_spaces(ifc_spaces_data, spaces_data):
    """
    Compare IFC spaces with Revit spaces using comprehensive matching (GUID, Number+Name, Name) 
    and format data according to table layout.
    """
    # Get space warnings first
    space_warnings = get_space_warnings(doc)
    
    # Use new matching logic
    match_results = find_space_matches(ifc_spaces_data, spaces_data)
    
    comparison_results = []
    
    # Process matched pairs
    for ifc_guid, (ifc_data, revit_data, match_type) in match_results["matched_pairs"].items():
        # Define column order explicitly
        row_data = [
            # IFC Data (columns A-G)
            ("IFC Room Name", ifc_data["Space Name"]),
            ("IFC Room Number", ifc_data["Space Number"]),
            ("IFC GUID", ifc_guid),
            ("IFC Level Reference", ifc_data["Level Reference"]),
            ("IFC Area", ifc_data["Area"]),
            ("IFC Volume", ifc_data["Volume"]),
            ("IFC Perimeter", ifc_data["Perimeter"]),

            # Status/Reflection Info (columns H-I)
            ("Reflect into RVT", "Done"),
            ("Reason", ""),

            # RVT Data (columns J-P)
            ("RVT Space Name", revit_data["Space Name"]),
            ("RVT Space Number", revit_data["Space Number"]),
            ("RVT GUID", revit_data["Guid"]),
            ("RVT Level Reference", revit_data["Level Reference"]),
            ("RVT Area", revit_data["Area"]),
            ("RVT Volume", revit_data["Volume"]),
            ("RVT Perimeter", revit_data["Perimeter"]),

            # Additional Info (columns Q-S)
            ("Status", revit_data["status"]),
            ("Issues", space_warnings.get(revit_data["Guid"], "")),
            ("Match Type", match_type)  # New column to show how the match was found
        ]

        # Compare data and note differences
        mismatches = []
        
        # Check all data fields for mismatches
        if ifc_data["Space Name"] != revit_data["Space Name"]:
            mismatches.append("Name mismatch")
        if ifc_data["Space Number"] != revit_data["Space Number"]:
            mismatches.append("Number mismatch")
        if ifc_guid != revit_data["Guid"]:
            mismatches.append("GUID mismatch")
        if ifc_data["Level Reference"] != revit_data["Level Reference"]:
            mismatches.append("Level mismatch")
        if ifc_data["Area"] != revit_data["Area"]:
            mismatches.append("Area mismatch")
        if ifc_data["Volume"] != revit_data["Volume"]:
            mismatches.append("Volume mismatch")
        if ifc_data["Perimeter"] != revit_data["Perimeter"]:
            mismatches.append("Perimeter mismatch")
        
        # Add match type context (this explains WHY they were matched despite mismatches)
        if match_type == "NUMBER_NAME":
            if "GUID mismatch" not in mismatches:
                mismatches.append("GUID mismatch")
        elif match_type == "NAME_ONLY":
            if "GUID mismatch" not in mismatches:
                mismatches.append("GUID mismatch")
            if "Number mismatch" not in mismatches:
                mismatches.append("Number mismatch")

        if mismatches:
            row_data[8] = ("Reason", "; ".join(mismatches))
        
        # Create ordered row
        ordered_row = create_ordered_row(row_data)
        comparison_results.append(ordered_row)
    
    # Process unmatched IFC spaces
    for ifc_guid, ifc_data in match_results["unmatched_ifc"].items():
        row_data = [
            # IFC Data (columns A-G)
            ("IFC Room Name", ifc_data["Space Name"]),
            ("IFC Room Number", ifc_data["Space Number"]),
            ("IFC GUID", ifc_guid),
            ("IFC Level Reference", ifc_data["Level Reference"]),
            ("IFC Area", ifc_data["Area"]),
            ("IFC Volume", ifc_data["Volume"]),
            ("IFC Perimeter", ifc_data["Perimeter"]),

            # Status/Reflection Info (columns H-I)
            ("Reflect into RVT", "Not"),
            ("Reason", "No matching Revit space found"),

            # Empty RVT Data (columns J-P)
            ("RVT Space Name", ""),
            ("RVT Space Number", ""),
            ("RVT GUID", ""),
            ("RVT Level Reference", ""),
            ("RVT Area", ""),
            ("RVT Volume", ""),
            ("RVT Perimeter", ""),

            # Additional Info (columns Q-S)
            ("Status", ""),
            ("Issues", ""),
            ("Match Type", "NO_MATCH")
        ]
        
        ordered_row = create_ordered_row(row_data)
        comparison_results.append(ordered_row)
    
    # Process unmatched Revit spaces
    for revit_guid, revit_data in match_results["unmatched_revit"].items():
        row_data = [
            # Empty IFC data (columns A-G)
            ("IFC Room Name", ""),
            ("IFC Room Number", ""),
            ("IFC GUID", ""),
            ("IFC Level Reference", ""),
            ("IFC Area", ""),
            ("IFC Volume", ""),
            ("IFC Perimeter", ""),

            # Status/Reflection Info (columns H-I)
            ("Reflect into RVT", "N/A"),
            ("Reason", "Revit space has no IFC counterpart"),

            # Revit data (columns J-P)
            ("RVT Space Name", revit_data["Space Name"]),
            ("RVT Space Number", revit_data["Space Number"]),
            ("RVT GUID", revit_data["Guid"]),
            ("RVT Level Reference", revit_data["Level Reference"]),
            ("RVT Area", revit_data["Area"]),
            ("RVT Volume", revit_data["Volume"]),
            ("RVT Perimeter", revit_data["Perimeter"]),

            # Additional Info (columns Q-S)
            ("Status", revit_data["status"]),
            ("Issues", space_warnings.get(revit_guid, "")),
            ("Match Type", "REVIT_ONLY")
        ]
        
        ordered_row = create_ordered_row(row_data)
        comparison_results.append(ordered_row)
    
    return comparison_results

def debug_space_matching(ifc_spaces_data, spaces_data):
    """
    Debug function to analyze space matching between IFC and Revit using comprehensive matching
    """
    # Use new matching logic
    match_results = find_space_matches(ifc_spaces_data, spaces_data)
    
    print("=== COMPREHENSIVE SPACE MATCHING DEBUG REPORT ===")
    
    # Print match statistics
    stats = match_results["match_stats"]
    total_ifc = len(match_results["matched_pairs"]) + len(match_results["unmatched_ifc"])
    total_revit = len(match_results["matched_pairs"]) + len(match_results["unmatched_revit"])
    
    print("Total IFC Spaces: {}".format(total_ifc))
    print("Total Revit Spaces: {}".format(total_revit))
    print()
    print("MATCHING RESULTS:")
    print("  GUID Matches: {}".format(stats["guid_matches"]))
    print("  Number+Name Matches: {}".format(stats["number_name_matches"]))
    print("  Name-Only Matches: {}".format(stats["name_only_matches"]))
    print("  Total Matches: {}".format(stats["total_matches"]))
    print("  Unmatched IFC: {}".format(stats["unmatched_ifc_count"]))
    print("  Unmatched Revit: {}".format(stats["unmatched_revit_count"]))
    
    # Calculate match percentage
    if total_ifc > 0:
        match_percentage = (stats["total_matches"] / float(total_ifc)) * 100
        perfect_percentage = (stats["guid_matches"] / float(total_ifc)) * 100
    else:
        match_percentage = 0.0
        perfect_percentage = 0.0
    
    print("  Match Rate: {:.1f}%".format(match_percentage))
    print("  Perfect Match Rate (GUID): {:.1f}%".format(perfect_percentage))
    
    # Analyze data quality for matches
    perfect_matches = []
    mismatched_data = []
    
    for ifc_guid, (ifc_data, revit_data, match_type) in match_results["matched_pairs"].items():
        # Check if all data matches
        name_match = ifc_data["Space Name"] == revit_data["Space Name"]
        number_match = ifc_data["Space Number"] == revit_data["Space Number"]
        level_match = ifc_data["Level Reference"] == revit_data["Level Reference"]
        area_match = ifc_data["Area"] == revit_data["Area"]
        volume_match = ifc_data["Volume"] == revit_data["Volume"]
        perimeter_match = ifc_data["Perimeter"] == revit_data["Perimeter"]
        guid_match = ifc_guid == revit_data["Guid"]

        if name_match and number_match and level_match and area_match and volume_match and perimeter_match and guid_match:
            perfect_matches.append(ifc_guid)
        else:
            # Create readable match status string
            match_status = []
            if not guid_match:
                match_status.append("GUID")
            if not name_match:
                match_status.append("Name")
            if not number_match:
                match_status.append("Number")
            if not level_match:
                match_status.append("Level")
            if not area_match:
                match_status.append("Area")
            if not volume_match:
                match_status.append("Volume")
            if not perimeter_match:
                match_status.append("Perimeter")
            
            mismatch_summary = "Mismatch: " + ";".join(match_status)
            
            mismatched_data.append({
                "IFC_GUID": ifc_guid,
                "RVT_GUID": revit_data["Guid"],
                "IFC_Name": ifc_data["Space Name"],
                "RVT_Name": revit_data["Space Name"],
                "IFC_Number": ifc_data["Space Number"],
                "RVT_Number": revit_data["Space Number"],
                "IFC_Level": ifc_data["Level Reference"],
                "RVT_Level": revit_data["Level Reference"],
                "IFC_Area": ifc_data["Area"],
                "RVT_Area": revit_data["Area"],
                "Match_Type": match_type,
                "Mismatch_Summary": mismatch_summary,
                "GUID_Match": "Yes" if guid_match else "No",
                "Name_Match": "Yes" if name_match else "No",
                "Number_Match": "Yes" if number_match else "No",
                "Level_Match": "Yes" if level_match else "No",
                "Area_Match": "Yes" if area_match else "No"
            })
    
    print("\nDATA QUALITY:")
    print("  Perfect Data Matches: {} ({:.1f}%)".format(len(perfect_matches), 
          (len(perfect_matches) / float(stats["total_matches"]) * 100) if stats["total_matches"] > 0 else 0.0))
    print("  Data Mismatches: {}".format(len(mismatched_data)))
    
    # Convert unmatched data to old format for compatibility
    ifc_only = []
    for guid, data in match_results["unmatched_ifc"].items():
        ifc_only.append({
            "GUID": guid,
            "Name": data["Space Name"],
            "Number": data["Space Number"],
            "Level": data["Level Reference"],
            "Area": data["Area"]
        })
    
    revit_only = []
    for guid, data in match_results["unmatched_revit"].items():
        revit_only.append({
            "GUID": guid,
            "Name": data["Space Name"],
            "Number": data["Space Number"],
            "Level": data["Level Reference"],
            "Area": data["Area"]
        })
    
    # Detailed breakdown by level
    ifc_by_level = {}
    revit_by_level = {}
    
    # Include matched IFC spaces
    for ifc_guid, (ifc_data, _, _) in match_results["matched_pairs"].items():
        level = ifc_data["Level Reference"]
        if level not in ifc_by_level:
            ifc_by_level[level] = []
        ifc_by_level[level].append(ifc_guid)
    
    # Include unmatched IFC spaces
    for guid, data in match_results["unmatched_ifc"].items():
        level = data["Level Reference"]
        if level not in ifc_by_level:
            ifc_by_level[level] = []
        ifc_by_level[level].append(guid)
    
    # Include matched Revit spaces
    for _, (_, revit_data, _) in match_results["matched_pairs"].items():
        level = revit_data["Level Reference"]
        if level not in revit_by_level:
            revit_by_level[level] = []
        revit_by_level[level].append(revit_data["Guid"])
    
    # Include unmatched Revit spaces
    for guid, data in match_results["unmatched_revit"].items():
        level = data["Level Reference"]
        if level not in revit_by_level:
            revit_by_level[level] = []
        revit_by_level[level].append(guid)
    
    print("\n=== LEVEL BREAKDOWN ===")
    all_levels = set(list(ifc_by_level.keys()) + list(revit_by_level.keys()))
    for level in sorted(all_levels):
        ifc_count = len(ifc_by_level.get(level, []))
        revit_count = len(revit_by_level.get(level, []))
        print("Level {}: IFC={}, Revit={}".format(level, ifc_count, revit_count))
    
    return {
        "perfect_matches": perfect_matches,
        "guid_matches": [guid for guid, (_, _, match_type) in match_results["matched_pairs"].items() if match_type == "GUID"],
        "mismatched_data": mismatched_data,
        "ifc_only": ifc_only,
        "revit_only": revit_only,
        "ifc_by_level": ifc_by_level,
        "revit_by_level": revit_by_level,
        "match_stats": stats,
        "match_results": match_results
    }

def filter_spaces_by_level(spaces_data, target_level):
    """
    Filter spaces data by a specific level
    """
    filtered_data = []
    for space_dict in spaces_data:
        for guid, data in space_dict.items():
            if data["Level Reference"] == target_level:
                filtered_data.append({guid: data})
    return filtered_data

def validate_space_processing(ifc_spaces, spaces, ifc_spaces_data, spaces_data):
    """
    Validate that all spaces are properly processed and counted
    """
    print("=== SPACE PROCESSING VALIDATION ===")
    
    # Count raw elements
    raw_ifc_count = len(ifc_spaces) if ifc_spaces else 0
    raw_revit_count = len(spaces) if spaces else 0
    
    # Count processed data
    processed_ifc_count = len(ifc_spaces_data) if ifc_spaces_data else 0
    processed_revit_count = len(spaces_data) if spaces_data else 0
    
    # Count unique GUIDs in processed data
    ifc_guids = set()
    revit_guids = set()
    
    for ifc_space_dict in ifc_spaces_data:
        for guid in ifc_space_dict.keys():
            ifc_guids.add(guid)
    
    for revit_space_dict in spaces_data:
        for guid in revit_space_dict.keys():
            revit_guids.add(guid)
    
    unique_ifc_count = len(ifc_guids)
    unique_revit_count = len(revit_guids)
    
    print("Raw Element Counts:")
    print("  IFC Elements from Link: {}".format(raw_ifc_count))
    print("  Revit Spaces from Model: {}".format(raw_revit_count))
    print()
    print("Processed Data Counts:")
    print("  IFC Data Objects: {}".format(processed_ifc_count))
    print("  Revit Data Objects: {}".format(processed_revit_count))
    print()
    print("Unique GUID Counts:")
    print("  Unique IFC GUIDs: {}".format(unique_ifc_count))
    print("  Unique Revit GUIDs: {}".format(unique_revit_count))
    print()
    
    # Check for processing issues
    if raw_ifc_count != processed_ifc_count:
        print("WARNING: IFC processing issue - {} elements but {} processed".format(raw_ifc_count, processed_ifc_count))
    
    if raw_revit_count != processed_revit_count:
        print("WARNING: Revit processing issue - {} elements but {} processed".format(raw_revit_count, processed_revit_count))
    
    if processed_ifc_count != unique_ifc_count:
        print("WARNING: Duplicate IFC GUIDs detected")
    
    if processed_revit_count != unique_revit_count:
        print("WARNING: Duplicate Revit GUIDs detected")
    
    # Check for spaces without GUIDs
    ifc_no_guid = 0
    revit_no_guid = 0
    
    for i, space in enumerate(ifc_spaces):
        try:
            guid = space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
            if not guid or guid.strip() == "":
                ifc_no_guid += 1
        except:
            ifc_no_guid += 1
    
    for i, space in enumerate(spaces):
        try:
            guid = space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
            if not guid or guid.strip() == "":
                revit_no_guid += 1
        except:
            revit_no_guid += 1
    
    if ifc_no_guid > 0:
        print("WARNING: {} IFC spaces have no GUID".format(ifc_no_guid))
    
    if revit_no_guid > 0:
        print("WARNING: {} Revit spaces have no GUID".format(revit_no_guid))
    
    print("=" * 50)
    return {
        "raw_ifc_count": raw_ifc_count,
        "raw_revit_count": raw_revit_count,
        "unique_ifc_count": unique_ifc_count,
        "unique_revit_count": unique_revit_count,
        "ifc_no_guid": ifc_no_guid,
        "revit_no_guid": revit_no_guid
    }

def analyze_all_levels(ifc_spaces_data, spaces_data):
    """
    Analyze spaces for all levels in detail using comprehensive matching
    """
    # Use comprehensive matching logic
    match_results = find_space_matches(ifc_spaces_data, spaces_data)
    
    # Group matched pairs by level
    matched_by_level = {}
    unmatched_ifc_by_level = {}
    unmatched_revit_by_level = {}
    
    # Process matched pairs
    for ifc_guid, (ifc_data, revit_data, match_type) in match_results["matched_pairs"].items():
        level = ifc_data["Level Reference"]
        if level not in matched_by_level:
            matched_by_level[level] = []
        matched_by_level[level].append((ifc_guid, ifc_data, revit_data, match_type))
    
    # Process unmatched IFC spaces
    for ifc_guid, ifc_data in match_results["unmatched_ifc"].items():
        level = ifc_data["Level Reference"]
        if level not in unmatched_ifc_by_level:
            unmatched_ifc_by_level[level] = []
        unmatched_ifc_by_level[level].append((ifc_guid, ifc_data))
    
    # Process unmatched Revit spaces
    for revit_guid, revit_data in match_results["unmatched_revit"].items():
        level = revit_data["Level Reference"]
        if level not in unmatched_revit_by_level:
            unmatched_revit_by_level[level] = []
        unmatched_revit_by_level[level].append((revit_guid, revit_data))
    
    # Get all levels
    all_levels = set(list(matched_by_level.keys()) + 
                     list(unmatched_ifc_by_level.keys()) + 
                     list(unmatched_revit_by_level.keys()))
    
    print("=== COMPREHENSIVE LEVEL ANALYSIS ===")
    print("Total Levels Found: {}".format(len(all_levels)))
    print()
    
    level_analysis = {}
    
    for level in sorted(all_levels):
        level_matches = matched_by_level.get(level, [])
        level_unmatched_ifc = unmatched_ifc_by_level.get(level, [])
        level_unmatched_revit = unmatched_revit_by_level.get(level, [])
        
        # Count spaces
        ifc_count = len(level_matches) + len(level_unmatched_ifc)
        revit_count = len(level_matches) + len(level_unmatched_revit)
        
        # Analyze matches by type
        guid_matches = 0
        number_name_matches = 0
        name_only_matches = 0
        perfect_matches = 0
        data_mismatches = []
        
        for ifc_guid, ifc_data, revit_data, match_type in level_matches:
            if match_type == "GUID":
                guid_matches += 1
            elif match_type == "NUMBER_NAME":
                number_name_matches += 1
            elif match_type == "NAME_ONLY":
                name_only_matches += 1
            
            # Check if all data matches perfectly
            name_match = ifc_data["Space Name"] == revit_data["Space Name"]
            number_match = ifc_data["Space Number"] == revit_data["Space Number"]
            level_match = ifc_data["Level Reference"] == revit_data["Level Reference"]
            area_match = ifc_data["Area"] == revit_data["Area"]
            volume_match = ifc_data["Volume"] == revit_data["Volume"]
            perimeter_match = ifc_data["Perimeter"] == revit_data["Perimeter"]
            guid_match = ifc_guid == revit_data["Guid"]
            
            if (name_match and number_match and level_match and 
                area_match and volume_match and perimeter_match and guid_match):
                perfect_matches += 1
            else:
                # Create mismatch details
                mismatch_details = []
                if not guid_match:
                    mismatch_details.append("GUID")
                if not name_match:
                    mismatch_details.append("Name")
                if not number_match:
                    mismatch_details.append("Number")
                if not level_match:
                    mismatch_details.append("Level")
                if not area_match:
                    mismatch_details.append("Area")
                if not volume_match:
                    mismatch_details.append("Volume")
                if not perimeter_match:
                    mismatch_details.append("Perimeter")
                
                data_mismatches.append({
                    "IFC_GUID": ifc_guid,
                    "RVT_GUID": revit_data["Guid"],
                    "IFC_Name": ifc_data["Space Name"],
                    "RVT_Name": revit_data["Space Name"],
                    "IFC_Number": ifc_data["Space Number"],
                    "RVT_Number": revit_data["Space Number"],
                    "Match_Type": match_type,
                    "Mismatch_Details": ";".join(mismatch_details),
                    "GUID_Match": "Yes" if guid_match else "No",
                    "Name_Match": "Yes" if name_match else "No",
                    "Number_Match": "Yes" if number_match else "No",
                    "Level_Match": "Yes" if level_match else "No",
                    "Area_Match": "Yes" if area_match else "No"
                })
        
        # Convert unmatched data to old format for compatibility
        ifc_only = []
        for ifc_guid, ifc_data in level_unmatched_ifc:
            ifc_only.append({
                "GUID": ifc_guid,
                "Name": ifc_data["Space Name"],
                "Number": ifc_data["Space Number"]
            })
        
        revit_only = []
        for revit_guid, revit_data in level_unmatched_revit:
            revit_only.append({
                "GUID": revit_guid,
                "Name": revit_data["Space Name"],
                "Number": revit_data["Space Number"]
            })
        
        # Calculate compliance percentages
        total_matches = len(level_matches)
        if ifc_count > 0:
            match_percentage = (total_matches / float(ifc_count)) * 100
            perfect_percentage = (perfect_matches / float(ifc_count)) * 100
        else:
            match_percentage = 0.0 if revit_count == 0 else 0.0
            perfect_percentage = 0.0
        
        # Store analysis
        level_analysis[level] = {
            "ifc_count": ifc_count,
            "revit_count": revit_count,
            "total_matches": total_matches,
            "guid_matches": guid_matches,
            "number_name_matches": number_name_matches,
            "name_only_matches": name_only_matches,
            "perfect_matches": perfect_matches,
            "ifc_only": ifc_only,
            "revit_only": revit_only,
            "data_mismatches": data_mismatches,
            "match_percentage": match_percentage,
            "compliance_percentage": perfect_percentage  # Keep old name for compatibility
        }
        
        # Print level summary
        print("LEVEL: {}".format(level))
        print("  IFC Spaces: {}".format(ifc_count))
        print("  Revit Spaces: {}".format(revit_count))
        print("  Total Matches: {} ({:.1f}%)".format(total_matches, match_percentage))
        print("    - GUID Matches: {}".format(guid_matches))
        print("    - Number+Name Matches: {}".format(number_name_matches))
        print("    - Name-Only Matches: {}".format(name_only_matches))
        print("  Perfect Data Matches: {} ({:.1f}%)".format(perfect_matches, perfect_percentage))
        print("  Data Mismatches: {}".format(len(data_mismatches)))
        print("  Missing in Revit: {}".format(len(ifc_only)))
        print("  Extra in Revit: {}".format(len(revit_only)))
        
        if len(ifc_only) > 0:
            print("  -> IFC spaces NOT matched: {}".format(len(ifc_only)))
        if len(revit_only) > 0:
            print("  -> Revit spaces NOT matched: {}".format(len(revit_only)))
        if len(data_mismatches) > 0:
            print("  -> Matched but data differs: {}".format(len(data_mismatches)))
        
        print()
    
    return level_analysis

def main():
    # query data
    link_doc = get_link_data(doc)
    ifc_spaces = get_generic_spaces(link_doc)
    spaces = DB.FilteredElementCollector(doc).OfCategory(DB.BuiltInCategory.OST_MEPSpaces).WhereElementIsNotElementType().ToElements()

    # process data
    ifc_spaces_data = [get_ifcSpace_data(space) for space in ifc_spaces]
    spaces_data = [get_space_data(space) for space in spaces]
    
    # Validate space processing
    validation_results = validate_space_processing(ifc_spaces, spaces, ifc_spaces_data, spaces_data)
    
    print("=== FULL PROJECT DEBUG ===")
    debug_results = debug_space_matching(ifc_spaces_data, spaces_data)
    
    # Analyze all levels in detail
    print("\n" + "=" * 60)
    level_analysis = analyze_all_levels(ifc_spaces_data, spaces_data)
    
    # Overall compliance summary
    total_ifc = sum([analysis["ifc_count"] for analysis in level_analysis.values()])
    total_revit = sum([analysis["revit_count"] for analysis in level_analysis.values()])
    total_perfect = sum([analysis["perfect_matches"] for analysis in level_analysis.values()])
    total_missing_in_revit = sum([len(analysis["ifc_only"]) for analysis in level_analysis.values()])
    total_extra_in_revit = sum([len(analysis["revit_only"]) for analysis in level_analysis.values()])
    
    overall_compliance = (total_perfect / float(total_ifc)) * 100 if total_ifc > 0 else 0.0
    
    print("=== OVERALL COMPLIANCE SUMMARY ===")
    print("Total IFC Spaces: {}".format(total_ifc))
    print("Total Revit Spaces: {}".format(total_revit))
    print("Perfect Matches: {}".format(total_perfect))
    print("Missing in Revit: {}".format(total_missing_in_revit))
    print("Extra in Revit: {}".format(total_extra_in_revit))
    print("Overall Compliance: {:.1f}%".format(overall_compliance))
    print()
    
    if total_missing_in_revit > 0:
        print("ACTION REQUIRED: {} IFC spaces need to be placed in Revit".format(total_missing_in_revit))
    if total_extra_in_revit > 0:
        print("REVIEW REQUIRED: {} Revit spaces have no IFC counterpart".format(total_extra_in_revit))
    
    comparison_data = compare_spaces(ifc_spaces_data, spaces_data)

    # Format data for Revit Spaces sheet
    revit_spaces_sheet_data = []
    for space_dict in spaces_data:
        for guid, data in space_dict.items():
            row_data = [
                ("Space Name", data["Space Name"]),
                ("Space Number", data["Space Number"]),
                ("GUID", data["Guid"]),
                ("Level Reference", data["Level Reference"]),
                ("Area", data["Area"]),
                ("Volume", data["Volume"]),
                ("Perimeter", data["Perimeter"]),
                ("Status", data["status"])
            ]
            revit_spaces_sheet_data.append(create_ordered_row(row_data))
    
    # Format data for IFC Spaces sheet
    ifc_spaces_sheet_data = []
    for ifc_dict in ifc_spaces_data:
        for guid, data in ifc_dict.items():
            row_data = [
                ("Room Name", data["Space Name"]),
                ("Room Number", data["Space Number"]),
                ("GUID", data["RGuid"]),
                ("Level Reference", data["Level Reference"]),
                ("Area", data["Area"]),
                ("Volume", data["Volume"]),
                ("Perimeter", data["Perimeter"])
            ]
            ifc_spaces_sheet_data.append(create_ordered_row(row_data))
    
    # Create level-by-level analysis sheets with comprehensive match data
    level_summary_data = []
    for level, analysis in level_analysis.items():
        row_data = [
            ("Level", level),
            ("IFC_Count", analysis["ifc_count"]),
            ("Revit_Count", analysis["revit_count"]),
            ("Total_Matches", analysis.get("total_matches", analysis["perfect_matches"])),
            ("GUID_Matches", analysis.get("guid_matches", 0)),
            ("Number_Name_Matches", analysis.get("number_name_matches", 0)),
            ("Name_Only_Matches", analysis.get("name_only_matches", 0)),
            ("Perfect_Data_Matches", analysis["perfect_matches"]),
            ("Data_Mismatches", len(analysis["data_mismatches"])),
            ("Missing_in_Revit", len(analysis["ifc_only"])),
            ("Extra_in_Revit", len(analysis["revit_only"])),
            ("Match_Percentage", round(analysis.get("match_percentage", 0.0), 1)),
            ("Perfect_Percentage", round(analysis["compliance_percentage"], 1))
        ]
        level_summary_data.append(create_ordered_row(row_data))
    
    # Add debug sheets with ordered columns
    debug_missing_ifc = []
    for item in debug_results["ifc_only"]:
        row_data = [
            ("GUID", item["GUID"]),
            ("Name", item["Name"]),
            ("Number", item["Number"]),
            ("Level", item["Level"]),
            ("Area", item["Area"])
        ]
        debug_missing_ifc.append(create_ordered_row(row_data))
    
    debug_missing_revit = []
    for item in debug_results["revit_only"]:
        row_data = [
            ("GUID", item["GUID"]),
            ("Name", item["Name"]),
            ("Number", item["Number"]),
            ("Level", item["Level"]),
            ("Area", item["Area"])
        ]
        debug_missing_revit.append(create_ordered_row(row_data))
    
    debug_mismatched = []
    for item in debug_results["mismatched_data"]:
        row_data = [
            ("IFC_GUID", item.get("IFC_GUID", item.get("GUID", ""))),
            ("RVT_GUID", item.get("RVT_GUID", "")),
            ("IFC_Name", item["IFC_Name"]),
            ("RVT_Name", item["RVT_Name"]),
            ("IFC_Number", item["IFC_Number"]),
            ("RVT_Number", item["RVT_Number"]),
            ("IFC_Level", item["IFC_Level"]),
            ("RVT_Level", item["RVT_Level"]),
            ("IFC_Area", item["IFC_Area"]),
            ("RVT_Area", item["RVT_Area"]),
            ("Match_Type", item.get("Match_Type", "GUID")),
            ("Mismatch_Summary", item["Mismatch_Summary"]),
            ("GUID_Match", item.get("GUID_Match", item.get("Name_Match", ""))),
            ("Name_Match", item["Name_Match"]),
            ("Number_Match", item["Number_Match"]),
            ("Level_Match", item["Level_Match"]),
            ("Area_Match", item["Area_Match"])
        ]
        debug_mismatched.append(create_ordered_row(row_data))
    
    # Create overall match statistics sheet
    match_stats = debug_results.get("match_stats", {})
    overall_stats_data = []
    
    stats_row_data = [
        ("Metric", "Value"),
        ("Total_IFC_Spaces", total_ifc),
        ("Total_Revit_Spaces", total_revit),
        ("Total_Matches", match_stats.get("total_matches", 0)),
        ("GUID_Matches", match_stats.get("guid_matches", 0)),
        ("Number_Name_Matches", match_stats.get("number_name_matches", 0)),
        ("Name_Only_Matches", match_stats.get("name_only_matches", 0)),
        ("Unmatched_IFC", match_stats.get("unmatched_ifc_count", 0)),
        ("Unmatched_Revit", match_stats.get("unmatched_revit_count", 0)),
        ("Perfect_Data_Matches", total_perfect),
        ("Overall_Match_Rate_%", round((match_stats.get("total_matches", 0) / float(total_ifc)) * 100, 1) if total_ifc > 0 else 0.0),
        ("Perfect_Match_Rate_%", round(overall_compliance, 1))
    ]
    
    # Create two-column format for statistics
    for i in range(0, len(stats_row_data), 2):
        if i + 1 < len(stats_row_data):
            row_data = [
                ("Metric_1", stats_row_data[i][0]),
                ("Value_1", stats_row_data[i][1]),
                ("Metric_2", stats_row_data[i + 1][0]),
                ("Value_2", stats_row_data[i + 1][1])
            ]
        else:
            row_data = [
                ("Metric_1", stats_row_data[i][0]),
                ("Value_1", stats_row_data[i][1]),
                ("Metric_2", ""),
                ("Value_2", "")
            ]
        overall_stats_data.append(create_ordered_row(row_data))
    
    # Convert to .NET Dictionary for MiniExcel
    exported_data = Dictionary[str, object]()
    exported_data["Match Statistics"] = overall_stats_data
    exported_data["Level Summary"] = level_summary_data
    exported_data["Comparison"] = comparison_data
    exported_data["Revit Spaces"] = revit_spaces_sheet_data
    exported_data["IFC Spaces"] = ifc_spaces_sheet_data
    exported_data["Missing in Revit"] = debug_missing_ifc
    exported_data["Missing in IFC"] = debug_missing_revit
    exported_data["Data Mismatches"] = debug_mismatched
    
    # Save data to Excel
    export_path = save_excel_file("Save Spaces Debug Data")
    if export_path:
        # clean file befor save
        if os.path.exists(export_path):
            os.remove(export_path)
        # Simply save the data without any styling
        MiniExcel.SaveAs(export_path, exported_data)

        # ask user to open the folder
        open_folder = alert("Data exported successfully to:\n{}\n\nOpen folder?".format(export_path), yes=True, no=True)
        if open_folder:
            folder_path = os.path.dirname(export_path)
            os.startfile(folder_path)