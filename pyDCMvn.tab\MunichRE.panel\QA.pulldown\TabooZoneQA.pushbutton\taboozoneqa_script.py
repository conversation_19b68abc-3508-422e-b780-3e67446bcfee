# coding: utf-8
import clr
import System

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

from DCMvn.core import DB, HOST_APP  # noqa: F401
from DCMvn.core.framework import Debug
from lib.rules import RuleRegistry
from lib.validation import ValidationWorkflow
from lib.reporting import ExcelReporter, HtmlReporter

if __name__ == "__main__":
    # Ensure plugin rules are imported (RuleRegistry is populated via import side-effect)
    from lib.rules.o1_beams_u1u2_rule import O1BeamsU1U2Rule  # noqa: F401
    rule_cls = RuleRegistry.get("O1_BEAMS_U1U2")
    rule = rule_cls() if rule_cls else None # type: O1BeamsU1U2Rule

    # Get link document and elements
    link_name = "O1--_AWA_300_5-_FA-_T1_0000_-_GE"
    link_instance = (DB.FilteredElementCollector(HOST_APP.doc)
                .OfClass(DB.RevitLinkInstance)
                .WhereElementIsNotElementType()
                .FirstOrDefault(lambda x: DB.RevitLinkType.IsLoaded(HOST_APP.doc, x.GetTypeId()) and x.Name.Contains(link_name)))

    if link_instance and rule:
        beams = rule.collect_arc_elements(link_instance.GetLinkDocument())
        masses = rule.collect_mass_elements(HOST_APP.doc)

        Debug.WriteLine("Found {} beams and {} masses".format(len(beams), len(masses)))

        # Use rule default tolerance and GUID param specs
        tolerance_mm = rule.tolerance_mm
        wf = ValidationWorkflow()
        reporter = ExcelReporter()
        html_reporter = HtmlReporter()

        result = wf.execute_validation(
            arc_elements=beams,
            mass_elements=masses,
            arc_name="O1 ARC Beams",
            mass_name="Mass Elements",
            tolerance_mm=tolerance_mm,
            arc_guid_param=rule.arc_guid_param_spec,
            mass_guid_param=rule.mass_guid_param_spec,
            html_reporter=html_reporter,
            excel_reporter=reporter,
            rule_name=rule.display_name,
            extra_columns=rule.extra_excel_columns,
        )

        if result.has_issues:
            Debug.WriteLine("\n❌ VALIDATION FAILED - Issues found!")
        else:
            Debug.WriteLine("\n✅ VALIDATION PASSED - All checks passed!")
    else:
        Debug.WriteLine("Link instance '{}' not found or not loaded, or rule missing".format(link_name))

