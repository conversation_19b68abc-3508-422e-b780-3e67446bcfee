---
applyTo: "**/viewmodel/**/*.py"
---

# ViewModel Patterns and MVVM Implementation

## ViewModel Base Structure
Always inherit from ViewModelBase and follow this pattern:
```python
from DCMvn.forms.mvvm import ViewModelBase, RelayCommand
from DCMvn.core.framework import ObservableCollection

class MainViewModel(ViewModelBase):
    def __init__(self, document):
        super(MainViewModel, self).__init__()
        self.document = document
        self._initialize_services()
        self._initialize_commands()
        self._initialize_properties()
    
    def _initialize_services(self):
        # Initialize service layer
        pass
    
    def _initialize_commands(self):
        self.execute_command = RelayCommand(self.execute, self.can_execute)
    
    def _initialize_properties(self):
        self._items = ObservableCollection[object]()
```

## Property Implementation Pattern
Use this pattern for bindable properties:
```python
@property
def selected_item(self):
    return self._selected_item

@selected_item.setter
def selected_item(self, value):
    if self._selected_item != value:
        self._selected_item = value
        self.RaisePropertyChanged("selected_item")
        # Update dependent commands
        self.execute_command.RaiseCanExecuteChanged()
```

## Command Implementation
Use RelayCommand for all user actions:
```python
def execute(self, parameter):
    try:
        # Use external events for Revit API operations
        self._event_handler.Raise(self._perform_revit_operation)
    except Exception as e:
        alert("Operation failed: {}".format(str(e)), "Error")

def can_execute(self, parameter):
    return self.selected_item is not None

def _perform_revit_operation(self, uiapp):
    # This runs on Revit's main thread
    with DB.Transaction(self.document, "Operation") as t:
        t.Start()
        try:
            # Perform Revit API operations
            t.Commit()
        except Exception as e:
            t.RollBack()
            raise
```

## Service Integration
Inject services through constructor:
```python
def __init__(self, document):
    super(MainViewModel, self).__init__()
    self.document = document
    self.collector_service = CollectorService(document)
    self.detection_service = DetectionService(document)
    self.report_service = ReportService(document)
```

## Threading and External Events
- Always use external events for Revit API operations
- Keep UI responsive by avoiding blocking operations
- Use progress reporting for long-running tasks
