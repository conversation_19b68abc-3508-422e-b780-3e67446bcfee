---
description: pyRevit tool organization, bundle.yaml patterns, and script structure
globs: ["**/bundle.yaml", "**/*_script.py", "**/script.py"]
alwaysApply: false
---

# pyRevit Tool Organization

## Bundle.yaml Configuration
```yaml
title: "Tool Name"
tooltip:
  en_us: |
    Version = X.X
    __________________________________________________________________
    Description:
    Brief tool description and purpose
    __________________________________________________________________
    How-to:
    Step 1: First action
    Step 2: Second action
    __________________________________________________________________
    Last update:
    - [DD.MM.YYYY] - X.X Description of changes
author: "Author Name"
engine:
  clean: true  # Enable clean engine for better performance
```

## Script Entry Patterns
### Main Script Structure
```python
# coding: utf-8
import clr
from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.core.framework import System, Debug
from DCMvn.forms import alert

from lib.ui import MainView
from lib.viewmodel import MainViewModel

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

doc = HOST_APP.doc
output = get_output()

def validate_document_requirements():
    """Validate document state before tool execution."""
    # Add tool-specific validation logic
    return True

# Main execution
if validate_document_requirements():
    try:
        view_model = MainViewModel(doc)
        view = MainView(view_model)
        view.show(modal=False)
    except Exception as e:
        alert("Tool initialization failed: {}".format(str(e)), "Error")
        Debug.WriteLine("Error: {}".format(str(e)))
```

## Directory Structure Standards
```
tool_name.pushbutton/
├── tool_name_script.py     # Main entry point
├── bundle.yaml             # pyRevit configuration
├── icon.png               # Tool icon (16x16 or 32x32)
├── README.md              # Optional documentation
└── lib/                   # All supporting code
    ├── __init__.py        # Package exports
    ├── ui/                # WPF views and XAML
    ├── viewmodel/         # MVVM ViewModels
    ├── services/          # Business logic
    ├── models/            # Data models
    ├── commands/          # Command implementations
    ├── events/            # External event handlers
    └── utils/             # Utility functions
```

## Tool Categorization
- **datacontrol.stack/**: Parameter and data management tools
- **geometrycontrol.stack/**: Geometry manipulation and placement tools
- **utils.stack/**: General utility and mapping tools
- **QA.pulldown/**: Quality assurance and validation tools
- **Parameter.stack/**: Shared parameter management tools

## Error Handling and Validation
- Always validate document requirements before UI initialization
- Use `DCMvn.forms.alert` for user-facing error messages
- Log detailed errors using `Debug.WriteLine`
- Implement graceful fallbacks for missing dependencies

[pyrevit-script-template.py](mdc:pyrevit-script-template.py)