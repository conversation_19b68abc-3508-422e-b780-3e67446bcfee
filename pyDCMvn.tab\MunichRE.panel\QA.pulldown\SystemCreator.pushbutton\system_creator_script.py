# coding: utf-8
from MunichRE.excel_reader import RegisterExcelReader, MiniExcel
from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.revit.transaction import transaction_wrapper
from DCMvn.io import pick_excel_file
from DCMvn.forms.wpfforms import Select<PERSON>romList, ask_for_string
from lib.register_system import RegisterSystem
from lib.creator import create_system, create_workset, create_view_template_with_filters

# Get output for HTML printing
output = get_output()

@transaction_wrapper(message="Create Systems, Worksets, and View Template")
def create_systems_and_template():
    """
    Main function to create systems, worksets, and a view template with filters.
    This function processes all system registers and creates the necessary Revit elements.
    """
    try:
        # Load system data from Excel
        output.print_html('<h3 style="color:blue;"> :bar_chart: Loading System Data</h3>')
        output.print_html('Loading system data from Excel...')
        
        excel_path = pick_excel_file(title="Select System Register Excel")
        if not excel_path:
            output.print_html('<strong style="color:red;">✗ No Excel file selected. Exiting.</strong>')
            return False
        available_sheets = MiniExcel.GetSheetNames(excel_path)
        selected_sheet = SelectFromList.show(context=available_sheets, title="Select Sheet", width=300, height=300)
        if not selected_sheet:
            output.print_html('<strong style="color:red;">✗ No sheet selected. Exiting.</strong>')
            return False
        start_cell = ask_for_string(prompt="Enter start cell (e.g., A1)", default="A1", title="Enter Start Cell")
        if not start_cell:
            output.print_html('<strong style="color:red;">✗ No start cell provided. Exiting.</strong>')
            return False
        data_reader = RegisterExcelReader(
            excel_path=excel_path,
            sheet_name=selected_sheet,
            start_cell=start_cell,
        )

        # Convert to RegisterSystem objects
        system_registers = [RegisterSystem(item) for item in data_reader.data]
        system_registers = SelectFromList.show(context=system_registers, 
                                               title="Select System Registers", 
                                               multiselect=True,
                                               width=400, height=400, name_attr="system_type")
        if not system_registers:
            output.print_html('<strong style="color:red;">✗ No system registers selected. Exiting.</strong>')
            return False
        output.print_html('<strong style="color:green;">✓ Loaded {} system registers</strong>'.format(len(system_registers)))

        # Get the current document
        doc = HOST_APP.doc

        # Track success/failure counts
        system_success_count = 0
        workset_success_count = 0

        # Process each system register
        output.insert_divider()
        output.print_html('<h3 style="color:blue;">⚙️ Processing System Registers</h3>')
        for i, system_register in enumerate(system_registers, 1):
            output.print_html('<div style="margin: 10px 0; padding: 8px; border-left: 3px solid #007acc;"><strong>Processing system {}/{}: {}</strong></div>'.format(
                i, len(system_registers), system_register.system_type))

            try:
                # Create system
                create_system(system_register, doc)
                system_success_count += 1
                output.print_html('&nbsp;&nbsp;<strong style="color:green;">✓ System created successfully</strong>')
            except Exception as e:
                output.print_html('&nbsp;&nbsp;<strong style="color:red;">✗ Error creating system:</strong> {}'.format(str(e)))

            try:
                # Create workset
                create_workset(system_register, doc)
                workset_success_count += 1
                output.print_html('&nbsp;&nbsp;<strong style="color:green;">✓ Workset created successfully</strong>')
            except Exception as e:
                output.print_html('&nbsp;&nbsp;<strong style="color:red;">✗ Error creating workset:</strong> {}'.format(str(e)))

        # Create view template with all filters
        output.insert_divider()
        output.print_html('<h3 style="color:blue;">🎨 Creating View Template with System Filters</h3>')
        try:
            # Template name is fixed as "400_Digital_3D View Filter Color_XX_CO"
            template = create_view_template_with_filters(system_registers, doc)

            if template:
                output.print_html('<strong style="color:green;">✓ View template "{}" created successfully with {} filters</strong>'.format(
                    template.Name, len(system_registers)))
                template_success = True
            else:
                output.print_html('<strong style="color:red;">✗ Failed to create view template</strong>')
                template_success = False
        except Exception as e:
            output.print_html('<strong style="color:red;">✗ Error creating view template:</strong> {}'.format(str(e)))
            template_success = False

        # Print summary
        output.insert_divider()
        output.print_html('<h2 style="color:blue; border-bottom: 2px solid #007acc; padding-bottom: 5px;">📋 OPERATION SUMMARY</h2>')

        # Create summary table
        output.print_html('<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">')
        output.print_html('<table style="width: 100%; border-collapse: collapse;">')
        output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Systems created:</td><td style="padding: 5px;"><strong style="color: {};">{}/{}</strong></td></tr>'.format(
            "green" if system_success_count == len(system_registers) else "orange", system_success_count, len(system_registers)))
        output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Worksets created:</td><td style="padding: 5px;"><strong style="color: {};">{}/{}</strong></td></tr>'.format(
            "green" if workset_success_count == len(system_registers) else "orange", workset_success_count, len(system_registers)))
        output.print_html('<tr><td style="padding: 5px; font-weight: bold;">View template created:</td><td style="padding: 5px;"><strong style="color: {};">{}</strong></td></tr>'.format(
            "green" if template_success else "red", "Yes" if template_success else "No"))
        output.print_html('</table>')
        output.print_html('</div>')

        if template_success:
            output.print_html('<div style="background-color: #e8f5e8; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0;">')
            output.print_html('<strong>📝 NOTE:</strong> The view template <strong>"400_Digital_3D View Filter Color_XX_CO"</strong> has been created with system filters.')
            output.print_html('<br>You can apply this template to specific views manually.')
            output.print_html('</div>')

        # Return True if at least some operations succeeded
        return (system_success_count > 0 or workset_success_count > 0 or template_success)

    except Exception as e:
        output.print_html('<h3 style="color:red;">💥 CRITICAL ERROR</h3>')
        output.print_html('<strong style="color:red;">Critical error in create_systems_and_template:</strong> {}'.format(str(e)))
        return False


if __name__ == "__main__":
    output.print_html('<h1 style="color:blue; text-align:center;"> :rocket: System Creator Tool</h1>')
    output.print_html('<div style="text-align:center; margin: 10px 0; padding: 10px; background-color: #e3f2fd; border-radius: 5px;">')
    output.print_html('<strong>Starting system creation with view template approach...</strong>')
    output.print_html('</div>')

    success = create_systems_and_template()

    output.insert_divider()
    if success:
        output.print_html('<div style="text-align:center; margin: 20px 0; padding: 15px; background-color: #e8f5e8; border-radius: 5px; border: 2px solid #28a745;">')
        output.print_html('<h2 style="color:green; margin: 0;">🎉 System Creation Completed Successfully!</h2>')
        output.print_html('</div>')
    else:
        output.print_html('<div style="text-align:center; margin: 20px 0; padding: 15px; background-color: #f8d7da; border-radius: 5px; border: 2px solid #dc3545;">')
        output.print_html('<h2 style="color:red; margin: 0;">:warning: System Creation Completed with Errors</h2>')
        output.print_html('<p style="margin: 5px 0;">Check the output above for details.</p>')
        output.print_html('</div>')

    # Close other output windows for cleaner presentation
    output.close_others()
