# coding: utf-8
"""Align the level of elements to the right level"""
import clr
import os
from System.Collections.Generic import List # noqa
from pyrevit import script
from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.core.framework import System, List, Type # noqa
from DCMvn.forms import alert
from DCMvn.forms.wpfforms import ask_for_string
from DCMvn.io import save_excel_file

from MunichRE.excel_reader import MiniExcel
from curve_handler import get_element_level
from validator import format_level_name, format_level_report, compare_levels, UNDEFINED_VALUE

clr.AddReference("System.Collections.Specialized")
from System.Collections.Specialized import OrderedDictionary # noqa

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq) # noqa

output = get_output()
logger = script.get_logger()
doc = HOST_APP.doc

# Constants
DEFAULT_SANITARY_OFFSET = 1500 # mm
PROJECT_BASE_POINT = 0

def _is_level_base_on_project_base_point(level):
    # type: (DB.Level) -> int
    level_type = level.Document.GetElement(level.GetTypeId())
    if level_type is not None:
        level_elevation_base = level_type.get_Parameter(DB.BuiltInParameter.LEVEL_RELATIVE_BASE_TYPE).AsInteger()
        return level_elevation_base == PROJECT_BASE_POINT
    return False

def _is_nested_element(ele):
    # type: (DB.Element) -> bool
    if isinstance(ele, DB.FamilyInstance):
        return ele.SuperComponent is not None

def _get_element_level(ele):
    try:
        sche_level = ele.get_Parameter(DB.BuiltInParameter.SCHEDULE_LEVEL_PARAM)
        if sche_level is not None and sche_level.AsElementId() != DB.ElementId.InvalidElementId:
            return doc.GetElement(sche_level.AsElementId())
        elif ele.LevelId != DB.ElementId.InvalidElementId:
            return doc.GetElement(ele.LevelId)
        elif ele.get_Parameter(DB.BuiltInParameter.FAMILY_LEVEL_PARAM) is not None:
            return doc.GetElement(ele.get_Parameter(DB.BuiltInParameter.FAMILY_LEVEL_PARAM).AsElementId())
        elif ele.get_Parameter(DB.BuiltInParameter.RBS_START_LEVEL_PARAM) is not None:
            return doc.GetElement(ele.get_Parameter(DB.BuiltInParameter.RBS_START_LEVEL_PARAM).AsElementId())
        elif hasattr(ele, 'Level' and ele.Level is not None):
            return ele.Level
        elif hasattr(ele, 'GenLevel') and ele.GenLevel is not None:
            return ele.GenLevel
        return None
    except Exception as e:
        logger.error("Error getting level for element {}: {}".format(ele.Id, str(e)))
        return None
    
def _get_name(item):
    """Get name of an item, handling Level objects and other types."""
    try:
        return item.Name
    except AttributeError:
        return UNDEFINED_VALUE
    
def _get_element_category(ele):
    try:
        return ele.Category.Name if ele.Category else UNDEFINED_VALUE
    except: # noqa
        return UNDEFINED_VALUE

def _get_level_at_location_height(ele, current_transform, ordered_levels):
    # type: (DB.Element, DB.Transform, List[DB.Level]) -> DB.Level | None
    location = ele.Location

    if location is None:
        return None

    if isinstance(ele.Location, DB.LocationPoint):
        location_point = ele.Location.Point
    else:
        location_curve = ele.Location.Curve
        sorted_points = sorted(location_curve.Tessellate(), key=lambda x: (x.X, x.Y, x.Z))
        location_point = sorted_points[0]
    
    location_height = current_transform.OfPoint(location_point).Z

    below_or_at_levels = ordered_levels.Where(lambda x: x.Elevation <= location_height).ToList()

    if below_or_at_levels.Count == 0:
        true_lev = ordered_levels.OrderBy(lambda x: x.Elevation).FirstOrDefault()
    else:
        true_lev = below_or_at_levels.OrderByDescending(lambda x: x.Elevation).FirstOrDefault()
    if true_lev is None:
        logger.error("No valid level found for element {} at height {}".format(output.linkify(ele.Id), location_height))
        return None
    return true_lev

def save_wrong_levels_report(grouped_ele):
    """
    Generate an Excel report for elements with wrong levels using MiniExcel.

    Args:
        grouped_ele: Dictionary structure containing elements with wrong levels,
                          organized by correct level and category
    """
    try:
        file_path = save_excel_file(title="Save Wrong Levels Report")
        if not file_path:
            output.print_html('<strong style="color:orange;"> :warning: Report not saved - no file path selected</strong>')
            return None

        report_data = []

        for true_level_key, categories in grouped_ele.items():
            for category, element_infos in categories.items():
                for element_info in element_infos:
                    guid = element_info[1]
                    origin_level_name = element_info[3]
                    element_id_str = element_info[2] if len(element_info) > 2 else ""

                    correct_level_formatted = true_level_key  # This is already formatted from the grouping

                    row_data = OrderedDictionary()
                    row_data["Guid"] = guid
                    row_data["ElementId"] = element_id_str
                    row_data["Category"] = category
                    row_data["CurrentLevel"] = origin_level_name
                    row_data["CorrectLevel"] = correct_level_formatted
                    row_data["Approver"] = ""
                    row_data["Reason"] = ""
                    report_data.append(row_data)

        if os.path.exists(file_path):
            os.remove(file_path)
        MiniExcel.SaveAs(file_path, report_data)

        output.print_html('<strong style="color:green;"> :check_mark: Wrong Levels Report saved successfully:</strong>')
        output.print_html('<div style="margin-left: 20px; color: blue;">{}</div>'.format(file_path))
        return file_path
    except:  # noqa
        output.print_html('<strong style="color:red;"> :x: Failed to save Wrong Levels Report</strong>')
        return None

def print_report(grouped_ele, not_equal_lev, not_equal_lev_count, level_undefined, level_undefined_count, total_ele):
    # type: (dict, list, dict, list, dict, int) -> None

    # Main report header with emoji icon
    output.print_html('<h2 style="color:blue; border-bottom: 2px solid #007acc; padding-bottom: 5px;"> :level_slider: LEVEL ALIGNMENT REPORT</h2>')

    for true_lev, categories in grouped_ele.items():
        output.insert_divider()
        formatted_level = format_level_report(true_lev)
        output.print_html('<h3 style="color: blue"> :direct_hit: Correct Level: {0}</h3>'.format(formatted_level))

        for category, elements in categories.items():
            output.print_html('<div style="display: flex; align-items: center; width: 100%; margin: 10px 0;"><strong style="color: #333;"> :package: Category: {0}</strong><span style="border-bottom: 1px solid #ccc; flex-grow: 1; margin-left: 10px;"></span></div>'.format(category))
            for element_info in elements:
                output.print_html('<div style="margin-left: 20px; padding: 5px 0;">{}</div>'.format(element_info[0]))

    output.insert_divider()
    output.print_html('<h3 style="color:blue;"> :clipboard: Summary Report</h3>')

    # Summary statistics with consistent styling
    output.print_html('<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">')
    output.print_html('<table style="width: 100%; border-collapse: collapse;">')
    output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Total Elements in Model:</td><td style="padding: 5px;"><strong style="color: blue;">{}</strong></td></tr>'.format(total_ele))
    output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Elements with Incorrect Level:</td><td style="padding: 5px;"><strong style="color: red;">{}</strong></td></tr>'.format(len(not_equal_lev)))
    output.print_html('<tr><td style="padding: 5px; font-weight: bold;">Elements with Undefined Level:</td><td style="padding: 5px;"><strong style="color: red;">{}</strong></td></tr>'.format(len(level_undefined)))
    output.print_html('</table>')
    output.print_html('</div>')

    # Detailed breakdown by level
    if not_equal_lev_count:
        output.print_html('<h4 style="color: #856404;"> :warning: Elements with Incorrect Levels by Target Level:</h4>')
        output.print_html('<div style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin: 10px 0;">')
        for level, count in sorted(not_equal_lev_count.items()):
            output.print_html('• Level {}: <strong style="color: red;">{}</strong>'.format(level, count))
        output.print_html('</div>')

    if level_undefined_count:
        output.print_html('<h4 style="color: #856404;"> :warning: Elements with Undefined Levels:</h4>')
        output.print_html('<div style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin: 10px 0;">')
        for level, count in sorted(level_undefined_count.items()):
            output.print_html('• Level {}: <strong style="color: red;">{}</strong>'.format(level, count))
        output.print_html('</div>')

    output.close_others()

    # Ask if user wants to save report
    if grouped_ele and len(not_equal_lev) > 0:
        from DCMvn.forms import alert
        save_report = alert("Do you want to save a report of elements with wrong levels?", yes=True, no=True)
        if save_report:
            save_wrong_levels_report(grouped_ele)

def main():
    # ask user for offset value
    offset_value = ask_for_string(prompt="Enter offset value (mm):", default="1500", title="Enter Offset Value")
    try:
        offset_value = float(offset_value)
    except ValueError:
        result = alert("Invalid offset value. Please enter a number.\n Use default value 1500 mm?", yes=True, no=True)
        if result:
            offset_value = DEFAULT_SANITARY_OFFSET
        else:
            return

    # ask user for System Classification
    system_classification = ask_for_string(prompt="Enter System Classification:", default="Sanitary", title="Enter System Classification for Offset")
    if system_classification is None or system_classification == "":
        result = alert("Invalid System Classification. Please enter a valid System Classification.\n Use default value Sanitary?", yes=True, no=True)
        if result:
            system_classification = "Sanitary"
        else:
            return


    # Get all Levels
    levels = DB.FilteredElementCollector(doc).OfClass(DB.Level).ToElements().OrderByDescending(
        lambda x: x.Elevation)  # type: List[DB.Level]

    are_all_base_on_project = all(_is_level_base_on_project_base_point(level) for level in levels)
    are_all_base_on_survey_point = all(not _is_level_base_on_project_base_point(level) for level in levels)
    if not are_all_base_on_project and not are_all_base_on_survey_point:
        alert("All levels type must be based on either Project Base Point or Survey Point", warn_icon=True, exitscript=True)

    # transform
    if are_all_base_on_survey_point:
        transform = doc.ActiveProjectLocation.GetTotalTransform().Inverse  # type: DB.Transform
    else:
        transform = DB.Transform.CreateTranslation(DB.BasePoint.GetProjectBasePoint(doc).Position)  # type: DB.Transform

    # Get all the elements in the document
    class_filter = DB.ElementMulticlassFilter(
        List[Type]([clr.GetClrType(DB.FamilyInstance), clr.GetClrType(DB.MEPCurve)]))
    insulation_filter = DB.ElementMulticategoryFilter(List[DB.BuiltInCategory]([DB.BuiltInCategory.OST_DuctInsulations,
                                                                                DB.BuiltInCategory.OST_PipeInsulations]))
    insulation = DB.FilteredElementCollector(doc).WherePasses(
        insulation_filter).WhereElementIsNotElementType().ToElementIds()
    if insulation and len(insulation) > 0:
        elements = (DB.FilteredElementCollector(doc, doc.ActiveView.Id)
                    .WherePasses(class_filter)
                    .Excluding(insulation)
                    .Where(lambda x: not _is_nested_element(x))
                    .ToList())
    else:
        elements = (DB.FilteredElementCollector(doc, doc.ActiveView.Id)
                    .WherePasses(class_filter)
                    .Where(lambda x: not _is_nested_element(x))
                    .ToList())

    total_elements = len(elements)
    element_default_level_dict = {item.Id: _get_element_level(item) for item in elements}
    element_right_level_dict = {item.Id: get_element_level(item, transform, levels, offset_value / 304.8, system_classification) for item in elements}

    # Check Raw Level and Right Level
    not_equal_levels = []
    level_null = []
    not_equal_levels_count = {}
    level_null_count = {}
    for elementId, origin_level in element_default_level_dict.items():
        right_level = element_right_level_dict.get(elementId) # this can be Level, List[Level] or None
        right_level_name = format_level_name(right_level)

        if right_level is None and origin_level is None:
            level_null.append(elementId)
            level_null_count[UNDEFINED_VALUE] = level_null_count.get(UNDEFINED_VALUE, 0) + 1
        if right_level is None or origin_level is None:
            not_equal_levels.append((elementId, origin_level, right_level))
            not_equal_levels_count[right_level_name] = not_equal_levels_count.get(right_level_name, 0) + 1
        elif not compare_levels(origin_level, right_level):
            not_equal_levels.append((elementId, origin_level, right_level))
            not_equal_levels_count[right_level_name] = not_equal_levels_count.get(right_level_name, 0) + 1

    # Fix the levels
    grouped_elements = {}
    with DB.Transaction(doc, "Correct Levels") as t:
        t.Start()
        for elementId, origin_level, true_level in not_equal_levels:

            element = doc.GetElement(elementId)
            category_name = _get_element_category(element)
            element_guid = element.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString() if element.get_Parameter(
                DB.BuiltInParameter.IFC_GUID) else "N/A"
            # Disable auto-fix for now

            # if __shiftclick__:  # noqa # type: ignore
            #     can_set_level = True
            #     # Handle multiple levels by using the first level directly
            #     primary_level = None
            #     try:
            #         if true_level is None:
            #             primary_level = None
            #         elif isinstance(true_level, DB.Level):
            #             # Single level
            #             primary_level = true_level
            #         elif hasattr(true_level, 'Count'):  # .NET List
            #             if true_level.Count > 0:
            #                 primary_level = true_level[0]
            #         elif hasattr(true_level, '__len__'):  # Python list
            #             if len(true_level) > 0:
            #                 primary_level = true_level[0]
            #     except Exception:
            #         # Silent exception handling
            #         primary_level = None

            #     if primary_level is not None:
            #         try:
            #             if isinstance(element, DB.MEPCurve):
            #                 element.get_Parameter(DB.BuiltInParameter.RBS_START_LEVEL_PARAM).Set(primary_level.Id)
            #             elif isinstance(element, DB.FamilyInstance):
            #                 location = element.Location.Point.Z

            #                 schedule_level = element.get_Parameter(DB.BuiltInParameter.INSTANCE_SCHEDULE_ONLY_LEVEL_PARAM)
            #                 family_level = element.get_Parameter(DB.BuiltInParameter.FAMILY_LEVEL_PARAM)

            #                 if schedule_level is not None and schedule_level.AsElementId() != DB.ElementId.InvalidElementId:
            #                     schedule_level.Set(primary_level.Id)
            #                 elif family_level is not None and family_level.AsElementId() != DB.ElementId.InvalidElementId:
            #                     family_level.Set(primary_level.Id)

            #                 element.get_Parameter(DB.BuiltInParameter.INSTANCE_ELEVATION_PARAM).Set(
            #                     location - primary_level.Elevation)
            #         except Exception:
            #             # Silent exception handling as per user preference
            #             can_set_level = False
            #     else:
            #         can_set_level = False

            #     fix_status = "<span style='color:red;'>Failed</span>" if not can_set_level else "<span style='color:green;'>Success</span>"
            #     message = "{} - Original Level: <strong style=\"color: red\">{}</strong> - Fix Status: {}" \
            #         .format(output.linkify(elementId, element_guid), _get_name(origin_level), fix_status)
            # else:
            message = "{} - Original Level: <strong style=\"color: red\">{}</strong>" \
                .format(output.linkify(elementId, element_guid), _get_name(origin_level))

            # Grouping by True Level and then Category
            level_name = format_level_name(true_level)
            origin_level_name = format_level_name(origin_level)

            if level_name not in grouped_elements:
                grouped_elements[level_name] = {}
            if category_name not in grouped_elements[level_name]:
                grouped_elements[level_name][category_name] = []

            # grouped_elements[level_name][category_name].append(
            #     (message, element_guid, origin_level_name)
            # )
            grouped_elements[level_name][category_name].append(
                (message, element_guid,str(elementId), origin_level_name)
            )
        t.Commit()

    print_report(grouped_elements, not_equal_levels, not_equal_levels_count, level_null, level_null_count,
                 total_elements)

if __name__ == "__main__":
    main()
