# coding: utf-8
import clr
import os
from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.core.framework import System, List, Debug
from DCMvn.forms import alert

from lib.ui import MainView
from lib.viewmodel import MainViewModel
from lib.utils import get_element_physical_filter

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

doc = HOST_APP.doc
output = get_output()

def validate_document_requirements():
    """Validate document has required elements before proceeding."""
    try:
        # Add your document validation logic here
        physical_filter = get_element_physical_filter(doc)
        elements = (DB.FilteredElementCollector(doc)
                   .WhereElementIsNotElementType()
                   .WherePasses(physical_filter)
                   .ToElements())
        
        if not elements or len(elements) == 0:
            alert("Document validation failed.\n\nPlease ensure the document contains required elements.", 
                  "Validation Error")
            return False
        return True
    except Exception as e:
        alert("An error occurred during validation:\n\n" + str(e), "Validation Error")
        return False

# Main execution
if validate_document_requirements():
    try:
        view_model = MainViewModel(doc)
        view = MainView(view_model)
        view.show(modal=False)
    except Exception as e:
        alert("Failed to initialize application:\n\n" + str(e), "Initialization Error")
        Debug.WriteLine("Application initialization error: {}".format(str(e)))