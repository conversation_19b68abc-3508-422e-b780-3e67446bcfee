# coding: utf-8
from DCMvn.core import DB
from DCMvn.core.framework import Debug

class ElementWrapper(object):
    """Base pattern for wrapping Revit elements with additional functionality."""
    
    def __init__(self, element, document_wrapper=None):
        # type: (DB.Element, DocumentWrapper) -> None
        if not element or not element.IsValidObject:
            raise ValueError("Element must be valid")
            
        self.element = element
        self.document_wrapper = document_wrapper
        self._cached_properties = {}
        
    @property 
    def element_id(self):
        # type: () -> DB.ElementId
        """Get element ID safely."""
        return self.element.Id if self.element else None
        
    @property
    def is_valid(self):
        # type: () -> bool
        """Check if wrapped element is still valid."""
        try:
            return self.element and self.element.IsValidObject
        except Exception:
            return False
            
    def get_parameter_value(self, parameter_name):
        # type: (str) -> object
        """Get parameter value with error handling."""
        try:
            if not self.is_valid:
                return None
                
            param = self.element.LookupParameter(parameter_name)
            if param and param.HasValue:
                if param.StorageType == DB.StorageType.String:
                    return param.AsString()
                elif param.StorageType == DB.StorageType.Integer:
                    return param.AsInteger()
                elif param.StorageType == DB.StorageType.Double:
                    return param.AsDouble()
                elif param.StorageType == DB.StorageType.ElementId:
                    return param.AsElementId()
            return None
        except Exception as ex:
            Debug.WriteLine("Error getting parameter {}: {}".format(parameter_name, str(ex)))
            return None
            
    def _get_cached_geometry_property(self, property_name, calculator_func):
        # type: (str, callable) -> object
        """Get cached geometry property with coordinate transformation."""
        cache_key = "geometry_{}".format(property_name)
        if cache_key not in self._cached_properties:
            try:
                raw_value = calculator_func()
                # Apply transformation if this is from a linked document
                if raw_value and self.document_wrapper and self.document_wrapper.is_linked:
                    if isinstance(raw_value, DB.XYZ):
                        raw_value = self.document_wrapper.transform_point(raw_value)
                    elif isinstance(raw_value, DB.BoundingBoxXYZ):
                        raw_value = self.document_wrapper.transform_bounding_box(raw_value)
                self._cached_properties[cache_key] = raw_value
            except Exception as ex:
                Debug.WriteLine("Error calculating geometry property {}: {}".format(property_name, str(ex)))
                self._cached_properties[cache_key] = None
        return self._cached_properties[cache_key]