<mah:MetroWindow
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
    Title="Xóa Filter trong Revit" 
    Width="600" 
    Height="500" 
    WindowStartupLocation="CenterOwner"
    ResizeMode="CanResize">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" 
                   Text="Công cụ xóa Filter" 
                   FontSize="20" 
                   FontWeight="Bold" 
                   Margin="0,0,0,16"/>
        
        <!-- Filter Summary -->
        <Grid Grid.Row="1" Margin="0,0,0,12">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBlock Text="Tổng số filters: " FontWeight="SemiBold"/>
                <TextBlock Text="{Binding total_count}" FontWeight="Bold" Foreground="Blue"/>
                <TextBlock Text=" | Đã chọn: " Margin="16,0,0,0" FontWeight="SemiBold"/>
                <TextBlock Text="{Binding selected_count}" FontWeight="Bold" Foreground="Red"/>
            </StackPanel>
            
            <!-- Loading indicator -->
            <ProgressBar Grid.Column="1" 
                         Width="100" 
                         Height="20" 
                         IsIndeterminate="True" 
                         Visibility="{Binding is_loading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </Grid>
        
        <!-- Filter List -->
        <Border Grid.Row="2" 
                BorderBrush="LightGray" 
                BorderThickness="1" 
                CornerRadius="4">
            <ListView ItemsSource="{Binding filter_items}" 
                      SelectionMode="Extended"
                      ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <Grid Margin="4">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <CheckBox Grid.Column="0" 
                                      IsChecked="{Binding is_selected}" 
                                      Margin="0,0,12,0" 
                                      VerticalAlignment="Center"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding filter_name}" 
                                           FontWeight="SemiBold" 
                                           TextWrapping="Wrap"/>
                                <TextBlock Text="{Binding filter_type}" 
                                           FontSize="11" 
                                           Foreground="Gray" 
                                           Margin="0,2,0,0"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Column="2" 
                                       Text="✓" 
                                       FontSize="16" 
                                       FontWeight="Bold" 
                                       Foreground="Green" 
                                       VerticalAlignment="Center"
                                       Visibility="{Binding is_selected, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        </Grid>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>
        </Border>
        
        <!-- Control Buttons -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="0,16,0,0">
            
            <Button Content="Refresh" 
                    Width="80" 
                    Height="32" 
                    Margin="0,0,8,0"
                    Command="{Binding load_filters_command}"/>
            
            <Button Content="Chọn tất cả" 
                    Width="80" 
                    Height="32" 
                    Margin="0,0,8,0"
                    Command="{Binding select_all_command}"/>
            
            <Button Content="Bỏ chọn tất cả" 
                    Width="100" 
                    Height="32" 
                    Margin="0,0,8,0"
                    Command="{Binding deselect_all_command}"/>
        </StackPanel>
        
        <!-- Action Buttons -->
        <Grid Grid.Row="4" Margin="0,16,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- Warning text -->
            <TextBlock Grid.Column="0" 
                       Text="⚠️ Hành động xóa không thể hoàn tác!" 
                       FontSize="12" 
                       Foreground="Orange" 
                       VerticalAlignment="Center"/>
            
            <!-- Delete button -->
            <Button Grid.Column="1" 
                    Content="Xóa đã chọn" 
                    Width="100" 
                    Height="36" 
                    Margin="0,0,8,0"
                    Background="Red" 
                    Foreground="White"
                    FontWeight="Bold"
                    Command="{Binding delete_selected_command}"/>
            
            <!-- Close button -->
            <Button Grid.Column="2" 
                    Content="Đóng" 
                    Width="80" 
                    Height="36" 
                    IsCancel="True"
                    Click="OnCloseClick"/>
        </Grid>
    </Grid>
</mah:MetroWindow>

