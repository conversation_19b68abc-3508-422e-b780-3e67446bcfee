# coding: utf-8
import clr
from DCMvn.core import DB, RevitExceptions, get_output
from DCMvn.core.framework import System
from .register_system import RegisterSystem
from pyrevit import script

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

# Get output for HTML printing
output = get_output()

def create_system(system_register, doc):
    # type: (RegisterSystem, DB.Document) -> None
    try:
        new_system_type = DB.Plumbing.PipingSystemType.Create(
            doc, system_register.system_classification, system_register.system_type,
        )
    except RevitExceptions.ArgumentsInconsistentException:
        new_system_type = DB.Mechanical.MechanicalSystemType.Create(
            doc, system_register.system_classification, system_register.system_type,
        )
    except Exception as e:
        output.print_html('<strong style="color:red;">Error creating system type:</strong> {} - {}'.format(
            system_register.system_type, str(e)))

    if new_system_type:
        new_system_type.get_Parameter(
            DB.BuiltInParameter.RBS_SYSTEM_ABBREVIATION_PARAM
        ).Set(system_register.system_abbreviation)

def create_workset(system_register, doc):
    # type: (RegisterSystem, DB.Document) -> None
    # try:
        DB.Workset.Create(doc, system_register.system_type)
    # except Exception as e:
    #     output.print_html('<strong style="color:red;">Error creating workset:</strong> {} - {}'.format(
    #         system_register.system_type, str(e)))

def create_view_template_with_filters(system_registers, doc, template_name=None):
    # type: (list[RegisterSystem], DB.Document, str) -> DB.View
    """
    Create or update the specific view template "400_Digital_3D View Filter Color_XX_CO" with system filters.

    Args:
        system_registers: List of RegisterSystem objects containing filter configurations
        doc: Revit document
        template_name: Ignored - always uses "400_Digital_3D View Filter Color_XX_CO"

    Returns:
        DB.View: The created/updated view template, or None if creation failed
    """
    
    all_patterns = DB.FilteredElementCollector(doc)\
                    .OfClass(DB.FillPatternElement)\
                    .ToElements()
    solid_pattern = [i for i in all_patterns 
                        if i.GetFillPattern().IsSolidFill][0]
    try:
        # Use the specific template name
        template_name = "400_Digital_3D View Filter Color_XX_CO"

        # Check if template already exists
        view_template = (DB.FilteredElementCollector(doc)
                         .OfClass(DB.View)
                         .WhereElementIsNotElementType()
                         .Where(lambda x: x.IsTemplate and x.Name == template_name)
                         .FirstOrDefault())

        # Create new template if it doesn't exist
        if not view_template:
            # Find an existing 3D view to use as a base for the template
            existing_3d_views = (DB.FilteredElementCollector(doc)
                                 .OfClass(DB.View)
                                 .WhereElementIsNotElementType()
                                 .Where(lambda x: x.ViewType == DB.ViewType.ThreeD and not x.IsTemplate)
                                 .ToList())

            if not existing_3d_views:
                # If no 3D views exist, create one first
                view_family_type = (DB.FilteredElementCollector(doc)
                                    .OfClass(DB.ViewFamilyType)
                                    .FirstOrDefault(lambda x: x.ViewFamily == DB.ViewFamily.ThreeDimensional))

                # Create a temporary 3D view
                base_view = DB.View3D.CreateIsometric(doc, view_family_type.Id)
                base_view.Name = "Temp_3D_for_Template"
            else:
                # Use an existing 3D view as base
                base_view = existing_3d_views[0]

            try:
                view_template = base_view.CreateViewTemplate()
                view_template.Name = template_name

                output.print_html('<strong style="color:green;">✓ Created new view template:</strong> "{}"'.format(template_name))

            except Exception as e:
                output.print_html('<strong style="color:orange;">⚠ CreateViewTemplate failed:</strong> {}. Trying alternative approach...'.format(str(e)))

        # Process each system register to create filters and apply to template
        filters_applied = 0
        for system_register in system_registers:
            try:
                # Check if filter already exists
                filter_element = None
                existing_filters = DB.FilteredElementCollector(doc).OfClass(DB.ParameterFilterElement).ToElements()
                for existing_filter in existing_filters:
                    if existing_filter.Name == system_register.filter_name:
                        filter_element = existing_filter
                        break

                # Create filter if it doesn't exist
                if not filter_element:
                    # Get categories and create filter rule
                    categories = system_register.route_categories
                    
                    parameter_provider_duct = DB.ParameterValueProvider(DB.ElementId(DB.BuiltInParameter.RBS_DUCT_SYSTEM_TYPE_PARAM))
                    parameter_provider_pipe = DB.ParameterValueProvider(DB.ElementId(DB.BuiltInParameter.RBS_PIPING_SYSTEM_TYPE_PARAM))
                    parameter_provider_cabletray = DB.ParameterValueProvider(DB.ElementId(DB.BuiltInParameter.RBS_CTC_SERVICE_TYPE))
                    
                    if system_register.model_name == "Model SAN":
                        parameter_provider = parameter_provider_pipe
                    elif system_register.model_name == "Model HZG":
                        parameter_provider = parameter_provider_pipe
                    elif system_register.model_name == "Model KLT":
                        parameter_provider = parameter_provider_duct
                    elif system_register.model_name == "Model RLT":
                        parameter_provider = parameter_provider_duct
                    elif system_register.model_name == "Model SPR":
                        parameter_provider = parameter_provider_pipe
                    elif system_register.model_name == "Model ELE":
                        parameter_provider = parameter_provider_cabletray
                    else:
                        raise ValueError("Invalid model name: {}".format(system_register.model_name))

                    filter_rule = DB.FilterStringRule(parameter_provider, DB.FilterStringEquals(), system_register.system_type)
                    element_filter = DB.ElementParameterFilter(filter_rule)

                    # Create new filter
                    filter_element = DB.ParameterFilterElement.Create(doc, system_register.filter_name, categories, element_filter)

                # Apply filter to template if possible
                if filter_element:
                    # Create override settings
                    color_string = system_register.filter_color.split("-")
                    color = DB.Color(int(color_string[0]), int(color_string[1]), int(color_string[2]))

                    override_setting = DB.OverrideGraphicSettings()
                    override_setting.SetProjectionLineColor(color)
                    override_setting.SetSurfaceForegroundPatternColor(color)
                    override_setting.SetSurfaceForegroundPatternId(solid_pattern.Id)
                    override_setting.SetCutLineColor(DB.Color(0, 0, 0))  # Black color for lines

                    # Apply to template
                    view_template.SetFilterOverrides(filter_element.Id, override_setting)
                    filters_applied += 1

            except Exception as e:
                output.print_html('<strong style="color:red;">✗ Error processing system "{}":</strong> {}'.format(system_register.system_type, str(e)))
                continue

        output.print_html('<strong style="color:green;">✓ Successfully created/updated view template:</strong> "{}" with <strong>{}</strong> filters applied'.format(
            template_name, filters_applied))
        return view_template

    except Exception as e:
        output.print_html('<strong style="color:red;">✗ Error creating view template:</strong> {}'.format(str(e)))
        return None