# coding: utf-8
"""
TabooZone QA Many-to-Many Mockup Demo for pyRevit
Demonstrates complex many-to-many relationships using pyRevit output.print_html()

This script creates mockup data and renders it using the same patterns as
the existing html_reporter.py to show how the report would appear in production.
"""

from pyrevit import output
from pyrevit import script

# Mock classes to simulate the DTO structure
class MockElementInfo(object):
    def __init__(self, element_id, display_string):
        self.element_id = element_id
        self.display_string = display_string
        self.element = None  # Would be DB.Element in real scenario

class MockGuidPair(object):
    def __init__(self, guid, element_info):
        self.guid = guid
        self.element_info = element_info

class MockMatchedPair(object):
    def __init__(self, guid, arc_info, mass_info):
        self.guid = guid
        self.arc_info = arc_info
        self.mass_info = mass_info

class MockCentroidPair(object):
    def __init__(self, guid, distance_mm, within_tolerance):
        self.guid = guid
        self.distance_rounded = round(distance_mm, 1) if distance_mm else None
        self.within_tolerance = within_tolerance

class MockValidationResult(object):
    def __init__(self):
        self.arc_name = "Architectural Elements"
        self.mass_name = "Mass Elements"
        self.tolerance_mm = 50.0
        self.matched_pairs = []
        self.missing_pairs = []
        self.extra_pairs = []
        self.duplicate_arc_pairs = []
        self.duplicate_mass_pairs = []
        self.centroid_pairs = []
        self.total_arc_elements = 324
        self.total_mass_elements = 257
        self.has_issues = True

def html_escape(text):
    """HTML escape function matching html_reporter.py"""
    if text is None:
        return ""
    s = str(text)
    s = s.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")
    return s

def create_mockup_data():
    """Create mockup data demonstrating many-to-many relationships"""
    result = MockValidationResult()
    
    # Missing pairs - composite GUIDs with multiple ARC elements
    missing_arc_info = MockElementInfo(
        "DB.Element(156789), DB.Element(234567)",
        "ID:156789 | Walls | Basic Wall: Generic - 200mm, ID:234567 | Structural Columns | Concrete-Rectangular-Column: 400 x 600mm"
    )
    result.missing_pairs.append(MockGuidPair(
        "a1b2c3d4-e5f6-7890-abcd-ef1234567890;b2c3d4e5-f6g7-8901-bcde-f23456789012",
        missing_arc_info
    ))
    
    # Extra pairs
    extra_mass_info = MockElementInfo(
        "DB.Element(789012)",
        "ID:789012 | Mass | Mass: Conceptual Mass"
    )
    result.extra_pairs.append(MockGuidPair(
        "f6g7h8i9-j0k1-2345-fghi-************;g7h8i9j0-k1l2-3456-ghij-************",
        extra_mass_info
    ))
    
    # Duplicate ARC pairs
    dup_arc_info = MockElementInfo(
        "DB.Element(890123)",
        "ID:890123 | Walls | Basic Wall: Exterior - Brick on CMU"
    )
    result.duplicate_arc_pairs.append(MockGuidPair(
        "h8i9j0k1-l2m3-4567-hijk-************;i9j0k1l2-m3n4-5678-ijkl-************",
        dup_arc_info
    ))
    
    # Matched pairs with many-to-many relationships
    # Example 1: 2 ARC elements to 1 Mass
    arc_info_1 = MockElementInfo(
        "DB.Element(123001), DB.Element(123002)",
        "ID:123001 | Walls | Basic Wall: Generic - 200mm, ID:123002 | Structural Columns | Concrete-Rectangular-Column: 300 x 300mm"
    )
    mass_info_1 = MockElementInfo(
        "DB.Element(456001)",
        "ID:456001 | Mass | Mass: Conceptual Mass | Centroid: (12.5, 8.3, 3.0) | Thickness: 200mm"
    )
    guid_1 = "12345678-90ab-cdef-1234-567890abcdef;23456789-01bc-def1-2345-6789012bcdef"
    result.matched_pairs.append(MockMatchedPair(guid_1, arc_info_1, mass_info_1))
    result.centroid_pairs.append(MockCentroidPair(guid_1, 15.2, True))
    
    # Example 2: 3 ARC elements to 1 Mass
    arc_info_2 = MockElementInfo(
        "DB.Element(123003), DB.Element(123004), DB.Element(123005)",
        "ID:123003 | Floors | Floor: Generic - 150mm, ID:123004 | Walls | Curtain Wall: Storefront, ID:123005 | Structural Columns | Steel-W-Column: W12X65"
    )
    mass_info_2 = MockElementInfo(
        "DB.Element(456002)",
        "ID:456002 | Mass | Mass: Conceptual Mass | Centroid: (25.1, 15.7, 6.0) | Thickness: 150mm"
    )
    guid_2 = "34567890-12cd-ef12-3456-789012cdef12;45678901-23de-f123-4567-890123def123;56789012-34ef-1234-5678-901234ef1234"
    result.matched_pairs.append(MockMatchedPair(guid_2, arc_info_2, mass_info_2))
    result.centroid_pairs.append(MockCentroidPair(guid_2, 8.7, True))
    
    # Example 3: Shared ARC element (centroid failure)
    arc_info_3 = MockElementInfo(
        "DB.Element(123001), DB.Element(123006)",
        "ID:123001 | Walls | Basic Wall: Generic - 200mm, ID:123006 | Structural Columns | Steel-HSS-Column: HSS8X8X1/2"
    )
    mass_info_3 = MockElementInfo(
        "DB.Element(456003)",
        "ID:456003 | Mass | Mass: Conceptual Mass | Centroid: (18.9, 12.4, 4.5) | Thickness: 250mm"
    )
    guid_3 = "12345678-90ab-cdef-1234-567890abcdef;67890123-45ef-2345-6789-012345ef2345"
    result.matched_pairs.append(MockMatchedPair(guid_3, arc_info_3, mass_info_3))
    result.centroid_pairs.append(MockCentroidPair(guid_3, 75.3, False))
    
    return result

def format_composite_guid_for_pyrevit(guid):
    """Format composite GUID with line breaks for better readability in pyRevit"""
    if not guid or ';' not in guid:
        return html_escape(guid)

    parts = guid.split(';')
    formatted_parts = []
    for part in parts:
        formatted_parts.append(html_escape(part.strip()))

    return "<br>".join(formatted_parts)

def render_many_to_many_report():
    """Render the many-to-many TabooZone QA report using pyRevit output"""
    
    # Get pyRevit output window
    output_window = output.get_output()
    
    # Create mockup data
    validation_result = create_mockup_data()
    
    # Header - matching html_reporter.py style
    output_window.print_html("<h2>TabooZone QA Report - Many-to-Many Validation</h2>")
    output_window.print_html("<p><strong>ARC:</strong> " + html_escape(validation_result.arc_name) +
                           " | <strong>Mass:</strong> " + html_escape(validation_result.mass_name) +
                           " | <strong>Tolerance:</strong> " + html_escape(str(validation_result.tolerance_mm)) + " mm</p>")
    
    # Missing pairs section - matching html_reporter.py style
    output_window.print_html("<h3>Missing in Mass (ARC present, Mass missing)</h3>")
    if validation_result.missing_pairs:
        output_window.print_html("<table>")
        output_window.print_html("<tr><th>Composite GUID</th><th>ARC Elements</th><th>ARC Info</th></tr>")

        for pair in validation_result.missing_pairs:
            output_window.print_html("<tr>")
            output_window.print_html("<td>" + format_composite_guid_for_pyrevit(pair.guid) + "</td>")
            output_window.print_html("<td>" + html_escape(pair.element_info.element_id) + "</td>")
            output_window.print_html("<td>" + html_escape(pair.element_info.display_string).replace(", ", "<br>") + "</td>")
            output_window.print_html("</tr>")

        output_window.print_html("</table>")
    else:
        output_window.print_html("<p><i>No missing GUIDs.</i></p>")

    # Extra pairs section
    output_window.print_html("<h3>Extra in Mass (Mass present, ARC missing)</h3>")
    if validation_result.extra_pairs:
        output_window.print_html("<table>")
        output_window.print_html("<tr><th>Composite GUID</th><th>Mass Element</th><th>Mass Info</th></tr>")

        for pair in validation_result.extra_pairs:
            output_window.print_html("<tr>")
            output_window.print_html("<td>" + format_composite_guid_for_pyrevit(pair.guid) + "</td>")
            output_window.print_html("<td>" + output.linkify(pair.element_info.element_id) + "</td>")
            output_window.print_html("<td>" + html_escape(pair.element_info.display_string) + "</td>")
            output_window.print_html("</tr>")

        output_window.print_html("</table>")
    else:
        output_window.print_html("<p><i>No extra GUIDs.</i></p>")

    # Duplicate ARC pairs section
    output_window.print_html("<h3>Duplicate GUIDs in ARC</h3>")
    if validation_result.duplicate_arc_pairs:
        output_window.print_html("<table>")
        output_window.print_html("<tr><th>Composite GUID</th><th>ARC Element</th><th>ARC Info</th></tr>")

        for pair in validation_result.duplicate_arc_pairs:
            output_window.print_html("<tr>")
            output_window.print_html("<td>" + format_composite_guid_for_pyrevit(pair.guid) + "</td>")
            output_window.print_html("<td>" + html_escape(pair.element_info.element_id) + "</td>")
            output_window.print_html("<td>" + html_escape(pair.element_info.display_string) + "</td>")
            output_window.print_html("</tr>")

        output_window.print_html("</table>")
    else:
        output_window.print_html("<p><i>No duplicate ARC GUIDs.</i></p>")

    # Duplicate Mass pairs section
    output_window.print_html("<h3>Duplicate GUIDs in Mass</h3>")
    output_window.print_html("<p><i>No duplicate Mass GUIDs.</i></p>")

    # Matched pairs section with centroid integration
    output_window.print_html("<h3>Matched GUID Pairs</h3>")
    if validation_result.matched_pairs:
        # Build centroid lookup
        cp_map = {}
        for cp in validation_result.centroid_pairs:
            cp_map[cp.guid] = cp

        output_window.print_html("<table>")
        output_window.print_html("<tr><th>Composite GUID</th><th>ARC Info</th><th>Mass Element</th><th>Mass Info</th><th>Distance (mm)</th><th>Result</th></tr>")

        for pair in validation_result.matched_pairs:
            cp = cp_map.get(pair.guid)
            if cp:
                result_text = "Passed" if cp.within_tolerance else "Failed"
                distance_text = str(cp.distance_rounded) if cp.distance_rounded is not None else "-"
            else:
                result_text = ""
                distance_text = "-"

            output_window.print_html("<tr>")
            output_window.print_html("<td>" + format_composite_guid_for_pyrevit(pair.guid) + "</td>")
            output_window.print_html("<td>" + html_escape(pair.arc_info.display_string).replace(", ", "<br>") + "</td>")
            output_window.print_html("<td>" + output.linkify(pair.mass_info.element_id) + "</td>")
            output_window.print_html("<td>" + html_escape(pair.mass_info.display_string) + "</td>")
            output_window.print_html("<td>" + html_escape(distance_text) + "</td>")
            output_window.print_html("<td>" + html_escape(result_text) + "</td>")
            output_window.print_html("</tr>")

        output_window.print_html("</table>")
    else:
        output_window.print_html("<p><i>No matched pairs.</i></p>")

    # Summary section - matching html_reporter.py style
    output_window.print_html("<h3>Summary</h3>")

    # Calculate summary stats
    matched = len(validation_result.matched_pairs)
    missing = len(validation_result.missing_pairs)
    extra = len(validation_result.extra_pairs)
    dup_arc = len(validation_result.duplicate_arc_pairs)
    dup_mass = len(validation_result.duplicate_mass_pairs)

    centroid_pass = sum(1 for cp in validation_result.centroid_pairs if cp.within_tolerance)
    centroid_fail = sum(1 for cp in validation_result.centroid_pairs if not cp.within_tolerance)

    # Summary using simple paragraph format like html_reporter.py
    output_window.print_html("<p><strong>:white_heavy_check_mark: Matched:</strong> " + str(matched) + "</p>")
    output_window.print_html("<p><strong>:cross_mark: Missing:</strong> " + str(missing) + "</p>")
    output_window.print_html("<p><strong>:cross_mark: Extra:</strong> " + str(extra) + "</p>")
    output_window.print_html("<p><strong>:warning: Duplicate ARC:</strong> " + str(dup_arc) + "</p>")
    output_window.print_html("<p><strong>:warning: Duplicate Mass:</strong> " + str(dup_mass) + "</p>")
    output_window.print_html("<p><strong>:white_heavy_check_mark: Centroid Passed:</strong> " + str(centroid_pass) + "</p>")
    output_window.print_html("<p><strong>:cross_mark: Centroid Failed:</strong> " + str(centroid_fail) + "</p>")

    # Document and rule information - matching html_reporter.py style
    output_window.print_html("<h3>Rule Information</h3>")
    output_window.print_html("<p><strong>Rule:</strong> TabooZone Many-to-Many Validation</p>")
    output_window.print_html("<p><strong>ARC Document:</strong> Munich_RE_Architectural_Model_v2.3.rvt</p>")
    output_window.print_html("<p><strong>Mass Document:</strong> Munich_RE_Mass_Study_v1.8.rvt</p>")
    output_window.print_html("<p><strong>Total ARC elements:</strong> " + str(validation_result.total_arc_elements) +
                           " | <strong>Total Mass elements:</strong> " + str(validation_result.total_mass_elements) + "</p>")

    # Overall status
    status_icon = ":cross_mark: FAILED" if validation_result.has_issues else ":white_heavy_check_mark: PASSED"
    output_window.print_html("<h3>Overall Status</h3>")
    output_window.print_html("<p><strong>" + status_icon + "</strong></p>")

    # Many-to-Many features demonstration
    output_window.print_html("<h3>Many-to-Many Relationship Features</h3>")
    output_window.print_html("<ul>")
    output_window.print_html("<li><strong>2-Element Composite:</strong> Mass 456001 relates to Wall + Column → 2-part GUID</li>")
    output_window.print_html("<li><strong>3-Element Composite:</strong> Mass 456002 relates to Floor + Wall + Column → 3-part GUID</li>")
    output_window.print_html("<li><strong>Shared Elements:</strong> Mass 456003 shares Wall(123001) with Mass 456001</li>")
    output_window.print_html("<li><strong>Improved Readability:</strong> GUIDs displayed with line breaks for better scanning</li>")
    output_window.print_html("</ul>")

    # Note section
    output_window.print_html("<p><strong>Note:</strong> This is a mockup demonstration of how the TabooZone QA report would appear "
                           "with complex many-to-many relationships in a production pyRevit environment. The composite GUID format "
                           "(semicolon-separated) allows tracking relationships where each Mass element corresponds to multiple ARC elements.</p>")

if __name__ == "__main__":
    render_many_to_many_report()
