# coding: utf-8
"""
TabooZone QA Many-to-Many Mockup Demo for pyRevit
Demonstrates complex many-to-many relationships using pyRevit output.print_html()

This script creates mockup data and renders it using the same patterns as
the existing html_reporter.py to show how the report would appear in production.
"""

from pyrevit import output
from pyrevit import script

# Mock classes to simulate the DTO structure
class MockElementInfo(object):
    def __init__(self, element_id, display_string):
        self.element_id = element_id
        self.display_string = display_string
        self.element = None  # Would be DB.Element in real scenario

class MockGuidPair(object):
    def __init__(self, guid, element_info):
        self.guid = guid
        self.element_info = element_info

class MockMatchedPair(object):
    def __init__(self, guid, arc_info, mass_info):
        self.guid = guid
        self.arc_info = arc_info
        self.mass_info = mass_info

class MockCentroidPair(object):
    def __init__(self, guid, distance_mm, within_tolerance):
        self.guid = guid
        self.distance_rounded = round(distance_mm, 1) if distance_mm else None
        self.within_tolerance = within_tolerance

class MockValidationResult(object):
    def __init__(self):
        self.arc_name = "Architectural Elements"
        self.mass_name = "Mass Elements"
        self.tolerance_mm = 50.0
        self.matched_pairs = []
        self.missing_pairs = []
        self.extra_pairs = []
        self.duplicate_arc_pairs = []
        self.duplicate_mass_pairs = []
        self.centroid_pairs = []
        self.total_arc_elements = 324
        self.total_mass_elements = 257
        self.has_issues = True

def html_escape(text):
    """HTML escape function matching html_reporter.py"""
    if text is None:
        return ""
    s = str(text)
    s = s.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")
    return s

def create_mockup_data():
    """Create mockup data demonstrating many-to-many relationships"""
    result = MockValidationResult()
    
    # Missing pairs - composite GUIDs with multiple ARC elements
    missing_arc_info = MockElementInfo(
        "DB.Element(156789), DB.Element(234567)",
        "ID:156789 | Walls | Basic Wall: Generic - 200mm, ID:234567 | Structural Columns | Concrete-Rectangular-Column: 400 x 600mm"
    )
    result.missing_pairs.append(MockGuidPair(
        "a1b2c3d4-e5f6-7890-abcd-ef1234567890;b2c3d4e5-f6g7-8901-bcde-f23456789012",
        missing_arc_info
    ))
    
    # Extra pairs
    extra_mass_info = MockElementInfo(
        "DB.Element(789012)",
        "ID:789012 | Mass | Mass: Conceptual Mass"
    )
    result.extra_pairs.append(MockGuidPair(
        "f6g7h8i9-j0k1-2345-fghi-************;g7h8i9j0-k1l2-3456-ghij-************",
        extra_mass_info
    ))
    
    # Duplicate ARC pairs
    dup_arc_info = MockElementInfo(
        "DB.Element(890123)",
        "ID:890123 | Walls | Basic Wall: Exterior - Brick on CMU"
    )
    result.duplicate_arc_pairs.append(MockGuidPair(
        "h8i9j0k1-l2m3-4567-hijk-************;i9j0k1l2-m3n4-5678-ijkl-************",
        dup_arc_info
    ))
    
    # Matched pairs with many-to-many relationships
    # Example 1: 2 ARC elements to 1 Mass
    arc_info_1 = MockElementInfo(
        "DB.Element(123001), DB.Element(123002)",
        "ID:123001 | Walls | Basic Wall: Generic - 200mm, ID:123002 | Structural Columns | Concrete-Rectangular-Column: 300 x 300mm"
    )
    mass_info_1 = MockElementInfo(
        "DB.Element(456001)",
        "ID:456001 | Mass | Mass: Conceptual Mass | Centroid: (12.5, 8.3, 3.0) | Thickness: 200mm"
    )
    guid_1 = "12345678-90ab-cdef-1234-567890abcdef;23456789-01bc-def1-2345-6789012bcdef"
    result.matched_pairs.append(MockMatchedPair(guid_1, arc_info_1, mass_info_1))
    result.centroid_pairs.append(MockCentroidPair(guid_1, 15.2, True))
    
    # Example 2: 3 ARC elements to 1 Mass
    arc_info_2 = MockElementInfo(
        "DB.Element(123003), DB.Element(123004), DB.Element(123005)",
        "ID:123003 | Floors | Floor: Generic - 150mm, ID:123004 | Walls | Curtain Wall: Storefront, ID:123005 | Structural Columns | Steel-W-Column: W12X65"
    )
    mass_info_2 = MockElementInfo(
        "DB.Element(456002)",
        "ID:456002 | Mass | Mass: Conceptual Mass | Centroid: (25.1, 15.7, 6.0) | Thickness: 150mm"
    )
    guid_2 = "34567890-12cd-ef12-3456-789012cdef12;45678901-23de-f123-4567-890123def123;56789012-34ef-1234-5678-901234ef1234"
    result.matched_pairs.append(MockMatchedPair(guid_2, arc_info_2, mass_info_2))
    result.centroid_pairs.append(MockCentroidPair(guid_2, 8.7, True))
    
    # Example 3: Shared ARC element (centroid failure)
    arc_info_3 = MockElementInfo(
        "DB.Element(123001), DB.Element(123006)",
        "ID:123001 | Walls | Basic Wall: Generic - 200mm, ID:123006 | Structural Columns | Steel-HSS-Column: HSS8X8X1/2"
    )
    mass_info_3 = MockElementInfo(
        "DB.Element(456003)",
        "ID:456003 | Mass | Mass: Conceptual Mass | Centroid: (18.9, 12.4, 4.5) | Thickness: 250mm"
    )
    guid_3 = "12345678-90ab-cdef-1234-567890abcdef;67890123-45ef-2345-6789-012345ef2345"
    result.matched_pairs.append(MockMatchedPair(guid_3, arc_info_3, mass_info_3))
    result.centroid_pairs.append(MockCentroidPair(guid_3, 75.3, False))
    
    return result

def format_composite_guid_for_pyrevit(guid):
    """Format composite GUID with line breaks for better readability in pyRevit"""
    if not guid or ';' not in guid:
        return html_escape(guid)
    
    parts = guid.split(';')
    formatted_parts = []
    for part in parts:
        formatted_parts.append("<span style='display:block;margin-bottom:2px;padding:1px 3px;background:#f8f9fa;border-radius:2px;font-family:Consolas,monospace;font-size:10px;'>" + html_escape(part.strip()) + "</span>")
    
    return "<div style='line-height:1.3;max-width:280px;'>" + "".join(formatted_parts) + "</div>"

def render_many_to_many_report():
    """Render the many-to-many TabooZone QA report using pyRevit output"""
    
    # Get pyRevit output window
    output_window = output.get_output()
    
    # Create mockup data
    validation_result = create_mockup_data()
    
    # Header
    header_html = (
        "<div style='font-family:Segoe UI, Arial, sans-serif;margin:8px 0;'>"
        "<h2 style='margin:4px 0 8px 0;color:#2c3e50;'>TabooZone QA Report - Many-to-Many Validation</h2>"
        "<div style='font-size:12px;color:#666;margin-bottom:16px;'>"
        "ARC: " + html_escape(validation_result.arc_name) + " | "
        "Mass: " + html_escape(validation_result.mass_name) + " | "
        "Tol: " + html_escape(str(validation_result.tolerance_mm)) + " mm"
        "</div>"
        "</div>"
    )
    output_window.print_html(header_html)
    
    # Missing pairs section
    output_window.print_html("<h3 style='margin:10px 0 6px 0;color:#34495e;'>Missing in Mass (ARC present, Mass missing)</h3>")
    if validation_result.missing_pairs:
        table_html = "<table style='border-collapse:collapse;width:100%;margin:8px 0;font-size:11px;'>"
        table_html += "<tr><th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>Composite GUID</th>"
        table_html += "<th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>ARC Elements</th>"
        table_html += "<th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>ARC Info</th></tr>"
        
        for pair in validation_result.missing_pairs:
            table_html += "<tr style='background:#fdecea;'>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + format_composite_guid_for_pyrevit(pair.guid) + "</td>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + html_escape(pair.element_info.element_id) + "</td>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + html_escape(pair.element_info.display_string).replace(", ", "<br>") + "</td>"
            table_html += "</tr>"
        
        table_html += "</table>"
        output_window.print_html(table_html)
    else:
        output_window.print_html("<i>No missing GUIDs.</i>")

    # Extra pairs section
    output_window.print_html("<h3 style='margin:10px 0 6px 0;color:#34495e;'>Extra in Mass (Mass present, ARC missing)</h3>")
    if validation_result.extra_pairs:
        table_html = "<table style='border-collapse:collapse;width:100%;margin:8px 0;font-size:11px;'>"
        table_html += "<tr><th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>Composite GUID</th>"
        table_html += "<th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>Mass Element</th>"
        table_html += "<th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>Mass Info</th></tr>"

        for pair in validation_result.extra_pairs:
            table_html += "<tr style='background:#fdecea;'>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + format_composite_guid_for_pyrevit(pair.guid) + "</td>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'><span style='color:#0066cc;text-decoration:underline;'>" + html_escape(pair.element_info.element_id) + "</span></td>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + html_escape(pair.element_info.display_string) + "</td>"
            table_html += "</tr>"

        table_html += "</table>"
        output_window.print_html(table_html)
    else:
        output_window.print_html("<i>No extra GUIDs.</i>")

    # Duplicate ARC pairs section
    output_window.print_html("<h3 style='margin:10px 0 6px 0;color:#34495e;'>Duplicate GUIDs in ARC</h3>")
    if validation_result.duplicate_arc_pairs:
        table_html = "<table style='border-collapse:collapse;width:100%;margin:8px 0;font-size:11px;'>"
        table_html += "<tr><th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>Composite GUID</th>"
        table_html += "<th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>ARC Element</th>"
        table_html += "<th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>ARC Info</th></tr>"

        for pair in validation_result.duplicate_arc_pairs:
            table_html += "<tr style='background:#fff4d6;'>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + format_composite_guid_for_pyrevit(pair.guid) + "</td>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + html_escape(pair.element_info.element_id) + "</td>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + html_escape(pair.element_info.display_string) + "</td>"
            table_html += "</tr>"

        table_html += "</table>"
        output_window.print_html(table_html)
    else:
        output_window.print_html("<i>No duplicate ARC GUIDs.</i>")

    # Duplicate Mass pairs section
    output_window.print_html("<h3 style='margin:10px 0 6px 0;color:#34495e;'>Duplicate GUIDs in Mass</h3>")
    output_window.print_html("<i>No duplicate Mass GUIDs.</i>")

    # Matched pairs section with centroid integration
    output_window.print_html("<h3 style='margin:10px 0 6px 0;color:#34495e;'>Matched GUID Pairs</h3>")
    if validation_result.matched_pairs:
        # Build centroid lookup
        cp_map = {}
        for cp in validation_result.centroid_pairs:
            cp_map[cp.guid] = cp

        table_html = "<table style='border-collapse:collapse;width:100%;margin:8px 0;font-size:11px;'>"
        table_html += "<tr><th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>Composite GUID</th>"
        table_html += "<th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>ARC Info</th>"
        table_html += "<th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>Mass Element</th>"
        table_html += "<th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>Mass Info</th>"
        table_html += "<th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>Distance (mm)</th>"
        table_html += "<th style='text-align:left;padding:6px 8px;background:#f1f2f6;border:1px solid #ddd;font-weight:600;'>Result</th></tr>"

        for pair in validation_result.matched_pairs:
            cp = cp_map.get(pair.guid)
            if cp:
                bg_color = "#eafaf1" if cp.within_tolerance else "#fdecea"
                result_text = "Passed" if cp.within_tolerance else "Failed"
                distance_text = str(cp.distance_rounded) if cp.distance_rounded is not None else "-"
            else:
                bg_color = ""
                result_text = ""
                distance_text = "-"

            table_html += "<tr style='background:" + bg_color + ";'>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + format_composite_guid_for_pyrevit(pair.guid) + "</td>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + html_escape(pair.arc_info.display_string).replace(", ", "<br>") + "</td>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'><span style='color:#0066cc;text-decoration:underline;'>" + html_escape(pair.mass_info.element_id) + "</span></td>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + html_escape(pair.mass_info.display_string) + "</td>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + html_escape(distance_text) + "</td>"
            table_html += "<td style='padding:6px 8px;border:1px solid #ddd;vertical-align:top;'>" + html_escape(result_text) + "</td>"
            table_html += "</tr>"

        table_html += "</table>"
        output_window.print_html(table_html)
    else:
        output_window.print_html("<i>No matched pairs.</i>")

    # Summary section with emoji icons (pyRevit format)
    output_window.print_html("<h3 style='margin:12px 0 6px 0;color:#34495e;'>Summary</h3>")

    # Calculate summary stats
    matched = len(validation_result.matched_pairs)
    missing = len(validation_result.missing_pairs)
    extra = len(validation_result.extra_pairs)
    dup_arc = len(validation_result.duplicate_arc_pairs)
    dup_mass = len(validation_result.duplicate_mass_pairs)

    centroid_pass = sum(1 for cp in validation_result.centroid_pairs if cp.within_tolerance)
    centroid_fail = sum(1 for cp in validation_result.centroid_pairs if not cp.within_tolerance)

    # Summary cards using pyRevit-compatible styling
    summary_html = "<div style='margin:8px 0 16px 0;'>"

    def create_card(label, value, bg_color, icon):
        return ("<div style='display:inline-block;margin:4px 8px 4px 0;padding:6px 10px;border-radius:4px;"
                "border:1px solid rgba(0,0,0,0.1);min-width:120px;vertical-align:top;background:" + bg_color + ";'>"
                "<div style='font-size:11px;opacity:0.8;margin-bottom:2px;'>" + icon + " " + html_escape(label) + "</div>"
                "<div style='font-size:16px;font-weight:600;'>" + html_escape(str(value)) + "</div>"
                "</div>")

    summary_html += create_card("Matched", matched, "#d5f5e3", ":white_heavy_check_mark:")
    summary_html += create_card("Missing", missing, "#fadbd8", ":cross_mark:")
    summary_html += create_card("Extra", extra, "#fadbd8", ":cross_mark:")
    summary_html += create_card("Duplicate ARC", dup_arc, "#fcf3cf", ":warning:")
    summary_html += create_card("Duplicate Mass", dup_mass, "#fcf3cf", ":warning:")
    summary_html += create_card("Centroid Passed", centroid_pass, "#d5f5e3", ":white_heavy_check_mark:")
    summary_html += create_card("Centroid Failed", centroid_fail, "#fadbd8", ":cross_mark:")

    summary_html += "</div>"
    output_window.print_html(summary_html)

    # Document and rule information
    info_html = ("<div style='margin:8px 0;padding:8px;border-radius:4px;background:#f9f9f9;"
                 "border:1px solid rgba(0,0,0,0.08);font-size:11px;color:#444;'>"
                 "<div><b>Rule:</b> TabooZone Many-to-Many Validation</div>"
                 "<div><b>ARC Document:</b> Munich_RE_Architectural_Model_v2.3.rvt</div>"
                 "<div><b>Mass Document:</b> Munich_RE_Mass_Study_v1.8.rvt</div>"
                 "<div><b>Total ARC elements:</b> " + html_escape(str(validation_result.total_arc_elements)) +
                 " &nbsp;&nbsp; <b>Total Mass elements:</b> " + html_escape(str(validation_result.total_mass_elements)) + "</div>"
                 "</div>")
    output_window.print_html(info_html)

    # Overall status
    status_icon = ":cross_mark: FAILED" if validation_result.has_issues else ":white_heavy_check_mark: PASSED"
    status_html = ("<div style='margin:12px 0;padding:8px;border-radius:4px;background:#f4f6f6;font-weight:bold;font-size:12px;'>"
                   "<b>Overall Status:</b> " + status_icon + "</div>")
    output_window.print_html(status_html)

    # Many-to-Many features demonstration
    features_html = ("<div style='background:#f8f9fa;padding:12px;border-radius:4px;margin:8px 0;'>"
                     "<div style='margin:0 0 8px 0;color:#495057;font-size:13px;font-weight:600;'>Many-to-Many Relationship Features:</div>"
                     "<ul style='margin:4px 0;color:#6c757d;font-size:11px;line-height:1.4;'>"
                     "<li style='margin-bottom:3px;'><strong>2-Element Composite:</strong> Mass 456001 relates to Wall + Column → 2-part GUID</li>"
                     "<li style='margin-bottom:3px;'><strong>3-Element Composite:</strong> Mass 456002 relates to Floor + Wall + Column → 3-part GUID</li>"
                     "<li style='margin-bottom:3px;'><strong>Shared Elements:</strong> Mass 456003 shares Wall(123001) with Mass 456001</li>"
                     "<li style='margin-bottom:3px;'><strong>Improved Readability:</strong> GUIDs displayed with line breaks and visual separation</li>"
                     "</ul>"
                     "</div>")
    output_window.print_html(features_html)

    # Note section
    note_html = ("<div style='margin-top:16px;padding:8px;background:#e3f2fd;border-radius:4px;"
                 "border-left:3px solid #2196f3;font-size:11px;'>"
                 "<strong>Note:</strong> This is a mockup demonstration of how the TabooZone QA report would appear "
                 "with complex many-to-many relationships in a production pyRevit environment. The composite GUID format "
                 "(semicolon-separated) allows tracking relationships where each Mass element corresponds to multiple ARC elements."
                 "</div>")
    output_window.print_html(note_html)

if __name__ == "__main__":
    render_many_to_many_report()
