# coding: utf-8
"""
Công cụ xóa Filter trong Revit
Cho phép người dùng chọn và xóa các filters mà không cần mở từng filter một
"""

import clr
from pyrevit import script

from DCMvn.core import DB, HOST_APP
from DCMvn.core.framework import Debug
from DCMvn.forms import alert, wpfforms

# Setup
logger = script.get_logger()
doc = HOST_APP.doc


def get_all_filters():
    """Lấy tất cả filters trong document"""
    try:
        filters = []
        
        # Lấy View Filters (ParameterFilterElement)
        view_filters = list(DB.FilteredElementCollector(doc).OfClass(DB.ParameterFilterElement))
        for f in view_filters:
            if f and f.Name:
                filters.append((f, f.Name, "View Filter"))
        
        logger.info("Found {} view filters".format(len(view_filters)))
        return filters
        
    except Exception as e:
        logger.error("Error getting filters: {}".format(str(e)))
        return []


def main():
    """Main function - khởi chạy công cụ xóa filter"""
    try:
        logger.info("Starting Filter Deletion Tool...")
        
        # Kiểm tra document
        if not doc:
            alert("Không có document nào đang mở.", warn_icon=True)
            return
        
        if doc.IsReadOnly:
            alert("Document đang ở chế độ chỉ đọc.\nKhông thể xóa filters.", warn_icon=True)
            return
        
        # Lấy danh sách filters
        filters = get_all_filters()
        
        if not filters:
            alert("Không tìm thấy filter nào trong model.")
            return
        
        # Tạo danh sách tên để chọn
        filter_names = []
        for filter_elem, name, filter_type in filters:
            filter_names.append("{} ({})".format(name, filter_type))
        
        # Cho phép chọn nhiều filters
        selected = wpfforms.SelectFromList.show(
            filter_names,
            title="Chọn filters để xóa",
            multiselect=True
        )
        
        if not selected:
            logger.info("No filters selected")
            return
        
        # Tìm filters được chọn
        selected_filters = []
        for i, (filter_elem, name, filter_type) in enumerate(filters):
            display_name = "{} ({})".format(name, filter_type)
            if display_name in selected:
                selected_filters.append((filter_elem, name))
        
        if not selected_filters:
            return
        
        # Xác nhận xóa
        confirm_msg = "Bạn có chắc chắn muốn xóa {} filter(s)?\n\n".format(len(selected_filters))
        for filter_elem, name in selected_filters[:10]:
            confirm_msg += "- {}\n".format(name)
        if len(selected_filters) > 10:
            confirm_msg += "... và {} filters khác".format(len(selected_filters) - 10)
        
        if not alert(confirm_msg, yes=True, no=True):
            return
        
        # Xóa filters
        with DB.Transaction(doc, "Delete Selected Filters") as trans:
            trans.Start()
            
            deleted_count = 0
            failed_count = 0
            
            for filter_elem, name in selected_filters:
                try:
                    if filter_elem and filter_elem.IsValidObject:
                        doc.Delete(filter_elem.Id)
                        deleted_count += 1
                        logger.info("Deleted: {}".format(name))
                    else:
                        failed_count += 1
                except Exception as e:
                    failed_count += 1
                    logger.error("Failed to delete {}: {}".format(name, str(e)))
            
            trans.Commit()
            
            # Báo cáo kết quả
            result_msg = "Kết quả:\n"
            result_msg += "- Đã xóa: {} filters\n".format(deleted_count)
            if failed_count > 0:
                result_msg += "- Không thể xóa: {} filters".format(failed_count)
            
            alert(result_msg)
            logger.info("Filter deletion completed: {} deleted, {} failed".format(deleted_count, failed_count))
        
    except Exception as e:
        error_msg = "Lỗi khi chạy công cụ xóa filter: {}".format(str(e))
        logger.error(error_msg)
        
        import traceback
        logger.error("Full traceback: {}".format(traceback.format_exc()))
        
        alert(error_msg, warn_icon=True)


if __name__ == "__main__":
    main()
    
