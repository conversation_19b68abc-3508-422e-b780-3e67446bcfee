# coding: utf-8
import clr
import sys
import os
import System

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

try:  # noqa
    extension_libs = IN[0]  # noqa  # type: ignore
    current_workspace = IN[1]  # noqa  # type: ignore
    sys.path.append(current_workspace) if current_workspace not in sys.path else None
    [sys.path.append(lib) for lib in extension_libs if os.path.exists(lib) and lib not in sys.path]
except NameError:
    current_workspace = os.path.dirname(__file__)

from DCMvn.revit.transaction import transaction_wrapper
from DCMvn.core import IRONPY, DYNAMO
from DCMvn.core import DB, UI, HOST_APP
from DCMvn.core.framework import List, Debug
from DCMvn.forms import wpfforms, alert

if DYNAMO:
    clr.AddReference('RevitNodes')
    import Revit

    clr.ImportExtensions(Revit.GeometryConversion)
    clr.ImportExtensions(Revit.Elements)
    
    clr.AddReference('ProtoGeometry')
    from Autodesk.DesignScript.Geometry import Point, Line, Arc, Face, Plane, Vector, Surface
    
    clr.AddReference("RevitServices")
    import RevitServices
    from RevitServices.Transactions import TransactionManager
else:
    from DCMvn.core import get_output
    output = get_output()

doc = HOST_APP.doc
uidoc = HOST_APP.uidoc

# Constants
MreProjectIdentifier = "0061721_MRE"
IfcSpaceTypeParameter = "Export to IFC As"
IfcSpaceTypeValue = "IfcSpaceType"
LevelParameter = "AW_ARC.Geschossnummer"
SpaceNameParameter = "AW_ARC.Raumname"
SpaceNumberParameter = "AW_ARC.Raumnummer ARC"

def get_link_data(document):
    link_instance = (DB.FilteredElementCollector(document)
                     .OfClass(DB.RevitLinkInstance)
                     .WhereElementIsNotElementType()
                     .FirstOrDefault(lambda x: x.Name.Contains(MreProjectIdentifier)))  # type: DB.RevitLinkInstance
    if link_instance:
        link_doc = link_instance.GetLinkDocument()
        return (link_doc, link_instance.GetTotalTransform().Inverse)
    return (None, None)

def get_generic_spaces(document):
    collector = DB.FilteredElementCollector(document)
    spaces = (collector
              .OfCategory(DB.BuiltInCategory.OST_GenericModel)
              .WhereElementIsNotElementType()
              .Where(lambda x: x.LookupParameter(IfcSpaceTypeParameter).AsString() == IfcSpaceTypeValue)
              .ToList())
    return spaces

def get_space_faces(spaces):
    space_not_have_faces = []
    link_space_surfaces = {}
    for space in spaces:
        space_surfaces = space.ToDSType(True).Faces
        if not space_surfaces:
            space_not_have_faces.append(space)
            # Debug.WriteLine(space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString())
        else:
            link_space_surfaces[space] = space_surfaces
    return space_not_have_faces, link_space_surfaces
    
def get_bottom_face(space_surfaces):
    # List[Surface] -> Surface
    plane_faces = []
    for face in space_surfaces:
        face = face # type: Surface
        normal = face.NormalAtParameter(0.5, 0.5)  # type: Vector
        if normal.Cross(Vector.ByCoordinates(0, 0, 1)).Length < 0.001:
            plane_faces.append(face)
    
    # Find the face with the lowest Z coordinate (bottom face)
    if plane_faces:
        # Sort by Z coordinate of face normal vector
        bottom_faces = sorted(plane_faces, key=lambda f: f.PointAtParameter(0.5, 0.5).Z)
        # Return the face with lowest Z value
        return bottom_faces[0]
    return None

def tranform_curve(curve, transform):
    return DB.Line.CreateTransformed(curve.ToRevitType(), transform)

def create_separator(bottom_face, sketchplane, view, transform):
    curves = bottom_face.PerimeterCurves().ToList()
    curvearray = DB.CurveArray()
    for curve in curves:
        try:
            curvearray.Append(tranform_curve(curve, transform.Inverse))
        except: # type: ignore
            pass
    separatorarray = doc.Create.NewSpaceBoundaryLines(sketchplane, curvearray, view)
    return separatorarray

view_dict = {i.Name : i for i in DB.FilteredElementCollector(doc)
              .OfCategory(DB.BuiltInCategory.OST_Views)
              .WhereElementIsNotElementType()
              .Where(lambda x: x.ViewType == DB.ViewType.FloorPlan)
              .ToList()}
level_dict = {i.Name : i for i in DB.FilteredElementCollector(doc)
              .OfCategory(DB.BuiltInCategory.OST_Levels)
              .WhereElementIsNotElementType()
              .ToList()}

# @transaction_wrapper(message="Transaction Message")
def main():
    TransactionManager.Instance.EnsureInTransaction(doc)
    
    link_doc, transform = get_link_data(doc)
    link_spaces = get_generic_spaces(link_doc)
    
    space_data = get_space_faces(link_spaces)
    space_not_have_faces = space_data[0] # type: List[DB.Element]
    link_space_surfaces = space_data[1] # type: dict[DB.Element, List[Face]]
    
    bottom_faces = []
    bottom_points = []
    for space, surfaces in link_space_surfaces.items():
        bottom_face = get_bottom_face(surfaces)
        if bottom_face:
            location = transform.Inverse.OfPoint(bottom_face.PointAtParameter(0.5, 0.5).ToRevitType())
            bottom_points.append(bottom_face.PointAtParameter(0.5, 0.5))
            sketchPlane = DB.SketchPlane.Create(doc, 
                DB.Plane.CreateByNormalAndOrigin(DB.XYZ.BasisZ, location))
            
            current_level = space.LookupParameter(LevelParameter).AsString()
            view = view_dict.get(current_level)
            level = level_dict.get(current_level)
            create_separator(bottom_face, sketchPlane, view, transform)
            new_space = doc.Create.NewSpace(level, DB.UV(location.X, location.Y))
            
            new_space.get_Parameter(DB.BuiltInParameter.IFC_GUID).Set(space.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString())
            new_space.get_Parameter(DB.BuiltInParameter.ROOM_NUMBER).Set(space.LookupParameter(SpaceNumberParameter).AsString())
            new_space.get_Parameter(DB.BuiltInParameter.ROOM_NAME).Set(space.LookupParameter(SpaceNameParameter).AsString())
    
    TransactionManager.Instance.TransactionTaskDone()
    # Dynamo
    if DYNAMO:
        out_value = []  # use this list to append out values
        # out_value.append(link_spaces.Select(lambda x: x.ToDSType(True)).ToList())
        out_value.append(bottom_points)

        return out_value
    else:
        return "DCMvn"

OUT = main()

if not DYNAMO:
    output.close_others()
