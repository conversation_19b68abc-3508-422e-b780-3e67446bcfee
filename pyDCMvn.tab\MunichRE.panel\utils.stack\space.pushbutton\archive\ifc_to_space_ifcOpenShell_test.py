import json
import clr
import sys
import os
import System

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

try:  # noqa
    extension_libs = IN[0]  # noqa  # type: ignore
    current_workspace = IN[1]  # noqa  # type: ignore
    sys.path.append(current_workspace) if current_workspace not in sys.path else None
    [sys.path.append(lib) for lib in extension_libs if os.path.exists(lib) and lib not in sys.path]
except NameError:
    current_workspace = os.path.dirname(__file__)

clr.AddReference('ProtoGeometry')
from Autodesk.DesignScript.Geometry import Point, Line, Arc

from DCMvn.revit.transaction import transaction_wrapper
from DCMvn.core import IRONPY, DYNAMO
from DCMvn.core import DB, UI, HOST_APP
from DCMvn.core.framework import List, Debug
from DCMvn.forms import wpfforms, alert

if DYNAMO:
    clr.AddReference('RevitNodes')
    import Revit

    clr.ImportExtensions(Revit.GeometryConversion)
    clr.ImportExtensions(Revit.Elements)
else:
    from DCMvn.core import get_output
    output = get_output()

doc = HOST_APP.doc
uidoc = HOST_APP.uidoc

def to_feet(unit_mm):
    return DB.UnitUtils.ConvertToInternalUnits(unit_mm, DB.UnitTypeId.Meters)

def boundary_to_dynamo(boundary):
    curves = []
    for seg in boundary:
        if seg["type"] == "Line":
            p1 = Point.ByCoordinates(to_feet(seg["start"][0]), to_feet(seg["start"][1]), 0)
            p2 = Point.ByCoordinates(to_feet(seg["end"][0]), to_feet(seg["end"][1]), 0)
            try:
                curves.append(Line.ByStartPointEndPoint(p1, p2))
            except:
                continue
        elif seg["type"] == "Arc":
            # Reconstruct using center, radius, angles (assuming XY plane)
            import math
            center = Point.ByCoordinates(to_feet(seg["center"][0]), to_feet(seg["center"][1]), 0)
            r = seg["radius"]
            sa = seg["start_angle"]
            ea = seg["end_angle"]
            # Points for start/end of the arc
            p1 = Point.ByCoordinates(
                center.X + r * math.cos(sa),
                center.Y + r * math.sin(sa),
                0)
            p2 = Point.ByCoordinates(
                center.X + r * math.cos(ea),
                center.Y + r * math.sin(ea),
                0)
            # Dynamo's Arc.ByCenterPointRadiusAngle expects degrees
            arc = Arc.ByCenterPointRadiusAngle(center, r, math.degrees(sa), math.degrees(ea))
            curves.append(arc)
        elif seg["type"] == "Polyline":
            pts = [Point.ByCoordinates(to_feet(x), to_feet(y), 0) for x, y in seg["points"]]
            for i in range(len(pts)-1):
                curves.append(Line.ByStartPointEndPoint(pts[i], pts[i+1]))
    return curves

json_path = r"F:\DIG_GiangVu\workspace\pyDCMvn\IfcAnalyzer\source\05_IfcSpaceToRevit\ifcspace_boundaries_detailed.json"
with open(json_path, "r") as f:
    parsed_data = json.load(f)

all_boundaries = []
for gid, boundary in parsed_data.items():
    all_boundaries.extend(boundary_to_dynamo(boundary))

OUT = all_boundaries

# space_id = "3qATB4Kq53H9BN$vmrZmF7"  # Replace with your target GlobalId
# boundary = parsed_data.get(space_id, [])
# OUT = boundary_to_dynamo(boundary)