# coding: utf-8

from .base_detection_strategy import BaseDetectionStrategy
from ...models import RevitSpatial, BaseSpatial, BaseMep  # noqa: F401
from ...utils import sample_curve_points

class RevitSpatialStrategy(BaseDetectionStrategy):
    """Detection strategy for RevitSpatial elements (Rooms/Spaces)."""

    def detect(self, mep_element, mep_type_info, spatial_element, show_visual=False):
        # type: (BaseMep, dict, BaseSpatial, bool) -> tuple[bool, float]
        """Handle scenarios 1-5: Room/Space detection with optimized type checking."""
        
        if not isinstance(spatial_element, RevitSpatial):
            return False, 0
        
        if mep_type_info['is_family_instance']:
            if mep_type_info['has_location_curve']:
                return self._scenario_1_family_curve_revit_spatial(mep_element, mep_type_info, spatial_element, show_visual)
            elif mep_type_info['has_location_point']:
                return self._scenario_2_family_point_revit_spatial(mep_type_info, spatial_element, show_visual)
            else:
                return self._scenario_3_family_no_location_revit_spatial(mep_type_info, spatial_element, show_visual)
        elif mep_type_info['is_curve_element']:
            return self._scenario_4_curve_element_revit_spatial(mep_element, mep_type_info, spatial_element, show_visual)
        elif mep_type_info['is_direct_shape']:
            return self._scenario_5_directshape_revit_spatial(mep_type_info, spatial_element, show_visual)
        else:
            return self.check_geometry_spatial_intersection(mep_element, mep_type_info, spatial_element, show_visual)
            
    def _scenario_1_family_curve_revit_spatial(self, mep_element, mep_type_info, spatial_element, show_visual=False):
        # type: (BaseMep, dict, RevitSpatial, bool) -> tuple[bool, float]
        """Scenario 1: Room/Space vs FamilyInstance curve-based."""
        try:
            # Priority 1: Check room calculation points first (most accurate)
            is_match, distance = self.geometry_detector.check_points(self.get_detection_points(mep_type_info), spatial_element, show_visual)
            if is_match:
                return True, distance
            
            # Priority 2: Use curve sampling as fallback
            if not mep_type_info['has_location_curve'] or not mep_type_info['curve']:
                return False, 0
                
            curve = mep_type_info['curve']
            
            # Above check: sample start/mid/end, translate by -Z
            is_match, distance = self.geometry_detector.check_curve_above(curve, spatial_element)
            if is_match:
                return True, distance
                    
            # Nearby check: use appropriate proximity method based on spatial type
            is_match, distance = self.geometry_detector.check_intersection(mep_element, spatial_element, mep_type_info, show_visual)
            if is_match:
                return True, distance
                    
            # Direct curve sampling (fallback method)
            sample_points = sample_curve_points(curve, 3)  # start, mid, end
            for point in sample_points:
                is_match, distance = self.geometry_detector.check_points([point], spatial_element, show_visual)
                if is_match:
                    return True, distance     
            return False, 0   
        except:  # noqa
            return False, 0
            
    def _scenario_2_family_point_revit_spatial(self, mep_type_info, spatial_element, show_visual=False):
        # type: (dict, BaseSpatial, bool) -> tuple[bool, float]
        """Scenario 2: Room/Space vs FamilyInstance point-based."""
        try:
            is_match, distance = self.geometry_detector.check_points(self.get_detection_points(mep_type_info), spatial_element, show_visual)
            if is_match:
                return True, distance
            return False, 0
        except:  # noqa
            return False, 0
            
    def _scenario_3_family_no_location_revit_spatial(self, mep_type_info, spatial_element, show_visual=False):
        # type: (dict, BaseSpatial, bool) -> tuple[bool, float]
        """Scenario 3: Room/Space vs FamilyInstance no location."""
        try:
            is_match, distance = self.geometry_detector.check_points(self.get_detection_points(mep_type_info), spatial_element, show_visual)
            if is_match:
                return True, distance
            return False, 0
        except:  # noqa
            return False, 0
            
    def _scenario_4_curve_element_revit_spatial(self, mep_element, mep_type_info, spatial_element, show_visual=False):
        # type: (BaseMep, dict, BaseSpatial, bool) -> tuple[bool, float]
        """Scenario 4: Room/Space vs Curve Element (Duct/Pipe)."""
        try:
            is_match, distance = self.geometry_detector.check_points(self.get_detection_points(mep_type_info), spatial_element, show_visual)
            if is_match:
                return True, distance
            
            # Priority 2: Use curve sampling as fallback
            if not mep_type_info['has_location_curve'] or not mep_type_info['curve']:
                return False, 0
                
            curve = mep_type_info['curve']
            
            # Priority 3: Sample at 0%, 50%, 100%
            sample_points = sample_curve_points(curve, 3)
            
            # Priority 4: Above check: -Z shift then point-in test
            is_match, distance = self.geometry_detector.check_curve_above(curve, spatial_element)
            if is_match:
                return True, distance
                        
            # Priority 5: Nearby check: use appropriate proximity method based on spatial type
            is_match, distance = self.geometry_detector.check_intersection(mep_element, spatial_element, mep_type_info, show_visual)
            if is_match:
                return True, distance

            # Priority 6: Direct sampling (fallback method)
            for point in sample_points:
                is_match, distance = self.geometry_detector.check_points([point], spatial_element, show_visual)
                if is_match:
                    return True, distance
            return False, 0
        except:  # noqa
            return False, 0
            
    def _scenario_5_directshape_revit_spatial(self, mep_type_info, spatial_element, show_visual=False):
        # type: (dict, BaseSpatial, bool) -> tuple[bool, float]
        """Scenario 5: Room/Space vs DirectShape."""
        try:
            is_match, distance = self.geometry_detector.check_points(self.get_detection_points(mep_type_info), spatial_element, show_visual)
            if is_match:
                return True, distance  
            return False, 0
        except:  # noqa
            return False, 0