---
applyTo: "**/models/**/*.py"
---

# Model and Element Wrapper Patterns

## Model Architecture Guidelines
- **Domain Models**: Core business entities (BaseMep, RevitSpatial, DocumentWrapper)
- **Data Transfer Objects**: Simple containers (ParameterPair, DetectionResult, TargetCategory)
- **Configuration Objects**: Settings and options (AdvancedOptionsViewModel)
- **UI Binding Models**: Data specifically formatted for UI consumption

## Element Wrapper Implementation
Use this pattern for wrapping Revit elements:
```python
class ElementWrapper(object):
    def __init__(self, element, document_wrapper):
        # Always validate element
        if not element or not element.IsValidObject:
            raise ValueError("Invalid element provided")
        
        self.element = element
        self.document_wrapper = document_wrapper
        self._cached_geometry = None
        
    @property
    def geometry(self):
        if self._cached_geometry is None:
            self._cached_geometry = self._calculate_geometry()
        return self._cached_geometry
        
    def _calculate_geometry(self):
        # Implement geometry calculation with transformation
        pass
```

## DocumentWrapper Pattern
Always use this pattern for handling document context:
```python
class DocumentWrapper(object):
    def __init__(self, document, link_instance=None):
        self.document = document
        self.link_instance = link_instance
        self._transform = None
        
    @property
    def transform(self):
        if self._transform is None:
            if self.link_instance:
                self._transform = self.link_instance.GetTotalTransform()
            else:
                self._transform = DB.Transform.Identity
        return self._transform
```

## Property and Caching Best Practices
- Use `@property` decorators for computed values
- Implement lazy loading for expensive operations
- Cache geometry calculations in instance variables
- Provide fallback values for missing properties
- Always validate element before accessing properties

## Validation and Error Handling
- Validate all constructor parameters
- Check `element.IsValidObject` before operations
- Provide meaningful error messages
- Use defensive programming patterns
