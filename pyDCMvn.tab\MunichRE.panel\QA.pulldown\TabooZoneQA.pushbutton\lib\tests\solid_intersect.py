# coding: utf-8
import clr

from DCMvn.revit.selection import DSelection
from DCMvn.core import DB, UI, RevitExceptions  # noqa: F401
from DCMvn.core.framework import Trace, System
from DCMvn.revit.geometry import GetElementMergedSolid, compute_obb_to_solid, GetElementMeshes, GetMeshesBoundingBox, GetPointsFromGeometryObject, compute_aabb

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

def test_solid_intersection(uidoc):
    # type: (UI.UIDocument) -> tuple[DB.Solid, DB.Solid, DB.Solid]
    ele1 = DSelection(uidoc, is_linked=True).pick()
    ele2 = DSelection(uidoc, is_linked=False).pick()

    solid1 = GetElementMeshes(ele1) or GetElementMergedSolid(ele1)
    solid2 = GetElementMeshes(ele2) or GetElementMergedSolid(ele2)

    try:
        intersect_solid = DB.BooleanOperationsUtils.ExecuteBooleanOperation(solid1, solid2, DB.BooleanOperationsType.Intersect)
    except:
        obb_solid1 = compute_obb_to_solid(solid1, True)
        obb_solid2 = compute_obb_to_solid(solid2, True)
        intersect_solid = DB.BooleanOperationsUtils.ExecuteBooleanOperation(obb_solid1, 
                                                                                obb_solid2, DB.BooleanOperationsType.Intersect)
    
    # print("{} volumne {}".format(ele1.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString(), solid1.Volume))
    # print("{} volumne {}".format(ele2.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString(), solid2.Volume))
    # print(intersect_solid.Volume)


    return intersect_solid, solid1, solid2
