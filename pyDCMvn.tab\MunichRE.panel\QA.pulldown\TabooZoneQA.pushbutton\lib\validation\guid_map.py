# coding: utf-8
"""
Flexible GUID mapping utilities to support many-to-many relationships.
"""
from collections import defaultdict
from DCMvn.core.framework import Debug
from DCMvn.core import DB


class GuidMultiMap(object):
    """Maintain many-to-many associations of GUID -> elements in each domain."""
    def __init__(self):
        self.arc = defaultdict(list)
        self.mass = defaultdict(list)

    def add_arc(self, guid, element):
        # type: (str, DB.Element) -> None
        if guid:
            self.arc[guid].append(element)

    def add_mass(self, guid, element):
        # type: (str, DB.Element) -> None
        if guid:
            self.mass[guid].append(element)

    def unique_guids(self):
        # type: () -> set
        # IronPython 2.7: keys() returns list, not set-like view
        return set(list(self.arc.keys()) + list(self.mass.keys()))

    def duplicates_arc(self):
        # type: () -> dict[str, list[DB.Element]]
        # Avoid dict comprehension for maximum IronPython compatibility
        d = {}
        for g, els in self.arc.items():
            try:
                if len(els) > 1:
                    d[g] = els
            except Exception as e:
                Debug.WriteLine("GuidMultiMap.duplicates_arc error: {}".format(str(e)))
                pass
        return d

    def duplicates_mass(self):
        d = {}
        for g, els in self.mass.items():
            try:
                if len(els) > 1:
                    d[g] = els
            except Exception as e:
                Debug.WriteLine("GuidMultiMap.duplicates_mass error: {}".format(str(e)))
                pass
        return d

    def matched_pairs(self):
        # type: () -> (str, DB.Element, DB.Element)
        """Yield (guid, arc_element, mass_element) for Cartesian matches of same GUID.
        Supports one-to-many and many-to-one by pairing all combos for the GUID.
        """
        try:
            arc_keys = set(list(self.arc.keys()))
            mass_keys = set(list(self.mass.keys()))
            for g in sorted(list(arc_keys.intersection(mass_keys))):
                for a in self.arc.get(g, []) or []:
                    for m in self.mass.get(g, []) or []:
                        yield g, a, m
        except Exception as ex:
            Debug.WriteLine("GuidMultiMap.matched_pairs error: {}".format(str(ex)))
            return

    def missing_arc(self):
        try:
            arc_keys = set(list(self.arc.keys()))
            mass_keys = set(list(self.mass.keys()))
            diff = arc_keys.difference(mass_keys)
            return sorted(list(diff))
        except Exception as ex:
            Debug.WriteLine("GuidMultiMap.missing_arc error: {}".format(str(ex)))
            return []

    def extra_mass(self):
        try:
            arc_keys = set(list(self.arc.keys()))
            mass_keys = set(list(self.mass.keys()))
            diff = mass_keys.difference(arc_keys)
            return sorted(list(diff))
        except Exception as ex:
            Debug.WriteLine("GuidMultiMap.extra_mass error: {}".format(str(ex)))
            return []

