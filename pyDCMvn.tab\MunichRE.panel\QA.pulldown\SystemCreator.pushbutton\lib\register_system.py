# coding: utf-8
"""Register parameter object"""
from . import constants
from DCMvn.core.framework import List
from DCMvn.core import DB

class RegisterSystem:
    """Register parameter object"""
    def __init__(self, expando_object):
        """Create SharedParameter from ExpandoObject

        Args:
            expando_object (ExpandoObject): .Net ExpandoObject
        """
        # type : (object) -> SharedParameter
        self._object = expando_object

    @property
    def system_full_name(self):
        # type: () -> str
        """System full name"""
        return getattr(self._object, constants.SYSTEM_FULL_NAME)
    
    @property
    def system_type(self):
        """System type (service type / workset)"""
        return getattr(self._object, constants.SYSTEM_TYPE)
    
    @property
    def validate_system_type_contain_string(self):
        """Validate system type contain string"""
        return getattr(self._object, constants.VALIDATE_SYSTEM_TYPE_CONTAIN_STRING)
    
    @property
    def system_classification(self):
        """System classification"""
        system_classification = getattr(self._object, constants.SYSTEM_CLASSIFICATION)
        return constants.MEP_CLASSIFICATION_MAP[system_classification]
    
    @property
    def system_abbreviation(self):
        """System abbreviation"""
        return getattr(self._object, constants.SYSTEM_ABBREVIATION)
    
    @property
    def route_size(self):
        """Route size"""
        return getattr(self._object, constants.ROUTE_SIZE)
    
    @property
    def route_material(self):
        """Route material"""
        return getattr(self._object, constants.ROUTE_MATERIAL)
    
    @property
    def route_standard(self):
        """Route standard"""
        return getattr(self._object, constants.ROUTE_STANDARD)
    
    @property
    def route_fitting_connection(self):
        """Route fitting connection"""
        return getattr(self._object, constants.ROUTE_FITTING_CONNECTION)
    
    @property
    def route_type_name(self):
        """Route type name"""
        return getattr(self._object, constants.ROUTE_TYPE_NAME)
    
    @property
    def filter_name(self):
        """Filter name"""
        return getattr(self._object, constants.FILTER_NAME)
    
    @property
    def filter_color(self):
        """Filter color"""
        return getattr(self._object, constants.FILTER_COLOR)
    
    @property
    def model_name(self):
        """Model name"""
        return getattr(self._object, constants.MODEL_NAME)
    
    @property
    def route_categories(self):
        # type: () -> List[DB.BuiltInCategory]
        """Route categories"""
        return constants.ROUTE_CATEGORIES_MAP[self.model_name]

    
