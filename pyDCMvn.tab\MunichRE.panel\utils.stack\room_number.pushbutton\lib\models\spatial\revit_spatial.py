# coding: utf-8
from DCMvn.core import DB
from DCMvn.core.framework import Trace
from .base_spatial import BaseSpatial
from ..document_wrapper import DocumentWrapper


class RevitSpatial(BaseSpatial):
    """Wrapper for Autodesk.Revit.DB.SpatialElement elements.

    This class provides specialized functionality for Revit Spatial elements,
    extending the base spatial functionality with spatial-specific properties
    and methods.
    """

    def __init__(self, spatial_element, document_wrapper, spatial_geometry_calculator):
        # type: (DB.SpatialElement, DocumentWrapper, DB.SpatialElementGeometryCalculator) -> None
        """Initialize a Revit Spatial wrapper.

        Args:
            spatial_element (DB.SpatialElement): The Revit Spatial element
            document_wrapper (DocumentWrapper): The document wrapper
            spatial_geometry_calculator (DB.SpatialElementGeometryCalculator): The Revit Spatial geometry calculator
        """
        super(RevitSpatial, self).__init__(spatial_element, document_wrapper, spatial_geometry_calculator)

    @property
    def name(self):
        # type: () -> str
        """Get the spatial name.

        Returns:
            str: The spatial name
        """
        return self.element.get_Parameter(DB.BuiltInParameter.ROOM_NAME).AsString()

    @property
    def number(self):
        # type: () -> str
        """Get the spatial number.

        Returns:
            str: The spatial number
        """
        return self.element.get_Parameter(DB.BuiltInParameter.ROOM_NUMBER).AsString()

    @property
    def area(self):
        # type: () -> float
        """Get the spatial area in internal units (square feet).

        Returns:
            float: The spatial area in square feet
        """
        return self.element.get_Parameter(DB.BuiltInParameter.ROOM_AREA).AsDouble()

    @property
    def volume(self):
        # type: () -> float
        """Get the spatial volume in internal units (cubic feet).

        Returns:
            float: The spatial volume in cubic feet
        """
        return self.element.get_Parameter(DB.BuiltInParameter.ROOM_VOLUME).AsDouble()

    @property
    def is_placed(self):
        # type: () -> bool
        """Check if the spatial is placed (has area).

        Returns:
            bool: True if the spatial is placed, False otherwise
        """
        return self.area > 0

    @property
    def is_enclosed(self):
        # type: () -> bool
        """Check if the spatial is enclosed (has volume).

        Returns:
            bool: True if the spatial is enclosed, False otherwise
        """
        return self.volume > 0
    
    @property
    def geometry(self):
        # type: () -> DB.Solid | None
        """Get the geometry of the spatial element.
        
        Returns:
            DB.Solid | None: The geometry or None if not available
        """
        if not self.is_valid:
            return None
        if self._geometry is None and DB.SpatialElementGeometryCalculator.CanCalculateGeometry(self.element):
            try:
                geometry_result = self._spatial_geometry_calculator.CalculateSpatialElementGeometry(self.element)  # type: DB.SpatialElementGeometryResults
                if geometry_result:
                    calculate_result = geometry_result.GetGeometry()
                    self._geometry = calculate_result if calculate_result and calculate_result.Volume > 0 else super(RevitSpatial, self).geometry
            except:  # noqa
                self._geometry = super(RevitSpatial, self).geometry
        return self._geometry

    @property
    def is_valid(self):
        # type: () -> bool
        """Check if the spatial is valid.
        
        Returns:
            bool: True if the spatial is valid, False otherwise
        """
        return self.element.IsValidObject and self.is_placed and self.is_enclosed

    def is_point_inside(self, point):
        # type: (DB.XYZ) -> bool
        """Check if point is inside this spatial element using the correct API.
        
        Args:
            point (DB.XYZ): Point to check
            
        Returns:
            bool: True if point is inside the spatial element, False otherwise
        """
        try:
            if isinstance(self.element, DB.Architecture.Room):
                if self._document_wrapper.is_host:
                    return self.element.IsPointInRoom(point)
                else:
                    return self.element.IsPointInRoom(self._document_wrapper.transform.Inverse.OfPoint(point))
            elif isinstance(self.element, DB.Mechanical.Space):
                if self._document_wrapper.is_host:
                    return self.element.IsPointInSpace(point)
                else:
                    return self.element.IsPointInSpace(self._document_wrapper.transform.Inverse.OfPoint(point))
            else:
                return False      
        except:  # noqa
            import traceback
            Trace.TraceError("IsPointInside {0}".format(traceback.format_exc()))
            return False

    def __str__(self):
        # type: () -> str
        """String representation of the spatial.

        Returns:
            str: String representation
        """
        return "Revit Spatial (Id={}, Number={}, Name={})".format(
            self._get_elementid_value(self.id), self.number, self.name)