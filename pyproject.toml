[project]
name = "pyDCMvn-MunichRE"
version = "0.1.0"
description = "pyRevit extension for MunichRE's DCMvn project"
requires-python = ">=3.12"
dependencies = [
    "pandas==2.3.1",
    "openpyxl==3.1.5",
    "xlwings==0.33.15",
    "matplotlib==3.10.5",
    "seaborn==0.13.2",
    "requests==2.32.4",
    "jupyter==1.1.1",
    "nbstripout==0.8.1",
    "manifold3d>=3.2.1",
    "trimesh>=4.7.4",
    "pyglet>=2.1.6",
    "scipy>=1.16.1",
]

[tool.ruff]
ignore = ["F405", "F403", "E402"]

[dependency-groups]
dev = [
    "pre-commit>=4.3.0",
]
