---
description: Data validation and processing patterns for DCMvn applications with large datasets
alwaysApply: false
---
# DCMvn Data Validation and Processing

## Element Validation Pattern
Always validate Revit elements before processing:
```python
def validate_element_data(element):
    """Validate element before processing for Excel export."""
    try:
        # Check if element is valid
        if not element.IsValidObject:
            return False
            
        # Check required parameters exist
        guid_param = element.get_Parameter(DB.BuiltInParameter.IFC_GUID)
        if not guid_param or not guid_param.AsString():
            return False
            
        return True
    except Exception:
        return False

def get_safe_parameter_value(element, parameter_id, default_value=""):
    """Safely get parameter value with fallback."""
    try:
        param = element.get_Parameter(parameter_id)
        if param and param.HasValue:
            if param.StorageType == DB.StorageType.String:
                return param.AsString() or default_value
            elif param.StorageType == DB.StorageType.Double:
                return param.AsDouble()
            elif param.StorageType == DB.StorageType.Integer:
                return param.AsInteger()
        return default_value
    except Exception:
        return default_value
```

## Data Processing Validation
```python
def validate_and_process_spaces(spaces_data):
    """Validate space processing and count discrepancies."""
    print("=== SPACE PROCESSING VALIDATION ===")
    
    # Count unique GUIDs
    unique_guids = set()
    for space_dict in spaces_data:
        for guid in space_dict.keys():
            unique_guids.add(guid)
    
    processed_count = len(spaces_data)
    unique_count = len(unique_guids)
    
    print("Processed Data Objects: {}".format(processed_count))
    print("Unique GUIDs: {}".format(unique_count))
    
    # Check for duplicates
    if processed_count != unique_count:
        print("WARNING: Duplicate GUIDs detected")
    
    return {
        "processed_count": processed_count,
        "unique_count": unique_count,
        "has_duplicates": processed_count != unique_count
    }
```

## Collection Processing Pattern
```python
def process_collection_safely(elements, processor_func):
    """Process a collection of elements with error handling."""
    processed_data = []
    error_count = 0
    
    for element in elements:
        try:
            if validate_element_data(element):
                data = processor_func(element)
                if data:
                    processed_data.append(data)
            else:
                error_count += 1
        except Exception as e:
            from DCMvn.core.framework import Debug
            Debug.WriteLine("Error processing element {}: {}".format(
                element.Id.IntegerValue if element else "Unknown", str(e)
            ))
            error_count += 1
    
    print("Successfully processed: {}".format(len(processed_data)))
    if error_count > 0:
        print("Errors encountered: {}".format(error_count))
    
    return processed_data
```

## Warning and Issues Integration
```python
def enhance_data_with_warnings(space_data, warnings_dict):
    """Enhance space data with warning information."""
    for space_dict in space_data:
        for guid, data in space_dict.items():
            # Add warning information if available
            warnings = warnings_dict.get(guid, "")
            data["Issues"] = warnings
            
            # Enhance status based on warnings
            if warnings:
                if "Manual" in data.get("Comments", ""):
                    data["Status"] = "Manual Place or Edit"
                else:
                    data["Status"] = "Has Issues"
    
    return space_data
```

## Level-Based Analysis Pattern
```python
def analyze_by_level(data_dict, level_key="Level Reference"):
    """Analyze data grouped by levels."""
    level_groups = {}
    
    for guid, data in data_dict.items():
        level = data.get(level_key, "Unknown")
        if level not in level_groups:
            level_groups[level] = {}
        level_groups[level][guid] = data
    
    # Print level breakdown
    print("=== LEVEL BREAKDOWN ===")
    for level in sorted(level_groups.keys()):
        count = len(level_groups[level])
        print("Level {}: {} elements".format(level, count))
    
    return level_groups
```

## Data Comparison Utilities
```python
def compare_datasets(dataset1, dataset2, match_key="GUID"):
    """Compare two datasets and identify matches/mismatches."""
    matches = []
    mismatches = []
    dataset1_only = []
    dataset2_only = []
    
    # Convert to dictionaries for easier comparison
    dict1 = {item[match_key]: item for item in dataset1}
    dict2 = {item[match_key]: item for item in dataset2}
    
    # Find matches and mismatches
    for key, item1 in dict1.items():
        if key in dict2:
            item2 = dict2[key]
            if items_match(item1, item2):
                matches.append(key)
            else:
                mismatches.append((key, item1, item2))
        else:
            dataset1_only.append(item1)
    
    # Find items only in dataset2
    for key, item2 in dict2.items():
        if key not in dict1:
            dataset2_only.append(item2)
    
    return {
        "matches": matches,
        "mismatches": mismatches,
        "dataset1_only": dataset1_only,
        "dataset2_only": dataset2_only
    }

def items_match(item1, item2, compare_keys=None):
    """Check if two items match on specified keys."""
    if compare_keys is None:
        compare_keys = ["Name", "Number", "Area", "Level"]
    
    for key in compare_keys:
        if item1.get(key) != item2.get(key):
            return False
    return True
```

## Progress Reporting Pattern
```python
def create_progress_reporter(total_items, report_interval=100):
    """Create a progress reporter for long operations."""
    processed = 0
    
    def report_progress(message="Processing"):
        nonlocal processed
        processed += 1
        if processed % report_interval == 0 or processed == total_items:
            percentage = (processed / float(total_items)) * 100
            print("{}: {}/{} ({:.1f}%)".format(
                message, processed, total_items, percentage
            ))
    
    return report_progress
```

## Debugging and Logging Utilities
```python
def debug_data_structure(data, name="Data"):
    """Debug data structure for troubleshooting."""
    print("=== {} DEBUG ===".format(name.upper()))
    
    if isinstance(data, list):
        print("Type: List, Count: {}".format(len(data)))
        if data:
            print("First item type: {}".format(type(data[0]).__name__))
    elif isinstance(data, dict):
        print("Type: Dictionary, Keys: {}".format(len(data)))
        if data:
            first_key = next(iter(data))
            print("First key: {}, Value type: {}".format(
                first_key, type(data[first_key]).__name__
            ))
    else:
        print("Type: {}, Value: {}".format(type(data).__name__, str(data)[:100]))
    
    print("=" * 30)
```

## Excel Data Access Utilities
```python
def safe_excel_get_value(excel_row, column_name, default_value=None):
    """Safely get value from Excel row using DCMvn patterns."""
    try:
        if hasattr(excel_row, column_name):
            value = getattr(excel_row, column_name)
            return value if value is not None else default_value
        return default_value
    except Exception:
        return default_value

def validate_excel_data_structure(excel_data, required_columns):
    """Validate Excel data has required columns."""
    if not excel_data:
        return False, "No data found"
    
    try:
        # Get first row to check columns
        first_row = next(iter(excel_data), None)
        if not first_row:
            return False, "Empty dataset"
        
        missing_columns = []
        for column in required_columns:
            if not hasattr(first_row, column):
                missing_columns.append(column)
        
        if missing_columns:
            return False, "Missing columns: {}".format(", ".join(missing_columns))
        
        return True, "Valid structure"
    except Exception as e:
        return False, "Validation error: {}".format(str(e))

def process_excel_data_safely(excel_data, processor_func):
    """Process Excel data with comprehensive error handling."""
    processed_data = []
    error_count = 0
    
    if not excel_data:
        return processed_data, error_count
    
    try:
        for row in excel_data:
            try:
                result = processor_func(row)
                if result:
                    processed_data.append(result)
            except Exception as e:
                error_count += 1
                from DCMvn.core.framework import Debug
                Debug.WriteLine("Error processing Excel row: {}".format(str(e)))
    except Exception as e:
        from DCMvn.core.framework import Debug
        Debug.WriteLine("Error iterating Excel data: {}".format(str(e)))
        error_count += 1
    
    print("Excel data processed: {} items, {} errors".format(
        len(processed_data), error_count
    ))
    return processed_data, error_count
```

## Best Practices
1. **Always validate inputs** before processing
2. **Use safe parameter access** with fallbacks
3. **Count and verify** processed data
4. **Log processing statistics** for debugging
5. **Handle missing or invalid data** gracefully
6. **Provide progress feedback** for long operations
7. **Group data by meaningful categories** (level, type, etc.)
8. **Compare datasets systematically** for analysis
9. **Use DCMvn Excel patterns** instead of project-specific readers
10. **Validate Excel structure** before processing data