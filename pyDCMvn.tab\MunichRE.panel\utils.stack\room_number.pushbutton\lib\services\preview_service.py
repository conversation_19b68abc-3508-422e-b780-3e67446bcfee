# coding: utf-8
import clr
import math
from DCMvn.core.framework import Debug

# WPF drawing types
clr.AddReference("PresentationCore")
clr.AddReference("PresentationFramework")
from System.Windows import Point
from System.Windows.Media import SolidColorBrush, Color
from System.Windows.Shapes import <PERSON><PERSON><PERSON>, <PERSON>, Polygon
from System.Windows.Controls import Canvas, TextBlock


class PreviewService(object):
    """Provides accurate detection algorithm previews with proper 3D perspective."""

    def __init__(self):
        self.__color_revit_spatial = SolidColorBrush(Color.FromRgb(40, 167, 69)) # green
        self.__color_revit_spatial_light = SolidColorBrush(Color.FromRgb(200, 230, 201)) # light green
        self.__color_generic_spatial = SolidColorBrush(Color.FromRgb(233, 30, 99)) # pink
        self.__color_generic_spatial_light = SolidColorBrush(Color.FromRgb(248, 187, 208)) # light pink
        self.__color_mep_original = SolidColorBrush(Color.FromRgb(33, 150, 243)) # blue
        self.__color_mep_translated = SolidColorBrush(Color.FromRgb(255, 152, 0)) # orange
        self.__color_proximity_zone = SolidColorBrush(Color.FromRgb(255, 193, 7)) # amber
        self.__color_ray = SolidColorBrush(Color.FromRgb(244, 67, 54)) # red
        self.__color_text = SolidColorBrush(Color.FromRgb(100, 100, 100)) # gray
        
    def _clear_canvas(self, canvas):
        try:
            canvas.Children.Clear()
        except Exception as ex:
            Debug.WriteLine("Preview clear error: {}".format(str(ex)))

    def _canvas_size(self, canvas):
        try:
            w = canvas.ActualWidth
            h = canvas.ActualHeight
            if not w or w <= 0:
                w = canvas.Width if canvas.Width and canvas.Width > 0 else 240
            if not h or h <= 0:
                h = canvas.Height if canvas.Height and canvas.Height > 0 else 140
            return float(w), float(h)
        except Exception:
            return 240.0, 140.0

    # --------------------------------------------------------------------------------------
    # 3D drawing helpers (isometric perspective)
    # --------------------------------------------------------------------------------------
    @staticmethod
    def _iso_transform(x, y, z):
        """Transform 3D coordinates to isometric 2D projection."""
        # Isometric projection angles: 30° for x-axis, -30° for z-axis
        iso_x = x * math.cos(math.radians(30)) - z * math.cos(math.radians(30))
        iso_y = y + x * math.sin(math.radians(30)) + z * math.sin(math.radians(30))
        return iso_x, iso_y

    @staticmethod
    def _draw_iso_face(canvas, corners_2d, fill_brush, stroke_brush, opacity):
        # type: (Canvas, list[Point], SolidColorBrush | None, SolidColorBrush, float) -> None
        """Draw a single face of the isometric box."""
        poly = Polygon()
        for x, y in corners_2d:
            poly.Points.Add(Point(x, y))

        if fill_brush:
            poly.Fill = fill_brush
            poly.Fill.Opacity = opacity
        poly.Stroke = stroke_brush
        poly.StrokeThickness = 1
        canvas.Children.Add(poly)

    def _draw_iso_box(self, canvas, x, y, z, w, h, d, fill_brush, stroke_brush):
        """Draw an isometric box with proper 3D perspective."""
        # Calculate 8 corners of the box
        corners_3d = [
            (x, y, z),         # front bottom left
            (x + w, y, z),     # front bottom right
            (x + w, y + h, z), # front top right
            (x, y + h, z),     # front top left
            (x, y, z + d),     # back bottom left
            (x + w, y, z + d), # back bottom right
            (x + w, y + h, z + d), # back top right
            (x, y + h, z + d), # back top left
        ]
        
        # Transform to 2D isometric
        corners_2d = [self._iso_transform(cx, cy, cz) for cx, cy, cz in corners_3d]
        
        # Draw back faces first (hidden line removal)
        self._draw_iso_face(canvas, [corners_2d[4], corners_2d[5], corners_2d[6], corners_2d[7]], None, stroke_brush, 0.5)  # back face
        self._draw_iso_face(canvas, [corners_2d[1], corners_2d[5], corners_2d[6], corners_2d[2]], None, stroke_brush, 0.5)  # right face
        self._draw_iso_face(canvas, [corners_2d[3], corners_2d[7], corners_2d[6], corners_2d[2]], None, stroke_brush, 0.5)  # top face
        
        # Draw front faces
        self._draw_iso_face(canvas, [corners_2d[0], corners_2d[1], corners_2d[2], corners_2d[3]], fill_brush, stroke_brush, 1.0)  # front face
        self._draw_iso_face(canvas, [corners_2d[0], corners_2d[4], corners_2d[7], corners_2d[3]], fill_brush, stroke_brush, 0.8)  # left face
        self._draw_iso_face(canvas, [corners_2d[0], corners_2d[1], corners_2d[5], corners_2d[4]], fill_brush, stroke_brush, 0.6)  # bottom face


    def _draw_iso_ellipsoid(self, canvas, cx, cy, cz, rx, ry, rz, fill_brush, stroke_brush):
        """Draw an isometric ellipsoid (simplified as multiple ellipses)."""
        # Front ellipse
        front_x, front_y = self._iso_transform(cx, cy, cz)
        front = Ellipse()
        front.Width = rx * 2
        front.Height = ry * 2
        front.Fill = fill_brush
        front.Stroke = stroke_brush
        front.StrokeThickness = 1
        canvas.Children.Add(front)
        Canvas.SetLeft(front, front_x - rx)
        Canvas.SetTop(front, front_y - ry)
        
        # Back ellipse (slightly offset)
        back_x, back_y = self._iso_transform(cx, cy, cz + rz)
        back = Ellipse()
        back.Width = rx * 2
        back.Height = ry * 2
        back.Fill = None
        back.Stroke = stroke_brush
        back.StrokeThickness = 1
        back.Opacity = 0.5
        canvas.Children.Add(back)
        Canvas.SetLeft(back, back_x - rx)
        Canvas.SetTop(back, back_y - ry)

    def _add_label(self, canvas, text, x, y, color=None):
        """Add a text label to the canvas."""
        tb = TextBlock()
        tb.Text = text
        tb.FontSize = 9
        tb.Foreground = color or self.__color_text
        canvas.Children.Add(tb)
        Canvas.SetLeft(tb, x)
        Canvas.SetTop(tb, y)

    @staticmethod
    def _draw_arrow(canvas, x1, y1, x2, y2, color, thickness=2):
        """Draw an arrow line."""
        # Main line
        line = Line()
        line.X1 = x1
        line.Y1 = y1
        line.X2 = x2
        line.Y2 = y2
        line.Stroke = color
        line.StrokeThickness = thickness
        canvas.Children.Add(line)
        
        # Arrow head
        angle = math.atan2(y2 - y1, x2 - x1)
        head_len = 6
        head_angle = math.radians(20)
        
        head1_x = x2 - head_len * math.cos(angle - head_angle)
        head1_y = y2 - head_len * math.sin(angle - head_angle)
        head2_x = x2 - head_len * math.cos(angle + head_angle)
        head2_y = y2 - head_len * math.sin(angle + head_angle)
        
        head = Polygon()
        head.Points.Add(Point(x2, y2))
        head.Points.Add(Point(head1_x, head1_y))
        head.Points.Add(Point(head2_x, head2_y))
        head.Fill = color
        canvas.Children.Add(head)

    # --------------------------------------------------------------------------------------
    # Specialized drawing methods for each detection scenario
    # --------------------------------------------------------------------------------------
    
    def draw_above_point_revit(self, canvas, above_mm=1000.0):
        """Above check for point-based elements with Revit Spatial."""
        try:
            self._clear_canvas(canvas)
            w, h = self._canvas_size(canvas)
            
            # Scale for canvas
            scale = min(w, h) / 200.0
            offset_x, offset_y = w * 0.1, h * 0.1
            
            # Draw Revit spatial (3D box) in isometric view
            spatial_x, spatial_y, spatial_z = 80 * scale, 60 * scale, 0
            spatial_w, spatial_h, spatial_d = 60 * scale, 40 * scale, 30 * scale
            
            # Transform to canvas coordinates
            iso_x, iso_y = self._iso_transform(spatial_x, spatial_y, spatial_z)
            self._draw_iso_box(canvas, offset_x + iso_x, offset_y + iso_y, spatial_z, 
                             spatial_w, spatial_h, spatial_d, 
                             self.__color_revit_spatial_light, self.__color_revit_spatial)
            
            # Original MEP point (blue)
            orig_x, orig_y, orig_z = 40 * scale, 80 * scale, 50 * scale
            orig_2d_x, orig_2d_y = self._iso_transform(orig_x, orig_y, orig_z)
            orig_pt = Ellipse()
            orig_pt.Width = 6
            orig_pt.Height = 6
            orig_pt.Fill = self.__color_mep_original
            canvas.Children.Add(orig_pt)
            Canvas.SetLeft(orig_pt, offset_x + orig_2d_x - 3)
            Canvas.SetTop(orig_pt, offset_y + orig_2d_y - 3)
            
            # Translated point (orange) - moved down by above_distance
            trans_z = orig_z - (above_mm / 1000.0) * 20 * scale  # scale the translation
            trans_2d_x, trans_2d_y = self._iso_transform(orig_x, orig_y, trans_z)
            trans_pt = Ellipse()
            trans_pt.Width = 6
            trans_pt.Height = 6
            trans_pt.Fill = self.__color_mep_translated
            canvas.Children.Add(trans_pt)
            Canvas.SetLeft(trans_pt, offset_x + trans_2d_x - 3)
            Canvas.SetTop(trans_pt, offset_y + trans_2d_y - 3)
            
            # Translation arrow
            self._draw_arrow(canvas, 
                           offset_x + orig_2d_x, offset_y + orig_2d_y,
                           offset_x + trans_2d_x, offset_y + trans_2d_y,
                           self.__color_mep_translated)
            
            # Labels
            self._add_label(canvas, "Original", offset_x + orig_2d_x + 8, offset_y + orig_2d_y - 5)
            self._add_label(canvas, "Translated -Z", offset_x + trans_2d_x + 8, offset_y + trans_2d_y - 5)
            self._add_label(canvas, "Revit Spatial", offset_x + iso_x + spatial_w/2, offset_y + iso_y - 10)
            
        except Exception as ex:
            Debug.WriteLine("Above point Revit preview error: {}".format(str(ex)))

    def draw_above_curve_revit(self, canvas, above_mm=1000.0):
        """Above check for curve-based elements with Revit Spatial."""
        try:
            self._clear_canvas(canvas)
            w, h = self._canvas_size(canvas)
            
            scale = min(w, h) / 200.0
            offset_x, offset_y = w * 0.1, h * 0.1
            
            # Draw Revit spatial (3D box)
            spatial_x, spatial_y, spatial_z = 60 * scale, 50 * scale, 0
            spatial_w, spatial_h, spatial_d = 80 * scale, 30 * scale, 25 * scale
            
            iso_x, iso_y = self._iso_transform(spatial_x, spatial_y, spatial_z)
            self._draw_iso_box(canvas, offset_x + iso_x, offset_y + iso_y, spatial_z,
                             spatial_w, spatial_h, spatial_d,
                             self.__color_revit_spatial_light, self.__color_revit_spatial)
            
            # Original MEP curve (blue line)
            curve_start_3d = (20 * scale, 70 * scale, 40 * scale)
            curve_end_3d = (120 * scale, 70 * scale, 40 * scale)
            
            start_2d = self._iso_transform(*curve_start_3d)
            end_2d = self._iso_transform(*curve_end_3d)
            
            orig_line = Line()
            orig_line.X1 = offset_x + start_2d[0]
            orig_line.Y1 = offset_y + start_2d[1]
            orig_line.X2 = offset_x + end_2d[0]
            orig_line.Y2 = offset_y + end_2d[1]
            orig_line.Stroke = self.__color_mep_original
            orig_line.StrokeThickness = 3
            canvas.Children.Add(orig_line)
            
            # Translated curve sample points (orange) - start, mid, end
            trans_z = curve_start_3d[2] - (above_mm / 1000.0) * 20 * scale
            sample_points_3d = [
                (curve_start_3d[0], curve_start_3d[1], trans_z),
                ((curve_start_3d[0] + curve_end_3d[0]) / 2, curve_start_3d[1], trans_z),
                (curve_end_3d[0], curve_start_3d[1], trans_z)
            ]
            
            for i, point_3d in enumerate(sample_points_3d):
                point_2d = self._iso_transform(*point_3d)
                pt = Ellipse()
                pt.Width = 5
                pt.Height = 5
                pt.Fill = self.__color_mep_translated
                canvas.Children.Add(pt)
                Canvas.SetLeft(pt, offset_x + point_2d[0] - 2.5)
                Canvas.SetTop(pt, offset_y + point_2d[1] - 2.5)
                
                # Translation arrows
                orig_point_3d = (point_3d[0], point_3d[1], curve_start_3d[2])
                orig_point_2d = self._iso_transform(*orig_point_3d)
                self._draw_arrow(canvas,
                               offset_x + orig_point_2d[0], offset_y + orig_point_2d[1],
                               offset_x + point_2d[0], offset_y + point_2d[1],
                               self.__color_mep_translated, 1)
            
            # Labels
            self._add_label(canvas, "Original Curve", offset_x + start_2d[0], offset_y + start_2d[1] - 15)
            self._add_label(canvas, "Sample Points -Z", offset_x + sample_points_3d[1][0], offset_y + self._iso_transform(*sample_points_3d[1])[1] + 10)
            
        except Exception as ex:
            Debug.WriteLine("Above curve Revit preview error: {}".format(str(ex)))

    def draw_proximity_curve_revit(self, canvas, proximity_mm=1000.0):
        """Proximity check for curve-based elements with Revit Spatial using rectangle intersection."""
        try:
            self._clear_canvas(canvas)
            w, h = self._canvas_size(canvas)
            
            scale = min(w, h) / 200.0
            offset_x, offset_y = w * 0.1, h * 0.1
            
            # Draw Revit spatial (3D box)
            spatial_x, spatial_y, spatial_z = 80 * scale, 40 * scale, 0
            spatial_w, spatial_h, spatial_d = 60 * scale, 50 * scale, 30 * scale
            
            iso_x, iso_y = self._iso_transform(spatial_x, spatial_y, spatial_z)
            self._draw_iso_box(canvas, offset_x + iso_x, offset_y + iso_y, spatial_z,
                             spatial_w, spatial_h, spatial_d,
                             self.__color_revit_spatial_light, self.__color_revit_spatial)
            
            # MEP curve - parallel to XY plane, within spatial height but not intersecting
            # Position curve at same Z level as spatial but slightly outside its bounds
            curve_z = spatial_z + spatial_d * 0.7  # Within spatial height
            curve_start_3d = (spatial_x - 20 * scale, spatial_y + spatial_h * 0.5, curve_z)
            curve_end_3d = (spatial_x + spatial_w + 20 * scale, spatial_y + spatial_h * 0.5, curve_z)
            
            start_2d = self._iso_transform(*curve_start_3d)
            end_2d = self._iso_transform(*curve_end_3d)
            
            curve_line = Line()
            curve_line.X1 = offset_x + start_2d[0]
            curve_line.Y1 = offset_y + start_2d[1]
            curve_line.X2 = offset_x + end_2d[0]
            curve_line.Y2 = offset_y + end_2d[1]
            curve_line.Stroke = self.__color_mep_original
            curve_line.StrokeThickness = 3
            canvas.Children.Add(curve_line)
            
            # Proximity rectangle around curve - rotated 90 degrees around X-axis (extending in Z direction)
            prox_band = (proximity_mm / 1000.0) * 15 * scale
            
            # Rectangle corners in 3D - perpendicular to curve direction (X), extending in Z direction
            rect_corners_3d = [
                (curve_start_3d[0], curve_start_3d[1], curve_z - prox_band),  # bottom left
                (curve_end_3d[0], curve_end_3d[1], curve_z - prox_band),      # bottom right
                (curve_end_3d[0], curve_end_3d[1], curve_z + prox_band),      # top right
                (curve_start_3d[0], curve_start_3d[1], curve_z + prox_band)   # top left
            ]
            
            # Draw proximity rectangle
            rect_poly = Polygon()
            for corner_3d in rect_corners_3d:
                corner_2d = self._iso_transform(*corner_3d)
                rect_poly.Points.Add(Point(offset_x + corner_2d[0], offset_y + corner_2d[1]))
            
            rect_poly.Fill = self.__color_proximity_zone
            rect_poly.Fill.Opacity = 0.3
            rect_poly.Stroke = self.__color_proximity_zone
            rect_poly.StrokeThickness = 1
            canvas.Children.Add(rect_poly)
            
            # Labels
            self._add_label(canvas, "MEP Curve", offset_x + start_2d[0], offset_y + start_2d[1] - 15)
            self._add_label(canvas, "Proximity Rectangle", offset_x + start_2d[0], offset_y + start_2d[1] + 25)
            
        except Exception as ex:
            Debug.WriteLine("Proximity curve Revit preview error: {}".format(str(ex)))

    def draw_proximity_point_revit(self, canvas, proximity_mm=1000.0):
        """Proximity check for point-based elements with Revit Spatial using circle intersection."""
        try:
            self._clear_canvas(canvas)
            w, h = self._canvas_size(canvas)
            
            scale = min(w, h) / 200.0
            offset_x, offset_y = w * 0.1, h * 0.1
            
            # Draw Revit spatial (3D box)
            spatial_x, spatial_y, spatial_z = 80 * scale, 50 * scale, 0
            spatial_w, spatial_h, spatial_d = 50 * scale, 40 * scale, 25 * scale
            
            iso_x, iso_y = self._iso_transform(spatial_x, spatial_y, spatial_z)
            self._draw_iso_box(canvas, offset_x + iso_x, offset_y + iso_y, spatial_z,
                             spatial_w, spatial_h, spatial_d,
                             self.__color_revit_spatial_light, self.__color_revit_spatial)
            
            # MEP point
            point_3d = (50 * scale, 80 * scale, 15 * scale)
            point_2d = self._iso_transform(*point_3d)
            
            mep_pt = Ellipse()
            mep_pt.Width = 6
            mep_pt.Height = 6
            mep_pt.Fill = self.__color_mep_original
            canvas.Children.Add(mep_pt)
            Canvas.SetLeft(mep_pt, offset_x + point_2d[0] - 3)
            Canvas.SetTop(mep_pt, offset_y + point_2d[1] - 3)
            
            # Proximity circle (simplified 2D representation)
            prox_radius = (proximity_mm / 1000.0) * 20 * scale
            
            prox_circle = Ellipse()
            prox_circle.Width = prox_radius * 2
            prox_circle.Height = prox_radius * 2
            prox_circle.Fill = self.__color_proximity_zone
            prox_circle.Fill.Opacity = 0.3
            prox_circle.Stroke = self.__color_proximity_zone
            prox_circle.StrokeThickness = 1
            canvas.Children.Add(prox_circle)
            Canvas.SetLeft(prox_circle, offset_x + point_2d[0] - prox_radius)
            Canvas.SetTop(prox_circle, offset_y + point_2d[1] - prox_radius)
            
            # Labels
            self._add_label(canvas, "MEP Point", offset_x + point_2d[0] + 8, offset_y + point_2d[1] - 5)
            self._add_label(canvas, "Proximity Circle", offset_x + point_2d[0] - prox_radius, offset_y + point_2d[1] + prox_radius + 5)
            
        except Exception as ex:
            Debug.WriteLine("Proximity point Revit preview error: {}".format(str(ex)))

    def draw_proximity_curve_generic(self, canvas, proximity_mm=1000.0):
        """Proximity check for curve-based elements with Generic Spatial using ray casting."""
        try:
            self._clear_canvas(canvas)
            w, h = self._canvas_size(canvas)
            
            scale = min(w, h) / 200.0
            offset_x, offset_y = w * 0.1, h * 0.1
            
            # Draw Generic spatial (3D ellipsoid)
            spatial_cx, spatial_cy, spatial_cz = 120 * scale, 60 * scale, 15 * scale
            spatial_rx, spatial_ry, spatial_rz = 35 * scale, 25 * scale, 20 * scale
            
            self._draw_iso_ellipsoid(canvas, spatial_cx, spatial_cy, spatial_cz,
                                   spatial_rx, spatial_ry, spatial_rz,
                                   self.__color_generic_spatial_light, self.__color_generic_spatial)
            
            # Spatial center point
            center_2d = self._iso_transform(spatial_cx, spatial_cy, spatial_cz)
            center_pt = Ellipse()
            center_pt.Width = 4
            center_pt.Height = 4
            center_pt.Fill = self.__color_generic_spatial
            canvas.Children.Add(center_pt)
            Canvas.SetLeft(center_pt, offset_x + center_2d[0] - 2)
            Canvas.SetTop(center_pt, offset_y + center_2d[1] - 2)
            
            # MEP curve with sample points
            curve_start_3d = (30 * scale, 80 * scale, 15 * scale)
            curve_end_3d = (90 * scale, 80 * scale, 15 * scale)
            
            start_2d = self._iso_transform(*curve_start_3d)
            end_2d = self._iso_transform(*curve_end_3d)
            
            curve_line = Line()
            curve_line.X1 = offset_x + start_2d[0]
            curve_line.Y1 = offset_y + start_2d[1]
            curve_line.X2 = offset_x + end_2d[0]
            curve_line.Y2 = offset_y + end_2d[1]
            curve_line.Stroke = self.__color_mep_original
            curve_line.StrokeThickness = 3
            canvas.Children.Add(curve_line)
            
            # Sample points on curve (start, mid, end)
            sample_points_3d = [
                curve_start_3d,
                ((curve_start_3d[0] + curve_end_3d[0]) / 2, curve_start_3d[1], curve_start_3d[2]),
                curve_end_3d
            ]
            
            for point_3d in sample_points_3d:
                point_2d = self._iso_transform(*point_3d)
                
                # Sample point
                pt = Ellipse()
                pt.Width = 4
                pt.Height = 4
                pt.Fill = self.__color_mep_original
                canvas.Children.Add(pt)
                Canvas.SetLeft(pt, offset_x + point_2d[0] - 2)
                Canvas.SetTop(pt, offset_y + point_2d[1] - 2)
                
                # Ray to spatial center
                self._draw_arrow(canvas,
                               offset_x + point_2d[0], offset_y + point_2d[1],
                               offset_x + center_2d[0], offset_y + center_2d[1],
                               self.__color_ray, 1)
            
            # Labels
            self._add_label(canvas, "MEP Curve", offset_x + start_2d[0], offset_y + start_2d[1] - 15)
            self._add_label(canvas, "Sample Points", offset_x + start_2d[0], offset_y + start_2d[1] + 15)
            self._add_label(canvas, "Generic Spatial", offset_x + center_2d[0] + 20, offset_y + center_2d[1] - 10)
            self._add_label(canvas, "Ray Cast", offset_x + (start_2d[0] + center_2d[0]) / 2, offset_y + (start_2d[1] + center_2d[1]) / 2 - 10)
            
        except Exception as ex:
            Debug.WriteLine("Proximity curve Generic preview error: {}".format(str(ex)))

    def draw_proximity_point_generic(self, canvas, proximity_mm=1000.0):
        """Proximity check for point-based elements with Generic Spatial using ray casting."""
        try:
            self._clear_canvas(canvas)
            w, h = self._canvas_size(canvas)
            
            scale = min(w, h) / 200.0
            offset_x, offset_y = w * 0.1, h * 0.1
            
            # Draw Generic spatial (3D ellipsoid)
            spatial_cx, spatial_cy, spatial_cz = 120 * scale, 50 * scale, 15 * scale
            spatial_rx, spatial_ry, spatial_rz = 40 * scale, 30 * scale, 25 * scale
            
            self._draw_iso_ellipsoid(canvas, spatial_cx, spatial_cy, spatial_cz,
                                   spatial_rx, spatial_ry, spatial_rz,
                                   self.__color_generic_spatial_light, self.__color_generic_spatial)
            
            # Spatial center point
            center_2d = self._iso_transform(spatial_cx, spatial_cy, spatial_cz)
            center_pt = Ellipse()
            center_pt.Width = 4
            center_pt.Height = 4
            center_pt.Fill = self.__color_generic_spatial
            canvas.Children.Add(center_pt)
            Canvas.SetLeft(center_pt, offset_x + center_2d[0] - 2)
            Canvas.SetTop(center_pt, offset_y + center_2d[1] - 2)
            
            # MEP point
            point_3d = (40 * scale, 90 * scale, 15 * scale)
            point_2d = self._iso_transform(*point_3d)
            
            mep_pt = Ellipse()
            mep_pt.Width = 6
            mep_pt.Height = 6
            mep_pt.Fill = self.__color_mep_original
            canvas.Children.Add(mep_pt)
            Canvas.SetLeft(mep_pt, offset_x + point_2d[0] - 3)
            Canvas.SetTop(mep_pt, offset_y + point_2d[1] - 3)
            
            # Ray to spatial center
            self._draw_arrow(canvas,
                           offset_x + point_2d[0], offset_y + point_2d[1],
                           offset_x + center_2d[0], offset_y + center_2d[1],
                           self.__color_ray, 2)
            
            # Distance check visualization (dashed circle around spatial center)
            max_distance = (proximity_mm / 1000.0) * 30 * scale
            distance_circle = Ellipse()
            distance_circle.Width = max_distance * 2
            distance_circle.Height = max_distance * 2
            distance_circle.Fill = None
            distance_circle.Stroke = self.__color_ray
            distance_circle.StrokeThickness = 1
            distance_circle.Opacity = 0.5
            canvas.Children.Add(distance_circle)
            Canvas.SetLeft(distance_circle, offset_x + center_2d[0] - max_distance)
            Canvas.SetTop(distance_circle, offset_y + center_2d[1] - max_distance)
            
            # Labels
            self._add_label(canvas, "MEP Point", offset_x + point_2d[0] + 8, offset_y + point_2d[1] - 5)
            self._add_label(canvas, "Generic Spatial", offset_x + center_2d[0] + 20, offset_y + center_2d[1] - 10)
            self._add_label(canvas, "Ray Cast", offset_x + (point_2d[0] + center_2d[0]) / 2, offset_y + (point_2d[1] + center_2d[1]) / 2 - 10)
            self._add_label(canvas, "Max Distance", offset_x + center_2d[0] - max_distance, offset_y + center_2d[1] - max_distance - 10)
            
        except Exception as ex:
            Debug.WriteLine("Proximity point Generic preview error: {}".format(str(ex)))