# -*- coding: utf-8 -*-
import clr
from pyrevit import HOST_APP, DB, routes

# Simple MEP Routes for pyDCMvn.MunichRE Extension
# Basic get data and set parameter operations
api = routes.API('pyDCMvn.MunichRE')


@api.route("/api/mep/ducts", methods=["GET"])
def get_ducts():
    """Get all duct elements in the active document."""
    try:
        doc = HOST_APP.doc
        if not doc:
            return routes.make_response({"error": "No active document"}), 400

        # Collect ducts
        collector = DB.FilteredElementCollector(doc).OfCategory(DB.BuiltInCategory.OST_DuctCurves).WhereElementIsNotElementType()
        
        ducts_data = []
        for duct in collector:
            duct_data = {
                "id": duct.Id.IntegerValue,
                "name": duct.Name if duct.Name else "Unnamed",
                "category": "Ducts",
                "level_id": duct.LevelId.IntegerValue if duct.LevelId else None,
                "system_name": get_parameter_value(duct, DB.BuiltInParameter.RBS_SYSTEM_NAME_PARAM),
                "diameter": get_parameter_value(duct, DB.BuiltInParameter.RBS_CURVE_DIAMETER_PARAM),
                "length": get_parameter_value(duct, DB.BuiltInParameter.CURVE_ELEM_LENGTH)
            }
            ducts_data.append(duct_data)
            
        return routes.make_response({
            "success": True,
            "count": len(ducts_data),
            "ducts": ducts_data
        })
        
    except Exception as e:
        return routes.make_response({"error": str(e)}), 500


@api.route("/api/mep/pipes", methods=["GET"])
def get_pipes():
    """Get all pipe elements in the active document."""
    try:
        doc = HOST_APP.doc
        if not doc:
            return routes.make_response({"error": "No active document"}), 400
            
        # Collect pipes
        collector = DB.FilteredElementCollector(doc).OfCategory(DB.BuiltInCategory.OST_PipeCurves).WhereElementIsNotElementType()
        
        pipes_data = []
        for pipe in collector:
            pipe_data = {
                "id": pipe.Id.IntegerValue,
                "name": pipe.Name if pipe.Name else "Unnamed",
                "category": "Pipes",
                "level_id": pipe.LevelId.IntegerValue if pipe.LevelId else None,
                "system_name": get_parameter_value(pipe, DB.BuiltInParameter.RBS_SYSTEM_NAME_PARAM),
                "diameter": get_parameter_value(pipe, DB.BuiltInParameter.RBS_PIPE_DIAMETER_PARAM),
                "length": get_parameter_value(pipe, DB.BuiltInParameter.CURVE_ELEM_LENGTH)
            }
            pipes_data.append(pipe_data)

        return routes.make_response({
            "success": True,
            "count": len(pipes_data),
            "pipes": pipes_data
        })
        
    except Exception as e:
        return routes.make_response({"error": str(e)}), 500


@api.route("/api/mep/cable-trays", methods=["GET"])
def get_cable_trays():
    """Get all cable tray elements in the active document."""
    try:
        doc = HOST_APP.doc
        if not doc:
            return routes.make_response({"error": "No active document"}), 400
            
        # Collect cable trays
        collector = DB.FilteredElementCollector(doc).OfCategory(DB.BuiltInCategory.OST_CableTray).WhereElementIsNotElementType()
        
        cable_trays_data = []
        for tray in collector:
            tray_data = {
                "id": tray.Id.IntegerValue,
                "name": tray.Name if tray.Name else "Unnamed",
                "category": "Cable Trays",
                "level_id": tray.LevelId.IntegerValue if tray.LevelId else None,
                "width": get_parameter_value(tray, DB.BuiltInParameter.RBS_CABLETRAY_WIDTH_PARAM),
                "height": get_parameter_value(tray, DB.BuiltInParameter.RBS_CABLETRAY_HEIGHT_PARAM),
                "length": get_parameter_value(tray, DB.BuiltInParameter.CURVE_ELEM_LENGTH)
            }
            cable_trays_data.append(tray_data)

        return routes.make_response({
            "success": True,
            "count": len(cable_trays_data),
            "cable_trays": cable_trays_data
        })
        
    except Exception as e:
        return routes.make_response({"error": str(e)}), 500


@api.route("/api/mep/elements", methods=["GET"])
def get_all_mep_elements():
    """Get all MEP elements (ducts, pipes, cable trays) in the active document."""
    try:
        doc = HOST_APP.doc
        if not doc:
            return routes.make_response({"error": "No active document"}), 400
            
        # Collect all MEP elements
        mep_categories = [
            DB.BuiltInCategory.OST_DuctCurves,
            DB.BuiltInCategory.OST_PipeCurves,
            DB.BuiltInCategory.OST_CableTray
        ]
        
        all_elements = []
        
        for category in mep_categories:
            collector = DB.FilteredElementCollector(doc).OfCategory(category).WhereElementIsNotElementType()
            
            for element in collector:
                element_data = {
                    "id": element.Id.IntegerValue,
                    "name": element.Name if element.Name else "Unnamed",
                    "category": element.Category.Name if element.Category else "Unknown",
                    "level_id": element.LevelId.IntegerValue if element.LevelId else None,
                    "system_name": get_parameter_value(element, DB.BuiltInParameter.RBS_SYSTEM_NAME_PARAM)
                }
                all_elements.append(element_data)

        return routes.make_response({
            "success": True,
            "total_count": len(all_elements),
            "elements": all_elements
        })
        
    except Exception as e:
        return routes.make_response({"error": str(e)}), 500


@api.route("/api/mep/set-parameter", methods=["POST"])
def set_element_parameter():
    """Set parameter value for MEP elements.
    
    Expected JSON payload:
    {
        "element_ids": [123456, 789012],
        "parameter_name": "Comments",
        "parameter_value": "New value"
    }
    """
    try:
        doc = HOST_APP.doc
        if not doc:
            return routes.make_response({"error": "No active document"}), 400
            
        data = routes.request.get_json()
        if not data:
            return routes.make_response({"error": "No JSON data provided"}), 400
            
        element_ids = data.get("element_ids", [])
        parameter_name = data.get("parameter_name")
        parameter_value = data.get("parameter_value")
        
        if not element_ids:
            return routes.make_response({"error": "No element IDs provided"}), 400
        if not parameter_name:
            return routes.make_response({"error": "No parameter name provided"}), 400
        if parameter_value is None:
            return routes.make_response({"error": "No parameter value provided"}), 400

        # Set parameters with transaction
        results = set_parameters_with_transaction(doc, element_ids, parameter_name, parameter_value)

        return routes.make_response({
            "success": True,
            "updated_count": results["updated_count"],
            "failed_count": results["failed_count"],
            "errors": results["errors"]
        })
        
    except Exception as e:
        return routes.make_response({"error": str(e)}), 500


# Helper Functions

def get_parameter_value(element, builtin_param):
    """Get parameter value from element."""
    try:
        param = element.get_Parameter(builtin_param)
        if param and param.HasValue:
            if param.StorageType == DB.StorageType.Double:
                return param.AsDouble()
            elif param.StorageType == DB.StorageType.Integer:
                return param.AsInteger()
            elif param.StorageType == DB.StorageType.String:
                return param.AsString()
            elif param.StorageType == DB.StorageType.ElementId:
                return param.AsElementId().IntegerValue
        return None
    except:
        return None


def set_parameters_with_transaction(doc, element_ids, parameter_name, parameter_value):
    """Set parameter values for multiple elements within a transaction."""
    results = {
        "updated_count": 0,
        "failed_count": 0,
        "errors": []
    }
    
    try:
        # Start transaction
        transaction = DB.Transaction(doc, "Set MEP Parameters")
        transaction.Start()
        
        try:
            for element_id in element_ids:
                try:
                    # Get element
                    element = doc.GetElement(DB.ElementId(element_id))
                    if not element:
                        results["failed_count"] += 1
                        results["errors"].append("Element {} not found".format(element_id))
                        continue
                    
                    # Find parameter by name
                    param = None
                    for p in element.Parameters:
                        if p.Definition.Name == parameter_name:
                            param = p
                            break
                    
                    if not param:
                        results["failed_count"] += 1
                        results["errors"].append("Parameter '{}' not found in element {}".format(parameter_name, element_id))
                        continue
                    
                    if param.IsReadOnly:
                        results["failed_count"] += 1
                        results["errors"].append("Parameter '{}' is read-only in element {}".format(parameter_name, element_id))
                        continue
                    
                    # Set parameter value based on storage type
                    if param.StorageType == DB.StorageType.String:
                        param.Set(str(parameter_value))
                    elif param.StorageType == DB.StorageType.Double:
                        param.Set(float(parameter_value))
                    elif param.StorageType == DB.StorageType.Integer:
                        param.Set(int(parameter_value))
                    else:
                        results["failed_count"] += 1
                        results["errors"].append("Unsupported parameter type for element {}".format(element_id))
                        continue
                    
                    results["updated_count"] += 1
                    
                except Exception as e:
                    results["failed_count"] += 1
                    results["errors"].append("Error updating element {}: {}".format(element_id, str(e)))
            
            # Commit transaction
            transaction.Commit()
            
        except Exception as e:
            transaction.RollBack()
            results["errors"].append("Transaction failed: {}".format(str(e)))
            
    except Exception as e:
        results["errors"].append("Failed to start transaction: {}".format(str(e)))
    
    return results


