---
description: DCMvn MiniExcel integration patterns for project-agnostic Excel operations
---

# DCMvn MiniExcel Integration Patterns

## Module-Level Assembly Loading Pattern
Always load MiniExcel assembly at the top of your module:

```python
# coding: utf-8
import clr
import os

# DCMvn core imports
from DCMvn.coreutils.assemblyhandler import load_miniexcel
from DCMvn.core.framework import IDictionary, Debug
from DCMvn.forms import alert

# Load MiniExcel assembly at module level (recommended)
try:
    load_miniexcel()
    from MiniExcelLibs import MiniExcel
    from MiniExcelLibs.OpenXml import OpenXmlConfiguration
    MINIEXCEL_AVAILABLE = True
except Exception as e:
    Debug.WriteLine("Failed to load MiniExcel: {}".format(str(e)))
    MINIEXCEL_AVAILABLE = False

# .NET collections support
clr.AddReference("System.Collections.Specialized")
from System.Collections.Specialized import OrderedDictionary
from System.Collections.Generic import Dictionary

# Check availability before using Excel operations
if not MINIEXCEL_AVAILABLE:
    raise ImportError("MiniExcel could not be loaded through DCMvn")
```

## Excel Reader Class Pattern
Create reusable Excel reader classes using DCMvn:

```python
from DCMvn.core.framework import IDictionary
from DCMvn.coreutils.assemblyhandler import load_miniexcel

class DCMvnExcelReader(object):
    """Excel reader using DCMvn MiniExcel integration."""
    
    def __init__(self, excel_path, sheet_name=None):
        self.excel_path = excel_path
        self.sheet_name = sheet_name
        self._data = None
        self._sheet_names = None
        
        # MiniExcel already loaded at module level
        self._miniexcel = MiniExcel
    
    @property
    def sheet_names(self):
        """Get all available sheet names."""
        if self._sheet_names is None:
            try:
                self._sheet_names = list(self._miniexcel.GetSheetNames(self.excel_path))
            except Exception:
                self._sheet_names = []
        return self._sheet_names
    
    @property
    def data(self):
        """Get data from specified sheet."""
        if self._data is None:
            self._data = self.load_data()
        return self._data
    
    def load_data(self, sheet_name=None, use_header_row=True, start_cell="A1"):
        """Load data from Excel sheet."""
        try:
            target_sheet = sheet_name or self.sheet_name or "Sheet1"
            
            data = self._miniexcel.Query(
                self.excel_path,
                useHeaderRow=use_header_row,
                sheetName=target_sheet,
                startCell=start_cell
            ).Cast[IDictionary[str, object]]()
            
            return data
                    except Exception as e:
                Debug.WriteLine("Failed to load Excel data: {}".format(str(e)))
                return None
    
    def get_by_column_value(self, column_name, value):
        """Get row where column equals specific value."""
        try:
            if not self.data:
                return None
            
            import System
            from System.Linq import Enumerable
            
            return Enumerable.FirstOrDefault(
                self.data,
                lambda x: getattr(x, column_name, None) == value
            )
        except Exception:
            return None
    
    def filter_by_column(self, column_name, value):
        """Filter data by column value."""
        try:
            if not self.data:
                return []
            
            import System
            from System.Linq import Enumerable
            
            return Enumerable.Where(
                self.data,
                lambda x: getattr(x, column_name, None) == value
            ).ToList()
        except Exception:
            return []
```

## Advanced Configuration Reader
For complex Excel files with specific requirements:

```python
class AdvancedExcelReader(DCMvnExcelReader):
    """Advanced Excel reader with configuration options."""
    
    def __init__(self, excel_path, sheet_name=None, config_options=None):
        super(AdvancedExcelReader, self).__init__(excel_path, sheet_name)
        self.config_options = config_options or {}
        
        # Configuration class already loaded at module level
        self._config_class = OpenXmlConfiguration
    
    def load_data_with_config(self, sheet_name=None, start_cell="A1"):
        """Load data with advanced configuration."""
        try:
            target_sheet = sheet_name or self.sheet_name or "Sheet1"
            
            # Create configuration
            config = self._config_class()
            config.FillMergedCells = self.config_options.get('fill_merged_cells', True)
            
            data = self._miniexcel.Query(
                self.excel_path,
                useHeaderRow=True,
                startCell=start_cell,
                sheetName=target_sheet,
                configuration=config
            ).Cast[IDictionary[str, object]]()
            
            return data
                    except Exception as e:
                Debug.WriteLine("Failed to load Excel with config: {}".format(str(e)))
                return None
```

## Excel Writer Pattern
Safe Excel writing using DCMvn:

```python
class DCMvnExcelWriter(object):
    """Excel writer using DCMvn MiniExcel integration."""
    
    def __init__(self):
        # MiniExcel already loaded at module level
        self._miniexcel = MiniExcel
    
    def write_data(self, file_path, data, overwrite=True):
        """Write data to Excel file safely."""
        try:
            # Clean existing file if needed
            if overwrite and os.path.exists(file_path):
                os.remove(file_path)
            
            # Write data
            self._miniexcel.SaveAs(file_path, data)
            return True
            
        except Exception as e:
            Debug.WriteLine("Failed to write Excel: {}".format(str(e)))
            return False
    
    def write_multiple_sheets(self, file_path, sheet_data_dict, overwrite=True):
        """Write multiple sheets to Excel file."""
        try:
            # Ensure data is in correct format
            if not isinstance(sheet_data_dict, Dictionary):
                # Convert Python dict to .NET Dictionary
                net_dict = Dictionary[str, object]()
                for key, value in sheet_data_dict.items():
                    net_dict[key] = value
                sheet_data_dict = net_dict
            
            return self.write_data(file_path, sheet_data_dict, overwrite)
            
        except Exception as e:
            Debug.WriteLine("Failed to write multiple sheets: {}".format(str(e)))
            return False
```

## Data Processing Utilities
Common utilities for Excel data processing:

```python
def convert_excel_row_to_dict(excel_row):
    """Convert Excel row (IDictionary) to Python dict."""
    try:
        result = {}
        for key in excel_row.Keys:
            value = excel_row[key]
            result[str(key)] = value
        return result
    except Exception:
        return {}

def safe_get_excel_value(excel_row, column_name, default_value=None):
    """Safely get value from Excel row."""
    try:
        if hasattr(excel_row, column_name):
            value = getattr(excel_row, column_name)
            return value if value is not None else default_value
        return default_value
    except Exception:
        return default_value

def convert_excel_data_to_list(excel_data):
    """Convert Excel data to list of Python dictionaries."""
    try:
        result = []
        for row in excel_data:
            dict_row = convert_excel_row_to_dict(row)
            if dict_row:
                result.append(dict_row)
        return result
    except Exception:
        return []
```

## Integration with DCMvn IO
Use DCMvn's file selection with Excel operations:

```python
def select_and_read_excel_file(title="Select Excel File"):
    """Select Excel file using DCMvn and read data."""
    from DCMvn.io import pick_file
    from DCMvn.revit.ui import get_mainwindow
    
    # Select file
    excel_path = pick_file(
        title=title,
        file_ext="xlsx",
        owner=get_mainwindow()
    )
    
    if not excel_path:
        return None, None
    
    # Create reader
    reader = DCMvnExcelReader(excel_path)
    
    # Get sheet selection if multiple sheets
    sheet_names = reader.sheet_names
    if len(sheet_names) > 1:
        from DCMvn.forms import wpfforms
        selected_sheet = wpfforms.SelectFromList.show(
            sheet_names,
            "Select Sheet from Workbook"
        )
        if not selected_sheet:
            return None, None
        reader.sheet_name = selected_sheet
    
    return reader, reader.data

def save_excel_with_dcmvn_dialog(data, default_name="export.xlsx"):
    """Save Excel using DCMvn file dialog."""
    from DCMvn.io import save_excel_file
    
    file_path = save_excel_file(title="Save Excel File")
    if not file_path:
        return False
    
    writer = DCMvnExcelWriter()
    success = writer.write_data(file_path, data)
    
    if success:
        # Offer to open folder
        from DCMvn.forms import alert
        open_folder = alert(
            "File saved successfully:\n{}\n\nOpen folder?".format(file_path),
            yes=True, no=True
        )
        if open_folder:
            folder_path = os.path.dirname(file_path)
            os.startfile(folder_path)
    
    return success
```

## Best Practices for DCMvn Excel Integration

1. **Always use DCMvn assembly loading** - `load_miniexcel()` from `DCMvn.coreutils.assemblyhandler`
2. **Handle assembly loading failures** - Check if MiniExcel loads successfully
3. **Use IDictionary casting** - Cast query results to `IDictionary[str, object]`
4. **Implement proper error handling** - Use DCMvn's Debug.WriteLine for logging
5. **Integrate with DCMvn UI** - Use DCMvn's file dialogs and alerts
6. **Clean up files safely** - Remove existing files before overwriting
7. **Support multiple sheets** - Always check for multiple sheets in workbooks
8. **Use configuration when needed** - OpenXmlConfiguration for complex scenarios
9. **Convert data types properly** - Handle .NET to Python type conversions
10. **Provide user feedback** - Use DCMvn's alert system for notifications