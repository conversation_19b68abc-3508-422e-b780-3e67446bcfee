from DCMvn.core import DB
from DCMvn.core.framework import List

MODEL_NAME = "Model Name"
SYSTEM_FULL_NAME = "System Full Name"
SYSTEM_TYPE = "System Type (Service Type / Workset)"
VALIDATE_SYSTEM_TYPE_CONTAIN_STRING = "Validate_System Type_Contain String"
SYSTEM_CLASSIFICATION = "System Classification"
SYSTEM_ABBREVIATION = "System Abbreviation"
ROUTE_SIZE = "Route_Size"
ROUTE_MATERIAL = "Route_Material"
ROUTE_STANDARD = "Route_Standard"
ROUTE_FITTING_CONNECTION = "Route_Fitting Connection"
ROUTE_TYPE_NAME = "Route_Type Name"
FILTER_NAME = "Filter_Name"
FILTER_CATEGORIES = "Filter_Categories"
FILTER_COLOR = "Filter_Color"

MEP_CLASSIFICATION_MAP = {
    "Domestic Hot Water": DB.MEPSystemClassification.DomesticHotWater,
    "Domestic Cold Water": DB.MEPSystemClassification.DomesticColdWater,
    "Hydronic Supply": DB.MEPSystemClassification.SupplyHydronic,
    "Hydronic Return": DB.MEPSystemClassification.ReturnHydronic,
    "Return Air": DB.MEPSystemClassification.ReturnAir,
    "Supply Air": DB.MEPSystemClassification.SupplyAir,
    "Exhaust Air": DB.MEPSystemClassification.ExhaustAir,
    "Sanitary": DB.MEPSystemClassification.Sanitary,
    "Fire Protection Wet": DB.MEPSystemClassification.FireProtectWet,
    "Other": DB.MEPSystemClassification.OtherPipe,
}

PIPE_CATEGORIES = List[DB.ElementId](
    [
        DB.ElementId(DB.BuiltInCategory.OST_PipeCurves),
        DB.ElementId(DB.BuiltInCategory.OST_PipeFitting),
        DB.ElementId(DB.BuiltInCategory.OST_PipeAccessory),
        DB.ElementId(DB.BuiltInCategory.OST_PipeInsulations),
        DB.ElementId(DB.BuiltInCategory.OST_FlexPipeCurves),
    ]
)

DUCT_CATEGORIES = List[DB.ElementId](
    [
        DB.ElementId(DB.BuiltInCategory.OST_DuctCurves),
        DB.ElementId(DB.BuiltInCategory.OST_DuctFitting),
        DB.ElementId(DB.BuiltInCategory.OST_DuctAccessory),
        DB.ElementId(DB.BuiltInCategory.OST_DuctInsulations),
        DB.ElementId(DB.BuiltInCategory.OST_FlexDuctCurves)
    ]
)

CABLETRAY_CATEGORIES = List[DB.ElementId](
    [
        DB.ElementId(DB.BuiltInCategory.OST_CableTray),
        DB.ElementId(DB.BuiltInCategory.OST_CableTrayFitting),
    ]
)


ROUTE_CATEGORIES_MAP = {
    "Model SAN": PIPE_CATEGORIES,
    "Model HZG": PIPE_CATEGORIES,
    "Model KLT": DUCT_CATEGORIES,
    "Model RLT": DUCT_CATEGORIES,
    "Model SPR": PIPE_CATEGORIES,
    "Model ELE": CABLETRAY_CATEGORIES,
}
