import clr
from DCMvn.core import DB
from DCMvn.core.framework import System, List
from DCMvn.forms import alert, wpfforms

from clashes import MepMassObject, ArcObject
from constant import HG_IFC_LINK_MAPPER, O1_IFC_LINK_MAPPER
from utils import get_workset_by_name

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)


def is_physical_element(element):
    pass


def get_condition(document, is_hg_tbz):
    # type: (DB.Document, bool) -> tuple[str, list[DB.RevitLinkInstance]]  # noqa
    """
    Get condition and apply IFC link to the condition
    Args:
        document (DB.Document): Revit document
        is_hg_tbz (bool): True if HG model

    Returns:
        tuple: (condition, list[DB.Document])

    """
    all_link_instances = (DB.FilteredElementCollector(document).OfClass(DB.RevitLinkInstance)  # noqa
                     .Where(lambda x: DB.RevitLinkType.IsLoaded(document, x.GetTypeId()))
                     .ToList())
    ifc_link_instances = [i for i in all_link_instances if ".ifc" in i.Name]

    if not ifc_link_instances:
        alert("No IFC link found for selected condition", warn_icon=True, exitscript=True)

    if is_hg_tbz:
        mapper = HG_IFC_LINK_MAPPER
    else:
        mapper = O1_IFC_LINK_MAPPER

    select_condition = wpfforms.SelectFromList.show(context=mapper.keys(),
                                                    show_maxrestore_button=False,
                                                    show_min_button=False,
                                                    width=300, height=300,
                                                    title="Select Condition")

    if select_condition is None:
        alert("Canceled", warn_icon=True, exitscript=True)

    condition_docs_title = mapper[select_condition]
    condition_instances = [i for i in ifc_link_instances if any([j in i.Name for j in condition_docs_title])]
    return select_condition, condition_instances


def get_mass(document, condition_workset):
    # type: (DB.Document, str) -> list[MepMassObject]  # noqa
    """
    Get all mass elements in the condition workset
    Args:
        document (DB.Document): current document
        condition_workset (str): workset name

    Returns:
        list[MepMassObject]: mass elements
    """
    workset = get_workset_by_name(document, condition_workset)
    if workset is None:
        alert("Workset {} not found".format(condition_workset), warn_icon=True, exitscript=True)
    workset_filter = DB.ElementWorksetFilter(workset.Id)
    masses = (DB.FilteredElementCollector(document).OfCategory(DB.BuiltInCategory.OST_Mass)
              .WhereElementIsNotElementType().WherePasses(workset_filter).ToElements())
    return [MepMassObject(i) for i in masses]


def get_arc_elements(link_instances):
    # type: (list[DB.RevitLinkInstance]) -> list[ArcObject]  # noqa
    """
    Get all arc elements in the condition workset
    Args:
        link_instances (list[DB.RevitLinkInstance]): Revit link instances

    Returns:
        list[ArcObject]: arc elements
    """
    arc_elements = []
    for link in link_instances:
        elements = (DB.FilteredElementCollector(link.GetLinkDocument()).WhereElementIsViewIndependent()
                    .WhereElementIsNotElementType().ToElements())
        transform = link.GetTotalTransform()
        arc_elements += [ArcObject(i, transform) for i in elements if is_physical_element(i)]
    return arc_elements


def get_arc_filter(is_mass_floor_case=False):
    # type: (bool) -> DB.ElementFilter
    """
    Get arc element filter
    Args:
        is_mass_floor_case (bool): True if get filter for mass floor case
    Returns:
        DB.ElementFilter: arc element filter
    """
    if is_mass_floor_case:
        categories_filter = DB.ElementMulticategoryFilter(List[DB.BuiltInCategory]([
                DB.BuiltInCategory.OST_StructuralColumns, 
                DB.BuiltInCategory.OST_Columns,
                DB.BuiltInCategory.OST_Walls]))
    else:
        categories_filter = DB.ElementMulticategoryFilter(List[DB.BuiltInCategory]([
            DB.BuiltInCategory.OST_StructuralColumns,
            DB.BuiltInCategory.OST_Columns,
            DB.BuiltInCategory.OST_Walls, 
            DB.BuiltInCategory.OST_StructuralFraming]))
    
    return categories_filter
