import System
import clr
import json
import re

from DCMvn.core.framework import IDictionary
from DCMvn.core import DB, HOST_APP
from DCMvn.coreutils.assemblyhandler import load_miniexcel
from DCMvn.revit.selection import DSelection
from pyrevit import script

load_miniexcel()
from MiniExcelLibs import MiniExcel

from MunichRE.constants import *
from MunichRE.geometry.duct import DuctSegment
import ast

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

# clr.AddReference("RevitNodes")
# import Revit
#
# clr.ImportExtensions(Revit.GeometryConversion)
# clr.ImportExtensions(Revit.Elements)

output = script.get_output()

file_path = r"C:\Users\<USER>\Downloads\241004_MRE-HG_KBP_MX_MEP_RLTA_1000_IfcOutput_noRound.xlsx"

excel = MiniExcel.Query(file_path, useHeaderRow=True).Cast[ # noqa
    IDictionary[str, object]]()  # type: IDictionary[str, object]  # noqa

linked_ele = DSelection(is_linked=True).pick()
guid = linked_ele.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString()
data_excel = excel.FirstOrDefault(lambda x: getattr(x, GLOBAL_ID) == guid)  # noqa
print(getattr(data_excel, GLOBAL_ID))

height = re.sub(r"[^\d.]", "", getattr(data_excel, DUCT_HEIGHT))  # noqa
width = re.sub(r"[^\d.]", "", getattr(data_excel, DUCT_WIDTH))
nominal_diameter = re.sub(r"[^\d.]", "", getattr(data_excel, PIPE_NOMINAL_DIAMETER))
length = getattr(data_excel, DUCT_LENGTH)

origin = list(ast.literal_eval(getattr(data_excel, CENTROID_MM)))
direct_x = list(ast.literal_eval(getattr(data_excel, DIRECTION_X)))
direct_y = list(ast.literal_eval(getattr(data_excel, DIRECTION_Y)))
direct_z = list(ast.literal_eval(getattr(data_excel, DIRECTION_Z)))
half_distance = list(ast.literal_eval(getattr(data_excel, HALF_DISTANCE_MM)))


def to_internal_unit(value):
    return DB.UnitUtils.ConvertToInternalUnits(value, DB.UnitTypeId.Millimeters)


transform = DB.Transform.Identity  # type: DB.Transform
transform.Origin = DB.XYZ(to_internal_unit(origin[0]), to_internal_unit(origin[1]), to_internal_unit(origin[2]))
transform.BasisX = DB.XYZ(direct_x[0], direct_x[1], direct_x[2])
transform.BasisY = DB.XYZ(direct_y[0], direct_y[1], direct_y[2])
transform.BasisZ = DB.XYZ(direct_z[0], direct_z[1], direct_z[2])

bbox = DB.BoundingBoxXYZ()
bbox.Transform = transform
bbox.Min = DB.XYZ(-to_internal_unit(half_distance[0]), -to_internal_unit(half_distance[1]),
                  -to_internal_unit(half_distance[2]))
bbox.Max = DB.XYZ(to_internal_unit(half_distance[0]), to_internal_unit(half_distance[1]),
                  to_internal_unit(half_distance[2]))

min_pt = bbox.Min
max_pt = bbox.Max

_pt0 = min_pt
_pt1 = DB.XYZ(max_pt.X, min_pt.Y, min_pt.Z)
_pt2 = DB.XYZ(max_pt.X, max_pt.Y, min_pt.Z)
_pt3 = DB.XYZ(min_pt.X, max_pt.Y, min_pt.Z)
_pt4 = DB.XYZ(min_pt.X, min_pt.Y, max_pt.Z)
_pt5 = DB.XYZ(max_pt.X, min_pt.Y, max_pt.Z)
_pt6 = max_pt
_pt7 = DB.XYZ(min_pt.X, max_pt.Y, max_pt.Z)

edge1 = DB.Line.CreateBound(_pt0, _pt1).CreateTransformed(transform)
edge2 = DB.Line.CreateBound(_pt0, _pt3).CreateTransformed(transform)
edge3 = DB.Line.CreateBound(_pt0, _pt4).CreateTransformed(transform)

edge_dict = {
    _pt1: edge1,
    _pt3: edge2,
    _pt4: edge3
}

group_lenth = self.__get_obbox_edges().items().OrderByDescending(  # noqa
    lambda x: abs(x[1].Length - length)).ToList()
cross_points = group_lenth.Take(2).Select(lambda x: x[0]).ToList()
side_edges = group_lenth.Take(2).Select(lambda x: x[1]).ToList()
direction_edge = group_lenth.Last()[1]

edge = DB.Line.CreateBound(side_edges[0], side_edges[1]).CreateTransformed(transform)
start_point = edge.Evaluate(0.5, True)
end_point = start_point + length * direction_edge.Direction

pipe_type_id = DB.ElementId(99508)
levelid = DB.FilteredElementCollector(HOST_APP.doc).OfClass(DB.Level).FirstElementId()
systemTypeid = DB.FilteredElementCollector(HOST_APP.doc).OfCategory(DB.BuiltInCategory.OST_PipingSystem).FirstElementId()
with DB.Transaction(HOST_APP.doc, "Create Pipe") as t:
    t.Start()
    pipe_ = DB.Plumbing.Pipe.Create(HOST_APP.doc, systemTypeid, pipe_type_id, levelid,
                                    start_point, end_point)

    pipe_.get_Parameter(DB.BuiltInParameter.RBS_PIPE_DIAMETER_PARAM).Set(nominal_diameter)
    pipe_.get_Parameter(DB.BuiltInParameter.IFC_GUID).Set(id)
    t.Commit()
