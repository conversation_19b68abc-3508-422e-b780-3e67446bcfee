# coding: utf-8
"""
HtmlReporter for TabooZone QA (DTO-based)

- Uses pyRevit output.print_html() and output.linkify()
- Compatible with IronPython 2.7 / legacy WebBrowser control
- Follows styling and structure patterns of legacy GuidValidator.print_html_report
"""
from DCMvn.core import HOST_APP, DB
from DCMvn.core.framework import Debug
from pyrevit.output import PyRevitOutputWindow
from ..validation import constants
from ..validation.models import ValidationResult


class HtmlReporter(object):
    def __init__(self):
        pass

    @staticmethod
    def html_escape(text):
        try:
            if text is None:
                return ""
            s = str(text)
            s = s.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")
            return s
        except Exception as ex:
            Debug.WriteLine("HtmlReporter.html_escape error: {}".format(str(ex)))
            return ""

    @staticmethod
    def linkify_or_id(element, output, current_document):
        # type: (DB.Element, PyRevitOutputWindow, DB.Document) -> str
        try:
            if not element.Document.IsLinked:
                return output.linkify(element.Id)
            try:
                return HtmlReporter.html_escape(element.Id.Value)
            except Exception as ex:
                Debug.WriteLine("HtmlReporter.linkify_or_id fallback error: {}".format(str(ex)))
                return HtmlReporter.html_escape(element.Id)
        except Exception as ex:
            Debug.WriteLine("HtmlReporter.linkify_or_id error: {}".format(str(ex)))
            return ""

    def export_validation_result(self, validation_result, output, current_document, rule_name=None):
        # type: (ValidationResult, HOST_APP.Output, DB.Document, str) -> None
        """
        Render ValidationResult to pyRevit output as styled HTML.
        """
        try:
            # Header & basic styles (inline to avoid external dependencies)
            header = "" + \
                "<div style='font-family:Segoe UI, Arial, sans-serif;margin:8px 0;'>" + \
                "<h2 style='margin:4px 0 8px 0;'>TabooZone QA Report" + (" - " + self.html_escape(rule_name) if rule_name else "") + "</h2>" + \
                "<div style='font-size:12px;color:#666;'>ARC: " + self.html_escape(getattr(validation_result, 'arc_name', '')) + \
                " | Mass: " + self.html_escape(getattr(validation_result, 'mass_name', '')) + \
                " | Tol: " + self.html_escape(getattr(validation_result, 'tolerance_mm', '')) + " mm</div>" + \
                "</div>"
            output.print_html(header)

            # Compute summary stats
            matched = len(getattr(validation_result, 'matched_pairs', []) or [])
            missing = len(getattr(validation_result, 'missing_pairs', []) or [])
            extra = len(getattr(validation_result, 'extra_pairs', []) or [])
            dup_arc = len(getattr(validation_result, 'duplicate_arc_pairs', []) or [])
            dup_mass = len(getattr(validation_result, 'duplicate_mass_pairs', []) or [])

            centroid_pairs = getattr(validation_result, 'centroid_pairs', []) or []
            centroid_pass = 0
            centroid_fail = 0
            for cp in centroid_pairs:
                try:
                    if getattr(cp, 'within_tolerance', False):
                        centroid_pass += 1
                    else:
                        centroid_fail += 1
                except Exception as ex:
                    Debug.WriteLine("HtmlReporter: export_validation_result centroid count error: {}".format(str(ex)))
                    continue

            # (Summary cards moved to the end per new flow)
            def _build_summary_cards():
                cards = []
                def card(label, value, background, icon_text):
                    return ("<div style='padding:8px 12px;border-radius:6px;background:" + background +
                            ";color:#111;min-width:140px;border:1px solid rgba(0,0,0,0.1);'>" +
                            "<div style='font-size:12px;opacity:0.8'>" + icon_text + " " + self.html_escape(label) + "</div>" +
                            "<div style='font-size:18px;font-weight:600'>" + self.html_escape(value) + "</div>" +
                            "</div>")
                ok = " :white_heavy_check_mark: "
                err = " :cross_mark: "
                warn = " :warning: "
                cards.append(card("Matched", matched, "#d5f5e3", ok))
                cards.append(card("Missing", missing, "#fadbd8", err))
                cards.append(card("Extra", extra, "#fadbd8", err))
                cards.append(card("Duplicate ARC", dup_arc, "#fcf3cf", warn))
                cards.append(card("Duplicate Mass", dup_mass, "#fcf3cf", warn))
                cards.append(card("Centroid Passed", centroid_pass, "#d5f5e3", ok))
                cards.append(card("Centroid Failed", centroid_fail, "#fadbd8", err))
                return "<div style='display:flex;flex-wrap:wrap;gap:10px;margin:8px 0 16px 0;'>" + "".join(cards) + "</div>"

            # Detailed sections
            def h3(title):
                output.print_html("<h3 style='margin:10px 0 6px 0;'>" + self.html_escape(title) + "</h3>")

            # Missing
            h3("Missing in Mass (ARC present, Mass missing)")
            if missing > 0:
                rows = ["<tr><th style='text-align:left'>GUID</th><th style='text-align:left'>ARC Element</th><th style='text-align:left'>ARC Info</th></tr>"]
                for gp in getattr(validation_result, 'missing_pairs', []) or []:
                    try:
                        guid = self.html_escape(getattr(gp, 'guid', ''))
                        arc = getattr(gp, 'element_info', None)
                        rows.append("<tr style='background:#fdecea'><td>" + guid + "</td><td>" + self.html_escape(getattr(arc, 'element_id', '')) + "</td><td>" + self.html_escape(getattr(arc, 'display_string', '')) + "</td></tr>")
                    except Exception as ex:
                        Debug.WriteLine("HtmlReporter: export_validation_result missing row error: {}".format(str(ex)))
                        continue
                output.print_html("<table style='border-collapse:collapse;width:100%'>" + "".join(rows) + "</table>")
            else:
                output.print_html("<i>No missing GUIDs.</i>")

            # Extra
            h3("Extra in Mass (Mass present, ARC missing)")
            if extra > 0:
                rows = ["<tr><th style='text-align:left'>GUID</th><th style='text-align:left'>Mass Element</th><th style='text-align:left'>Mass Info</th></tr>"]
                for gp in getattr(validation_result, 'extra_pairs', []) or []:
                    try:
                        guid = self.html_escape(getattr(gp, 'guid', ''))
                        mass = getattr(gp, 'element_info', None)
                        rows.append("<tr style='background:#fdecea'><td>" + guid + "</td><td>" + self.linkify_or_id(getattr(mass, 'element', None), output, current_document) + "</td><td>" + self.html_escape(getattr(mass, 'display_string', '')) + "</td></tr>")
                    except Exception as ex:
                        Debug.WriteLine("HtmlReporter: export_validation_result extra row error: {}".format(str(ex)))
                        continue
                output.print_html("<table style='border-collapse:collapse;width:100%'>" + "".join(rows) + "</table>")
            else:
                output.print_html("<i>No extra GUIDs.</i>")

            # Duplicates
            h3("Duplicate GUIDs in ARC")
            if dup_arc > 0:
                rows = ["<tr><th style='text-align:left'>GUID</th><th style='text-align:left'>ARC Element</th><th style='text-align:left'>ARC Info</th></tr>"]
                for gp in getattr(validation_result, 'duplicate_arc_pairs', []) or []:
                    try:
                        guid = self.html_escape(getattr(gp, 'guid', ''))
                        el = getattr(gp, 'element_info', None)
                        rows.append("<tr style='background:#fff4d6'><td>" + guid + "</td><td>" + self.html_escape(getattr(el, 'element_id', '')) + "</td><td>" + self.html_escape(getattr(el, 'display_string', '')) + "</td></tr>")
                    except Exception as ex:
                        Debug.WriteLine("HtmlReporter: export_validation_result dup_arc row error: {}".format(str(ex)))
                        continue
                output.print_html("<table style='border-collapse:collapse;width:100%'>" + "".join(rows) + "</table>")
            else:
                output.print_html("<i>No duplicate ARC GUIDs.</i>")

            h3("Duplicate GUIDs in Mass")
            if dup_mass > 0:
                rows = ["<tr><th style='text-align:left'>GUID</th><th style='text-align:left'>Mass Element</th><th style='text-align:left'>Mass Info</th></tr>"]
                for gp in getattr(validation_result, 'duplicate_mass_pairs', []) or []:
                    try:
                        guid = self.html_escape(getattr(gp, 'guid', ''))
                        el = getattr(gp, 'element_info', None)
                        rows.append("<tr style='background:#fff4d6'><td>" + guid + "</td><td>" + self.linkify_or_id(getattr(el, 'element', None), output, current_document) + "</td><td>" + self.html_escape(getattr(el, 'display_string', '')) + "</td></tr>")
                    except Exception as ex:
                        Debug.WriteLine("HtmlReporter: export_validation_result dup_mass row error: {}".format(str(ex)))
                        continue
                output.print_html("<table style='border-collapse:collapse;width:100%'>" + "".join(rows) + "</table>")
            else:
                output.print_html("<i>No duplicate Mass GUIDs.</i>")

            # Matched pairs (with centroid integration)
            h3("Matched GUID Pairs")
            if matched > 0:
                # Build quick lookup for centroid data by GUID
                cp_map = {}
                try:
                    for cp in centroid_pairs:
                        cp_map[getattr(cp, 'guid', None)] = cp
                except Exception as ex:
                    Debug.WriteLine("HtmlReporter: export_validation_result centroid map error: {}".format(str(ex)))
                    cp_map = {}
                rows = ["<tr><th style='text-align:left'>GUID</th><th style='text-align:left'>ARC Info</th><th style='text-align:left'>Mass Element</th><th style='text-align:left'>Mass Info</th><th style='text-align:left'>Distance (mm)</th><th style='text-align:left'>Result</th></tr>"]
                for mp in getattr(validation_result, 'matched_pairs', []) or []:
                    try:
                        guid_raw = getattr(mp, 'guid', '')
                        guid = self.html_escape(guid_raw)
                        arc_info = getattr(mp, 'arc_info', None)
                        mass_info = getattr(mp, 'mass_info', None)
                        cp = cp_map.get(guid_raw)
                        if cp is not None:
                            within = getattr(cp, 'within_tolerance', False)
                            dist = getattr(cp, 'distance_rounded', None)
                            res_text = constants.PASSED if within else constants.FAILED
                            bg = "#eafaf1" if within else "#fdecea"
                            dist_text = self.html_escape(dist) if dist is not None else "-"
                            res_cell = self.html_escape(res_text)
                        else:
                            bg = ""
                            dist_text = "-"
                            res_cell = ""
                        tr_style = (" style='background:" + bg + "'") if bg else ""
                        rows.append("<tr" + tr_style + "><td>" + guid + "</td><td>" + self.html_escape(getattr(arc_info, 'display_string', '')) + "</td><td>" + self.linkify_or_id(getattr(mass_info, 'element', None), output, current_document) + "</td><td>" + self.html_escape(getattr(mass_info, 'display_string', '')) + "</td><td>" + dist_text + "</td><td>" + res_cell + "</td></tr>")
                    except Exception as ex:
                        Debug.WriteLine("HtmlReporter: export_validation_result matched row error: {}".format(str(ex)))
                        continue
                output.print_html("<table style='border-collapse:collapse;width:100%'>" + "".join(rows) + "</table>")
            else:
                output.print_html("<i>No matched pairs.</i>")



            # Summary section moved to end with rule/doc info and totals
            output.print_html("<h3 style='margin:12px 0 6px 0;'>Summary</h3>")
            try:
                row_html = _build_summary_cards()
                output.print_html(row_html)
            except Exception as ex:
                Debug.WriteLine("HtmlReporter: export_validation_result summary cards error: {}".format(str(ex)))
                pass

            # Compute document titles from available DTOs
            def _get_doc_title_from_ele_info(ele_info):
                try:
                    ele_info = getattr(ele_info, 'element', None)
                    if ele_info is not None and hasattr(ele_info, 'Document') and ele_info.Document is not None:
                        return ele_info.Document.Title
                except Exception as e:
                    Debug.WriteLine("HtmlReporter: _get_doc_title_from_ele_info error: {}".format(str(e)))
                    return None
                return None

            arc_doc_title = None
            mass_doc_title = None
            try:
                # Try matched pairs first
                for mp in getattr(validation_result, 'matched_pairs', []) or []:
                    if arc_doc_title is None:
                        arc_doc_title = _get_doc_title_from_ele_info(getattr(mp, 'arc_info', None))
                    if mass_doc_title is None:
                        mass_doc_title = _get_doc_title_from_ele_info(getattr(mp, 'mass_info', None))
                    if arc_doc_title and mass_doc_title:
                        break
                # Fallbacks
                if arc_doc_title is None:
                    for gp in getattr(validation_result, 'missing_pairs', []) or []:
                        arc_doc_title = _get_doc_title_from_ele_info(getattr(gp, 'element_info', None))
                        if arc_doc_title:
                            break
                if mass_doc_title is None:
                    for gp in getattr(validation_result, 'extra_pairs', []) or []:
                        mass_doc_title = _get_doc_title_from_ele_info(getattr(gp, 'element_info', None))
                        if mass_doc_title:
                            break
                if arc_doc_title is None:
                    for gp in getattr(validation_result, 'duplicate_arc_pairs', []) or []:
                        arc_doc_title = _get_doc_title_from_ele_info(getattr(gp, 'element_info', None))
                        if arc_doc_title:
                            break
                if mass_doc_title is None:
                    for gp in getattr(validation_result, 'duplicate_mass_pairs', []) or []:
                        mass_doc_title = _get_doc_title_from_ele_info(getattr(gp, 'element_info', None))
                        if mass_doc_title:
                            break
            except Exception as ex:
                Debug.WriteLine("HtmlReporter: export_validation_result doc title error: {}".format(str(ex)))
                pass

            # Rule, docs, totals block
            info_html = "<div style='margin:4px 0 10px 0;padding:8px;border-radius:6px;background:#f9f9f9;border:1px solid rgba(0,0,0,0.08);font-size:12px;color:#444'>" + \
                        ("<div><b>Rule:</b> " + self.html_escape(rule_name) + "</div>" if rule_name else "") + \
                        "<div><b>ARC Document:</b> " + self.html_escape(arc_doc_title or "-") + "</div>" + \
                        "<div><b>Mass Document:</b> " + self.html_escape(mass_doc_title or "-") + "</div>" + \
                        "<div><b>Total ARC elements:</b> " + self.html_escape(str(getattr(validation_result, 'total_arc_elements', 0))) + \
                        " &nbsp;&nbsp; <b>Total Mass elements:</b> " + self.html_escape(str(getattr(validation_result, 'total_mass_elements', 0))) + "</div>" + \
                        "</div>"
            output.print_html(info_html)

            # Overall status banner at very end
            status = (" :white_heavy_check_mark: PASSED" if not getattr(validation_result, 'has_issues', False) else ":cross_mark: FAILED")
            output.print_html("<div style='margin:12px 0;padding:10px;border-radius:6px;background:#f4f6f6'>" +
                              "<b>Overall Status:</b> " + status + "</div>")
        except Exception as ex:
            Debug.WriteLine("HtmlReporter: export_validation_result error: {}".format(str(ex)))
