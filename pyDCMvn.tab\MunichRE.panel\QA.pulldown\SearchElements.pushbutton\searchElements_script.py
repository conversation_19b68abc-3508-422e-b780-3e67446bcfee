# coding: utf-8
import os.path as op
from DCMvn.core import HOST_APP, DB
from DCMvn.revit.transaction import transaction_wrapper
from lib.ui import SearchElementsViewModel, SearchElements
from lib.interaction import set_scope
from lib.external.external_event import ExternalEventSolver

uidoc = HOST_APP.uidoc


def main():
    ext_event = ExternalEventSolver(function_or_method=set_scope)
    xaml_path = op.join(op.dirname(__file__), "lib", "ui", "search_elements.xaml")
    view_model = SearchElementsViewModel(uidoc, ext_event)
    view = SearchElements(xaml_path, view_model)
    view.show(modal=False)

if __name__ == "__main__":
    main()