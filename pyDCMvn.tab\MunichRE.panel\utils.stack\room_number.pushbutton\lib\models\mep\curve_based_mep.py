# coding: utf-8
from DCMvn.core import DB
from .base_mep import BaseMep


class CurveBasedMep(BaseMep):
    """Wrapper for curve-based MEP elements (Ducts, Pipes, Cable Trays, Conduits).

    This class provides specialized functionality for MEP elements that have
    location curves, optimized for spatial mapping and clash checking operations.
    """

    def __init__(self, mep_element):
        # type: (DB.Element) -> None
        """Initialize a curve-based MEP element wrapper.

        Args:
            mep_element (DB.Element): The Revit MEP element with location curve

        Raises:
            TypeError: If element doesn't have a LocationCurve
        """
        super(CurveBasedMep, self).__init__(mep_element)
        self._location_curve = None  # type: DB.Curve | None

    @property
    def location_curve(self):
        # type: () -> DB.Curve | None
        """Get the location curve of the MEP element.

        Returns:
            DB.Curve: The location curve or None if not available
        """
        if self._location_curve is None:
            try:
                location = self.location
                if isinstance(location, DB.LocationCurve):
                    self._location_curve = location.Curve
            except Exception:
                pass
        return self._location_curve
        
    @property
    def curve_length(self):
        # type: () -> float
        """Get the length of the curve in internal units (feet).

        Returns:
            float: The curve length in feet or 0.0 if not available
        """
        try:
            curve = self.location_curve
            return curve.Length if curve else 0.0
        except Exception:
            return 0.0

    def __str__(self):
        # type: () -> str
        """String representation of the curve-based MEP element.

        Returns:
            str: String representation
        """
        return "CurveBasedMEP (Id={}, Length={:.2f}, Category={})".format(
            self._get_elementid_value(self.id), 
            self.curve_length, 
            self.get_category_name())

    def __repr__(self):
        # type: () -> str
        """Detailed string representation of the curve-based MEP element.

        Returns:
            str: Detailed string representation
        """
        return "CurveBasedMEP (Id={}, GUID={}, Length={:.2f}, Category={})".format(
            self._get_elementid_value(self.id), 
            self.guid, 
            self.curve_length, 
            self.get_category_name())
