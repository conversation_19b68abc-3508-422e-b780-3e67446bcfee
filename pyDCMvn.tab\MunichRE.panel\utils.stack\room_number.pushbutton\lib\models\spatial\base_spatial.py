# coding: utf-8
from DCMvn.core import DB
from DCMvn.core.framework import Debug
from pyrevit.compat import get_elementid_value_func
from ..document_wrapper import DocumentWrapper
from ..base_geometry import BaseGeometry


class BaseSpatial(BaseGeometry):
    """Base class for all spatial elements (Rooms, Spaces, DirectShapes).

    This class provides common functionality for spatial elements in Revit
    for accessing spatial element properties and geometry.
    """

    def __init__(
        self, element, document_wrapper=None, spatial_geometry_calculator=None
    ):
        # type: (DB.Element, DocumentWrapper, DB.SpatialElementGeometryCalculator) -> None
        """Initialize a spatial element wrapper.

        Args:
            spatial_element (DB.Element): The Revit spatial element to wrap
            document_wrapper (DocumentWrapper): The document wrapper
            spatial_geometry_calculator (DB.SpatialElementGeometryCalculator): The spatial geometry calculator
        """
        super(BaseSpatial, self).__init__(element, document_wrapper)
        self._spatial_geometry_calculator = spatial_geometry_calculator  # type: DB.SpatialElementGeometryCalculator | None
        self._get_elementid_value = get_elementid_value_func()

    @property
    def document_wrapper(self):
        # type: () -> DocumentWrapper
        """Get the document wrapper.

        Returns:
            DocumentWrapper: The document wrapper
        """
        return self._document_wrapper

    @property
    def element(self):
        # type: () -> DB.Element
        """Get the underlying Revit element.

        Returns:
            DB.Element: The wrapped Revit element
        """
        return self._element

    @property
    def id(self):
        # type: () -> DB.ElementId
        """Get the element ID.

        Returns:
            DB.ElementId: The element ID
        """
        return self._element.Id

    @property
    def guid(self):
        # type: () -> str
        """Get the IFC GUID of the spatial element.

        Returns:
            str: The IFC GUID or empty string if not available
        """
        param = self._element.get_Parameter(DB.BuiltInParameter.IFC_GUID)
        return param.AsString() or ""

    @property
    def document(self):
        # type: () -> DB.Document
        """Get the document containing this element.

        Returns:
            DB.Document: The document containing this element
        """
        return self._element.Document

    def is_point_inside(self, point):
        # type: (DB.XYZ) -> bool
        # TODO: Implement this method on children classes
        pass

    def get_parameter_value(self, parameter, use_string_for_specific=True):
        # type: (str | DB.BuiltInParameter, bool) -> str | float | DB.ElementId
        """Get parameter value as string.

        Args:
            parameter (str): Parameter name or BuiltInParameter
            use_string_for_specific (bool): Whether to return integer or ElementId as value string

        Returns:
            str: Parameter value or empty string if not found
        """
        try:
            if isinstance(parameter, str):
                param = self._element.LookupParameter(parameter)
            else:
                param = self._element.get_Parameter(parameter)

            if param and param.HasValue:
                if param.StorageType == DB.StorageType.String:
                    return param.AsString() or ""
                elif param.StorageType == DB.StorageType.Double:
                    return param.AsDouble()
                elif param.StorageType == DB.StorageType.Integer:
                    return (
                        param.AsValueString()
                        if use_string_for_specific
                        else param.AsInteger()
                    )
                elif param.StorageType == DB.StorageType.ElementId:
                    return param.AsElementId() if not use_string_for_specific else param.AsValueString()
        except:  # noqa
            import traceback

            Debug.WriteLine(
                "Error getting parameter value: {}".format(traceback.format_exc())
            )
        return ""

    @property
    def is_valid(self):
        # type: () -> bool
        """Check if the spatial element is valid.

        Returns:
            bool: True if the element is valid, False otherwise
        """
        try:
            return self._element.IsValidObject
        except:  # noqa
            return False

    def get_category(self):
        # type: () -> DB.Category | None
        """Get the category of the spatial element.

        Returns:
            DB.Category: The category or None if not available
        """
        try:
            return self._element.Category
        except:  # noqa
            return None

    def get_category_name(self):
        # type: () -> str
        """Get the category name of the spatial element.

        Returns:
            str: The category name or empty string if not available
        """
        try:
            category = self.get_category()
            return category.Name if category else ""
        except:  # noqa
            return ""

    def __str__(self):
        # type: () -> str
        """String representation of the spatial element.

        Returns:
            str: String representation
        """
        return "Spatial (Id={}, GUID={}, Category={})".format(
            self._get_elementid_value(self.id), self.guid, self.get_category_name()
        )

    def __repr__(self):
        # type: () -> str
        """Detailed string representation of the spatial element.

        Returns:
            str: Detailed string representation
        """
        return "Spatial (Id={}, GUID={}, Category={})".format(
            self._get_elementid_value(self.id), self.guid, self.get_category_name()
        )

    def __eq__(self, other):
        # type: (object) -> bool
        """Check equality based on element ID.

        Args:
            other (object): Other object to compare

        Returns:
            bool: True if elements have the same ID, False otherwise
        """
        if not isinstance(other, BaseSpatial):
            return False
        get_elementid_value = get_elementid_value_func()
        equal_id = get_elementid_value(self.id) == get_elementid_value(other.id)
        equal_doc = self.document.Equals(other.document)
        return equal_id and equal_doc

    def __hash__(self):
        # type: () -> int
        """Hash based on element ID.

        Returns:
            int: Hash value
        """
        return hash(self.element)
