# coding: utf-8

from .base_detection_strategy import BaseDetectionStrategy
from ...models import GenericSpatial, BaseSpatial, BaseMep
from ...utils import sample_curve_points

class GenericSpatialStrategy(BaseDetectionStrategy):
    """Detection strategy for GenericSpatial elements (IFC DirectShapes)."""

    def detect(self, mep_element, mep_type_info, spatial_element, show_visual=False):
        # type: (BaseMep, dict, BaseSpatial, bool) -> tuple[bool, float]
        """Handle scenarios 6-10: Generic spatial detection with optimized type checking."""
        
        if not isinstance(spatial_element, GenericSpatial):
            return False, 0
        
        if mep_type_info['is_family_instance']:
            if mep_type_info['has_location_curve']:
                return self._scenario_6_family_curve_generic_spatial(mep_element, mep_type_info, spatial_element, show_visual)
            elif mep_type_info['has_location_point']:
                return self._scenario_7_family_point_generic_spatial(mep_type_info, spatial_element, show_visual)
            else:
                return self._scenario_8_family_no_location_generic_spatial(mep_type_info, spatial_element, show_visual)
        elif mep_type_info['is_curve_element']:
            return self._scenario_9_curve_element_generic_spatial(mep_element, mep_type_info, spatial_element, show_visual)
        elif mep_type_info['is_direct_shape']:
            return self._scenario_10_directshape_generic_spatial(mep_type_info, spatial_element, show_visual)
        else:
            return self.check_geometry_spatial_intersection(mep_element, mep_type_info, spatial_element, show_visual)
            
    def _scenario_6_family_curve_generic_spatial(self, mep_element, mep_type_info, spatial_element, show_visual=False):
        # type: (BaseMep, dict, BaseSpatial, bool) -> tuple[bool, float]
        """Scenario 6: Generic spatial vs FamilyInstance curve-based."""
        try:
            # Priority 1: Check room calculation points first (most accurate)
            is_match, distance = self.geometry_detector.check_points(self.get_detection_points(mep_type_info), spatial_element, show_visual)
            if is_match:
                return True, distance
            
            # Priority 2: Use curve sampling as fallback
            if not mep_type_info['has_location_curve'] or not mep_type_info['curve']:
                return False, 0
                
            curve = mep_type_info['curve']
            sample_points = sample_curve_points(curve, 3)
            
            # Priority 3: Check curve above
            is_match, distance = self.geometry_detector.check_curve_above(curve, spatial_element)
            if is_match:
                return True, distance
                        
            # Priority 4: Check intersection
            is_match, distance = self.geometry_detector.check_intersection(mep_element, spatial_element, mep_type_info, show_visual)
            if is_match:
                return True, distance
                    
            # Priority 5: Direct point sampling (fallback method)
            for point in sample_points:
                is_match, distance = self.geometry_detector.check_points([point], spatial_element, show_visual)
                if is_match:
                    return True, distance              
            return False, 0
        except:  # noqa
            return False, 0
            
    def _scenario_7_family_point_generic_spatial(self, mep_type_info, spatial_element, show_visual=False):
        # type: (dict, BaseSpatial, bool) -> tuple[bool, float]
        """Scenario 7: Generic spatial vs FamilyInstance point-based."""
        try:
            is_match, distance = self.geometry_detector.check_points(self.get_detection_points(mep_type_info), spatial_element, show_visual)
            if is_match:
                return True, distance
            return False, 0
        except:  # noqa
            return False, 0
            
    def _scenario_8_family_no_location_generic_spatial(self, mep_type_info, spatial_element, show_visual=False):
        # type: (dict, BaseSpatial, bool) -> tuple[bool, float]
        """Scenario 8: Generic spatial vs FamilyInstance no location."""
        try:
            is_match, distance = self.geometry_detector.check_points(self.get_detection_points(mep_type_info), spatial_element, show_visual)
            if is_match:
                return True, distance
            return False, 0
        except:  # noqa
            return False, 0
            
    def _scenario_9_curve_element_generic_spatial(self, mep_element, mep_type_info, spatial_element, show_visual=False):
        # type: (BaseMep, dict, BaseSpatial, bool) -> tuple[bool, float]
        """Scenario 9: Generic spatial vs Curve Element."""
        try:
            # Priority 1: Check room calculation points first (most accurate)
            is_match, distance = self.geometry_detector.check_points(self.get_detection_points(mep_type_info), spatial_element, show_visual)
            if is_match:
                return True, distance
            
            # Priority 2: Use curve sampling as fallback
            if not mep_type_info['has_location_curve'] or not mep_type_info['curve']:
                return False, 0
                
            curve = mep_type_info['curve']
            sample_points = sample_curve_points(curve, 3)
            
            # Priority 3: Check curve above
            is_match, distance = self.geometry_detector.check_curve_above(curve, spatial_element)
            if is_match:
                return True, distance
                        
            # Priority 4: Check intersection
            is_match, distance = self.geometry_detector.check_intersection(mep_element, spatial_element, mep_type_info, show_visual)
            if is_match:
                return True, distance
                    
            # Priority 5: Direct sampling (fallback method)
            for point in sample_points:
                is_match, distance = self.geometry_detector.check_points([point], spatial_element, show_visual)
                if is_match:
                    return True, distance

            return False, 0
        except:  # noqa
            return False, 0
            
    def _scenario_10_directshape_generic_spatial(self, mep_type_info, spatial_element, show_visual=False):
        # type: (dict, BaseSpatial, bool) -> tuple[bool, float]
        """Scenario 10: Generic spatial vs DirectShape."""
        try:
            is_match, distance = self.geometry_detector.check_points(self.get_detection_points(mep_type_info), spatial_element, show_visual)
            if is_match:
                return True, distance
            return False, 0
        except:  # noqa
            return False, 0