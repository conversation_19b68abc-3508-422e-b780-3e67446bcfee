{"Uuid": "438dde9e-2327-41a6-8a4f-c0e1578bf7f2", "IsCustomNode": false, "Description": "", "Name": "taboozoneqa", "ElementResolver": {"ResolutionMap": {}}, "Inputs": [], "Outputs": [], "Nodes": [{"ConcreteType": "CoreNodeModels.Input.BoolSelector, CoreNodeModels", "NodeType": "BooleanInputNode", "InputValue": false, "Id": "6a416ca0432b4221b0f337b9b2076fb9", "Inputs": [], "Outputs": [{"Id": "c0f17bc8a66d4f21bc52b2c0b47fd7ed", "Name": "", "Description": "Boolean", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Selection between a true and false."}, {"ConcreteType": "PythonNodeModels.PythonNode, PythonNodeModels", "NodeType": "PythonScriptNode", "Code": "import clr\r\n\r\nclr.AddReference('DynamoServices')  # noqa\r\nfrom Dynamo.Events import ExecutionEvents  # noqa\r\nfrom System import IO, Environment\r\n\r\n\r\ndef internal_lib(start_path):\r\n    lib_folders = []\r\n    current_path = start_path\r\n    while current_path:\r\n        _lib_path = IO.Path.Combine(current_path, \"lib\")\r\n        if IO.Directory.Exists(_lib_path):\r\n            lib_folders.append(_lib_path)\r\n        if \".extension\" in IO.Directory.GetDirectories(current_path):\r\n            break\r\n        current_path = IO.Path.GetDirectoryName(current_path)\r\n    return lib_folders\r\n\r\n\r\n# Paths\r\ncurrent_dynamo_path = ExecutionEvents.ActiveSession.CurrentWorkspacePath\r\ncurrent_workspace = IO.Path.GetDirectoryName(current_dynamo_path)\r\nappdata = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData)\r\npydcmvn_lib = IO.Path.Combine(appdata, \"pyRevit\", \"Extensions\", \"pyDCMvn.lib\")\r\npyrevit_site_packages = IO.Path.Combine(appdata, \"pyRevit-Master\", \"site-packages\")\r\n\r\nlib_paths = internal_lib(current_workspace)\r\nlib_paths.append(pydcmvn_lib) if IO.Directory.Exists(pydcmvn_lib) else None\r\nlib_paths.append(pyrevit_site_packages) if IO.Directory.Exists(pyrevit_site_packages) else None\r\n\r\n# Python file end with script.py\r\npython_file = None\r\nfor file_ in IO.Directory.GetFiles(current_workspace):\r\n    if file_.endswith(\"script.py\"):\r\n        python_file = file_\r\n        break\r\n\r\n# Python File Content\r\npython_content = IO.File.ReadAllText(python_file)\r\n\r\nOUT = python_content, lib_paths, current_workspace\r\n", "Engine": "CPython3", "EngineName": "CPython3", "VariableInputPorts": true, "Id": "5b2064176ce14a07b09f697218bafd51", "Inputs": [{"Id": "d8775e77b7e74a5888ef408eb0259564", "Name": "IN[0]", "Description": "Input #0", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "9b80a062f3d74a15a67d0a23279513c3", "Name": "OUT", "Description": "Result of the python script", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Runs an embedded Python script."}, {"ConcreteType": "Dynamo.Graph.Nodes.CodeBlockNodeModel, DynamoCore", "NodeType": "CodeBlockNode", "Code": "input = t4;\nscript_content = input[0];\nlib_paths = input[1];\ncurrent_workspace = input[2];", "Id": "9f22c350a52e48099c878295485072f7", "Inputs": [{"Id": "10f12979633843a48936f3b3ad54fc01", "Name": "t4", "Description": "t4", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "4d5e5f611c3b420ba0b7b7adb18be08e", "Name": "", "Description": "input", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "30b9b7cf9378439cb7e76420ae94e398", "Name": "", "Description": "script_content", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "d29d49e9e7f2415ca6ae267acb0df4b7", "Name": "", "Description": "lib_paths", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "4804e976abd2437988821e20b9cf9212", "Name": "", "Description": "current_workspace", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Allows for DesignScript code to be authored directly"}, {"ConcreteType": "PythonNodeModels.PythonStringNode, PythonNodeModels", "Engine": "IronPython2", "EngineName": "IronPython2", "VariableInputPorts": true, "NodeType": "ExtensionNode", "Id": "4750cfaa0a6c4cb5a6794fb51ec82251", "Inputs": [{"Id": "96da8239276142b690d3355e3967002a", "Name": "script", "Description": "Python script to run.", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "35737381e74f4ef9b18dfde9e9df0a29", "Name": "IN[0]", "Description": "Input #0", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "b57886be34da4c21bba6be6a5e788e3e", "Name": "IN[1]", "Description": "Input #1", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "e4332f46b0654f46b31029baf4ac5de5", "Name": "OUT", "Description": "Result of the python script", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Runs a Python script from a string."}, {"ConcreteType": "Dynamo.Graph.Nodes.CodeBlockNodeModel, DynamoCore", "NodeType": "CodeBlockNode", "Code": "result = a[521];\narc_ob = result[2];\nmep_mass_ob = result[3];\nvol = [2];", "Id": "a14c161851f04112b3c2b4d470b9cc8a", "Inputs": [{"Id": "5d5c9d1c947e4945bffb1878a49460b4", "Name": "a", "Description": "a", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "3f2f3468cba2487cbb46d918f5564174", "Name": "", "Description": "result", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "b87f9a76dbc346b189bae86fcf27041b", "Name": "", "Description": "arc_ob", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "eb2e879ca6d64f5ea7c3267e93bc5e7d", "Name": "", "Description": "mep_mass_ob", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "8992ee28da374dfd8d6c8300030a8113", "Name": "", "Description": "vol", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Allows for DesignScript code to be authored directly"}, {"ConcreteType": "Dynamo.Graph.Nodes.CodeBlockNodeModel, DynamoCore", "NodeType": "CodeBlockNode", "Code": "a;", "Id": "9d829c1029f0471fafac28b7b75dc6c2", "Inputs": [{"Id": "496008365fb24560bf92b4cb590737b0", "Name": "a", "Description": "a", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "c2260106e9654037b358d3dda0a1a80a", "Name": "", "Description": "Value of expression at line 1", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Allows for DesignScript code to be authored directly"}, {"ConcreteType": "Dynamo.Graph.Nodes.CodeBlockNodeModel, DynamoCore", "NodeType": "CodeBlockNode", "Code": "a;", "Id": "bbd3ed7a08474626bdd2b2711a6fec13", "Inputs": [{"Id": "5b21a7e2c27041b89c91958b7a79e8d0", "Name": "a", "Description": "a", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "3bf481c3a009464c8548018dba69615d", "Name": "", "Description": "Value of expression at line 1", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Allows for DesignScript code to be authored directly"}, {"ConcreteType": "Dynamo.Graph.Nodes.ZeroTouch.DSFunction, DynamoCore", "NodeType": "FunctionNode", "FunctionSignature": "<EMAIL>", "Id": "96b65b7c85a54ac2afe0f9cf6f6bcdc4", "Inputs": [{"Id": "725704c883594557a13fea47a326b65c", "Name": "geometry", "Description": "Autodesk.DesignScript.Geometry.Geometry", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "853033d2e96946f3b80f96c58a6e076f", "Name": "other", "Description": "Geometry", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "c5d0d7fbc46148a79b337253999db325", "Name": "Geometry[]", "Description": "Geometry[]", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Auto", "Description": "Get the intersection Geometry for this object and another\n\nGeometry.Intersect (other: Geometry): Geometry[]"}, {"ConcreteType": "Dynamo.Nodes.DSModelElementSelection, DSRevitNodesUI", "NodeType": "ExtensionNode", "InstanceId": ["825635f8-b6be-4e77-adf8-fc2e46082133-000a4393"], "Id": "3379b1916a744ddda2e46f2952fa2bf3", "Inputs": [], "Outputs": [{"Id": "fd9733ee086b4e9eaf98b619130ac18d", "Name": "Element", "Description": "The selected elements.", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled"}, {"ConcreteType": "Dynamo.Graph.Nodes.CustomNodes.Function, DynamoCore", "FunctionSignature": "12050b47-2098-4101-af6d-79523545a310", "FunctionType": "Graph", "NodeType": "FunctionNode", "Id": "58d15517c1ed407296eade4253451254", "Inputs": [{"Id": "cd1129ec4c944e1c92489d5f8b2f072e", "Name": "refresh", "Description": "bool\nDefault value : false", "UsingDefaultValue": true, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "4b10c94402004615a83e85cae73908f2", "Name": "Element", "Description": "return value", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "d00501032adf4ae0b1e83ab94688232b", "Name": "RevitLinkInstance", "Description": "return value", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "123c61229cc14d86af39faa06a2f519e", "Name": "Transform", "Description": "return value", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Auto", "Description": "Selects the linked elements."}, {"ConcreteType": "Dynamo.Graph.Nodes.ZeroTouch.DSFunction, DynamoCore", "NodeType": "FunctionNode", "FunctionSignature": "Revit.Elements.Element.Solids", "Id": "ae43ec9cfec34fb799390c6b6ef94916", "Inputs": [{"Id": "acb9bec69f0a4870b9723855e6ca207a", "Name": "element", "Description": "Revit.Elements.Element", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "a01335fc658e4ca0b1762920284b7f0b", "Name": "Solid[]", "Description": "Solid[]", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Auto", "Description": "The Solids in this Element\n\nElement.Solids: Solid[]"}, {"ConcreteType": "Dynamo.Graph.Nodes.ZeroTouch.DSFunction, DynamoCore", "NodeType": "FunctionNode", "FunctionSignature": "Revit.Elements.Element.Solids", "Id": "fffb6a18bd0347c09528463e3fbd582d", "Inputs": [{"Id": "c4170411442148258561f8ee110d2bc0", "Name": "element", "Description": "Revit.Elements.Element", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "01281aa3b5824913b8f4c74d367219b7", "Name": "Solid[]", "Description": "Solid[]", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Auto", "Description": "The Solids in this Element\n\nElement.Solids: Solid[]"}, {"ConcreteType": "Dynamo.Graph.Nodes.ZeroTouch.DSFunction, DynamoCore", "NodeType": "FunctionNode", "FunctionSignature": "<EMAIL>", "Id": "c8606ac41a074af1b9c8b4e8000a086c", "Inputs": [{"Id": "f462955520ac4b8cba80a41b56f7152f", "Name": "geometry", "Description": "Autodesk.DesignScript.Geometry.Geometry", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "e9abbef80ea54b9f8f70ae6396be898d", "Name": "other", "Description": "Geometry", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "81c7fb46d2354b5991395d391d158ea4", "Name": "Geometry[]", "Description": "Geometry[]", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Auto", "Description": "Get the intersection Geometry for this object and another\n\nGeometry.Intersect (other: Geometry): Geometry[]"}, {"ConcreteType": "Dynamo.Graph.Nodes.ZeroTouch.DSFunction, DynamoCore", "NodeType": "FunctionNode", "FunctionSignature": "<EMAIL>[]", "Id": "6f1a99bfc26f4b88a8a281fe7cb2acdc", "Inputs": [{"Id": "ccd6162540644992967da7c4aa532402", "Name": "solids", "Description": "A collection of solids\n\nSolid[]", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "9fa0abbe060448b8a83e0f4600bc1b4e", "Name": "Solid", "Description": "Solid", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Auto", "Description": "Union a collection of solids into one solid\n\nSolid.ByUnion (solids: Solid[]): Solid"}, {"ConcreteType": "Dynamo.Graph.Nodes.CodeBlockNodeModel, DynamoCore", "NodeType": "CodeBlockNode", "Code": "a[0];", "Id": "daa5d2122f1e4fb1b1bdb855bea65e26", "Inputs": [{"Id": "c4a97e957158401280af665d13777c42", "Name": "a", "Description": "a", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "d4c88134d0b64431bb0898cd27f1a5db", "Name": "", "Description": "Value of expression at line 1", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Allows for DesignScript code to be authored directly"}, {"ConcreteType": "Dynamo.Graph.Nodes.ZeroTouch.DSFunction, DynamoCore", "NodeType": "FunctionNode", "FunctionSignature": "<EMAIL>", "Id": "a4a8298f2db74c3dadfe2458b42742f2", "Inputs": [{"Id": "77e1ef24754949d9a7d057f34946d981", "Name": "geometry", "Description": "Autodesk.DesignScript.Geometry.Geometry", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}, {"Id": "4763aed1860d4ff9a201fa67991e8f49", "Name": "cs", "Description": "CoordinateSystem", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "4437356bfada4c38a0755d888ef6aa37", "Name": "Geometry", "Description": "Transformed geometry", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Auto", "Description": "Transforms geometry by the given transform of CoordinateSystem\n\nGeometry.Transform (cs: CoordinateSystem): Geometry"}, {"ConcreteType": "Dynamo.Graph.Nodes.ZeroTouch.DSFunction, DynamoCore", "NodeType": "FunctionNode", "FunctionSignature": "Autodesk.DesignScript.Geometry.Solid.Volume", "Id": "ebead10aba3c4e899bf4c6d27fc76c2b", "Inputs": [{"Id": "d34a92f0d6ed49d4add472957a4300ba", "Name": "solid", "Description": "Autodesk.DesignScript.Geometry.Solid", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "96a663d4d344462291f23692d02de0fa", "Name": "double", "Description": "double", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Auto", "Description": "Returns the total volume of the Solid\n\nSolid.Volume: double"}, {"ConcreteType": "UnitsUI.DynamoUnitConvert, UnitsUI", "MeasurementType": "autodesk.unit.quantity:volume-1.0.1", "FromConversion": "autodesk.unit.unit:cubicMillimeters-1.0.1", "ToConversion": "autodesk.unit.unit:cubicFeet-1.0.1", "NodeType": "ExtensionNode", "Id": "8b96e33bfe4f433eae13d3be2e1d7ead", "Inputs": [{"Id": "f7c6719b4ef846988727563469b98ef6", "Name": "", "Description": "A value to convert", "UsingDefaultValue": true, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "a5cdef7c7c4445c08eb5d537381e1388", "Name": "", "Description": "Converted value", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Convert a value between selected Unit systems."}, {"ConcreteType": "Dynamo.Graph.Nodes.ZeroTouch.DSFunction, DynamoCore", "NodeType": "FunctionNode", "FunctionSignature": "Autodesk.DesignScript.Geometry.Solid.Volume", "Id": "8d1400c8b5fa4aa881e5fb04f72b03a1", "Inputs": [{"Id": "a87593299e0742ab84cb659c2a0f4618", "Name": "solid", "Description": "Autodesk.DesignScript.Geometry.Solid", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "ceeafc97f8c64aeeb5b3560837f26434", "Name": "double", "Description": "double", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Auto", "Description": "Returns the total volume of the Solid\n\nSolid.Volume: double"}, {"ConcreteType": "Dynamo.Graph.Nodes.ZeroTouch.DSFunction, DynamoCore", "NodeType": "FunctionNode", "FunctionSignature": "Autodesk.DesignScript.Geometry.Solid.Volume", "Id": "47f26c4c541b4b75b9399d339e922e19", "Inputs": [{"Id": "1079edd202bb48a2876bc0bc9859b267", "Name": "solid", "Description": "Autodesk.DesignScript.Geometry.Solid", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "ef0fa9f96d0c45dab89910cc8f495ac4", "Name": "double", "Description": "double", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Auto", "Description": "Returns the total volume of the Solid\n\nSolid.Volume: double"}, {"ConcreteType": "UnitsUI.DynamoUnitConvert, UnitsUI", "MeasurementType": "autodesk.unit.quantity:volume-1.0.1", "FromConversion": "autodesk.unit.unit:cubicMillimeters-1.0.1", "ToConversion": "autodesk.unit.unit:cubicFeet-1.0.1", "NodeType": "ExtensionNode", "Id": "c2b84ca8e5dc4f3fb963f85be5f73bd6", "Inputs": [{"Id": "5dce04fd731249bcb259f5ab87625464", "Name": "", "Description": "A value to convert", "UsingDefaultValue": true, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "cb8cb874c87848f3aae5cce252422e74", "Name": "", "Description": "Converted value", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Convert a value between selected Unit systems."}, {"ConcreteType": "UnitsUI.DynamoUnitConvert, UnitsUI", "MeasurementType": "autodesk.unit.quantity:volume-1.0.1", "FromConversion": "autodesk.unit.unit:cubicMillimeters-1.0.1", "ToConversion": "autodesk.unit.unit:cubicFeet-1.0.1", "NodeType": "ExtensionNode", "Id": "ff4f717bff534d529dcafe2bbc955feb", "Inputs": [{"Id": "3ace6e7c1bf64bf8be9d2bc02c6fa108", "Name": "", "Description": "A value to convert", "UsingDefaultValue": true, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "2f5f440ac30c4aedb2206837935bc913", "Name": "", "Description": "Converted value", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Convert a value between selected Unit systems."}, {"ConcreteType": "PythonNodeModels.PythonNode, PythonNodeModels", "NodeType": "PythonScriptNode", "Code": "# Load the Python Standard and DesignScript Libraries\r\nimport sys\r\nimport clr\r\nclr.AddReference('ProtoGeometry')\r\nfrom Autodesk.DesignScript.Geometry import *\r\n\r\n# The inputs to this n0ode will be stored as a list in the IN variables.\r\ndataEnteringNode = IN[0]\r\n# Place your code below this line\r\nele = [i for i in dataEnteringNode if isinstance(i, list) and str(i[1].Id) == \"496066\"]\r\n# Assign your output to the OUT variable.\r\nOUT = ele", "Engine": "CPython3", "EngineName": "CPython3", "VariableInputPorts": true, "Id": "6257664eaa134930adad288ac471e413", "Inputs": [{"Id": "fe573649d6c6449a8dc7ba33de1f40b0", "Name": "IN[0]", "Description": "Input #0", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "c892d40d707b483cab5e3bc180d983ad", "Name": "OUT", "Description": "Result of the python script", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Runs an embedded Python script."}, {"ConcreteType": "CoreNodeModels.Input.BoolSelector, CoreNodeModels", "NodeType": "BooleanInputNode", "InputValue": false, "Id": "cfdb85b0f378498faef2ca8892e8c5f4", "Inputs": [], "Outputs": [{"Id": "a94a502f562a45cdba22abd26f4f34a0", "Name": "", "Description": "Boolean", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Selection between a true and false."}, {"ConcreteType": "Dynamo.Graph.Nodes.CodeBlockNodeModel, DynamoCore", "NodeType": "CodeBlockNode", "Code": "a[0];", "Id": "fcdc051a31f9489385321579bd312ea6", "Inputs": [{"Id": "818bcd358af54691a11ef73e542ac231", "Name": "a", "Description": "a", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "280136166ffc44c792b0565c377fa99b", "Name": "", "Description": "Value of expression at line 1", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Allows for DesignScript code to be authored directly"}, {"ConcreteType": "Dynamo.Graph.Nodes.CodeBlockNodeModel, DynamoCore", "NodeType": "CodeBlockNode", "Code": "a[1];", "Id": "03c0a00648b645b1945f7c845b3b8e84", "Inputs": [{"Id": "1a98811d20b24167a9783fade67c4d64", "Name": "a", "Description": "a", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Outputs": [{"Id": "c99fc250d1624a0d825bcbab881cae4c", "Name": "", "Description": "Value of expression at line 1", "UsingDefaultValue": false, "Level": 2, "UseLevels": false, "KeepListStructure": false}], "Replication": "Disabled", "Description": "Allows for DesignScript code to be authored directly"}], "Connectors": [{"Start": "c0f17bc8a66d4f21bc52b2c0b47fd7ed", "End": "d8775e77b7e74a5888ef408eb0259564", "Id": "18e1531ca04044179491fb3360cd1e56", "IsHidden": "False"}, {"Start": "9b80a062f3d74a15a67d0a23279513c3", "End": "10f12979633843a48936f3b3ad54fc01", "Id": "530ba9910a6946d8b8b4a308d9d8e1c0", "IsHidden": "False"}, {"Start": "30b9b7cf9378439cb7e76420ae94e398", "End": "96da8239276142b690d3355e3967002a", "Id": "54e602fdf29646578d41beedc15d8eb4", "IsHidden": "False"}, {"Start": "d29d49e9e7f2415ca6ae267acb0df4b7", "End": "35737381e74f4ef9b18dfde9e9df0a29", "Id": "e14612b00d30437b9a342021339b2506", "IsHidden": "False"}, {"Start": "4804e976abd2437988821e20b9cf9212", "End": "b57886be34da4c21bba6be6a5e788e3e", "Id": "a8e3903408c245d090a1ee5e8be9a5bf", "IsHidden": "False"}, {"Start": "e4332f46b0654f46b31029baf4ac5de5", "End": "fe573649d6c6449a8dc7ba33de1f40b0", "Id": "86e4a2ec96ab49d2aa8dfa970776c67c", "IsHidden": "False"}, {"Start": "e4332f46b0654f46b31029baf4ac5de5", "End": "5d5c9d1c947e4945bffb1878a49460b4", "Id": "4db1ab09bb194449b87c96ccbb9fabef", "IsHidden": "False"}, {"Start": "e4332f46b0654f46b31029baf4ac5de5", "End": "818bcd358af54691a11ef73e542ac231", "Id": "b02264e9aff94979a27541e81ed43ca4", "IsHidden": "False"}, {"Start": "e4332f46b0654f46b31029baf4ac5de5", "End": "1a98811d20b24167a9783fade67c4d64", "Id": "e1b17d67b95c4dc0b5cf594e08124ac1", "IsHidden": "False"}, {"Start": "b87f9a76dbc346b189bae86fcf27041b", "End": "496008365fb24560bf92b4cb590737b0", "Id": "927ccb7234354ccd8ae0132345842610", "IsHidden": "False"}, {"Start": "eb2e879ca6d64f5ea7c3267e93bc5e7d", "End": "5b21a7e2c27041b89c91958b7a79e8d0", "Id": "09941a8cd90443ddb4a1d11f97a6792b", "IsHidden": "False"}, {"Start": "c2260106e9654037b358d3dda0a1a80a", "End": "725704c883594557a13fea47a326b65c", "Id": "f7b22802d7314cf8bf3fd70d5e6b7bef", "IsHidden": "False"}, {"Start": "3bf481c3a009464c8548018dba69615d", "End": "853033d2e96946f3b80f96c58a6e076f", "Id": "527f2f0c1d124b38b39c1b4ba59bfe25", "IsHidden": "False"}, {"Start": "fd9733ee086b4e9eaf98b619130ac18d", "End": "acb9bec69f0a4870b9723855e6ca207a", "Id": "947685fcbdf044a39ab1112393b6a7e4", "IsHidden": "False"}, {"Start": "4b10c94402004615a83e85cae73908f2", "End": "c4170411442148258561f8ee110d2bc0", "Id": "b147744ae34e46909117156c31386241", "IsHidden": "False"}, {"Start": "123c61229cc14d86af39faa06a2f519e", "End": "4763aed1860d4ff9a201fa67991e8f49", "Id": "a29e78bf445641fa83bbda452bb0e39e", "IsHidden": "False"}, {"Start": "a01335fc658e4ca0b1762920284b7f0b", "End": "ccd6162540644992967da7c4aa532402", "Id": "d7a7f16e6eb248d39ff2baca365bff5a", "IsHidden": "False"}, {"Start": "01281aa3b5824913b8f4c74d367219b7", "End": "c4a97e957158401280af665d13777c42", "Id": "9514af1f8e724a569fe5f80cddb3d04c", "IsHidden": "False"}, {"Start": "81c7fb46d2354b5991395d391d158ea4", "End": "d34a92f0d6ed49d4add472957a4300ba", "Id": "f486d238a1c94c0d87c1782e6bfff451", "IsHidden": "False"}, {"Start": "9fa0abbe060448b8a83e0f4600bc1b4e", "End": "f462955520ac4b8cba80a41b56f7152f", "Id": "1b9d065d5c40419b993b099deb03f3b9", "IsHidden": "False"}, {"Start": "9fa0abbe060448b8a83e0f4600bc1b4e", "End": "a87593299e0742ab84cb659c2a0f4618", "Id": "622719b39fa74ac89acb488f363c606b", "IsHidden": "False"}, {"Start": "d4c88134d0b64431bb0898cd27f1a5db", "End": "77e1ef24754949d9a7d057f34946d981", "Id": "38badc56556149369e8b087db96675f5", "IsHidden": "False"}, {"Start": "4437356bfada4c38a0755d888ef6aa37", "End": "e9abbef80ea54b9f8f70ae6396be898d", "Id": "413a670bc46b4bde962b78a0829923ca", "IsHidden": "False"}, {"Start": "4437356bfada4c38a0755d888ef6aa37", "End": "1079edd202bb48a2876bc0bc9859b267", "Id": "001812e7dc204f438953e68215cf22dc", "IsHidden": "False"}, {"Start": "96a663d4d344462291f23692d02de0fa", "End": "f7c6719b4ef846988727563469b98ef6", "Id": "785f43cd94db49358f484c6731bcff7c", "IsHidden": "False"}, {"Start": "ceeafc97f8c64aeeb5b3560837f26434", "End": "5dce04fd731249bcb259f5ab87625464", "Id": "de6b6b75ef3f4a83897400de610416fb", "IsHidden": "False"}, {"Start": "ef0fa9f96d0c45dab89910cc8f495ac4", "End": "3ace6e7c1bf64bf8be9d2bc02c6fa108", "Id": "8b642c2c514a4b7fb675d76b6dc66fe8", "IsHidden": "False"}, {"Start": "a94a502f562a45cdba22abd26f4f34a0", "End": "cd1129ec4c944e1c92489d5f8b2f072e", "Id": "172bbcccba12492f9f227b1906eee820", "IsHidden": "False"}], "Dependencies": ["12050b47-2098-4101-af6d-79523545a310"], "NodeLibraryDependencies": [{"Name": "<PERSON><PERSON>", "Version": "2024.5.29", "ReferenceType": "Package", "Nodes": ["58d15517c1ed407296eade4253451254"]}], "Thumbnail": "", "GraphDocumentationURL": null, "ExtensionWorkspaceData": [{"ExtensionGuid": "28992e1d-abb9-417f-8b1b-05e053bee670", "Name": "Properties", "Version": "2.12", "Data": {}}, {"ExtensionGuid": "DFBD9CC0-DB40-457A-939E-8C8555555A9D", "Name": "Generative Design", "Version": "1.10", "Data": {}}], "Author": "", "Linting": {"activeLinter": "None", "activeLinterId": "7b75fb44-43fd-4631-a878-29f4d5d8399a", "warningCount": 0, "errorCount": 0}, "Bindings": [], "View": {"Dynamo": {"ScaleFactor": 1.0, "HasRunWithoutCrash": true, "IsVisibleInDynamoLibrary": true, "Version": "2.16.4.10216", "RunType": "Automatic", "RunPeriod": "1000"}, "Camera": {"Name": "Background Preview", "EyeX": 120736.1171875, "EyeY": 7426.0859375, "EyeZ": -38104.58203125, "LookX": -306.1328125, "LookY": -8315.7626953125, "LookZ": 11.125, "UpX": -0.9673755168914795, "UpY": 0.2503550350666046, "UpZ": 0.03882136195898056}, "ConnectorPins": [], "NodeViews": [{"Id": "6a416ca0432b4221b0f337b9b2076fb9", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Boolean", "ShowGeometry": true, "Excluded": false, "X": 1008.3608565302607, "Y": 662.9782376748119}, {"Id": "5b2064176ce14a07b09f697218bafd51", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Content", "ShowGeometry": true, "Excluded": false, "X": 683.456320368086, "Y": 805.3997783044272}, {"Id": "9f22c350a52e48099c878295485072f7", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Code Block", "ShowGeometry": true, "Excluded": false, "X": 901.1706039023602, "Y": 805.6798003417716}, {"Id": "4750cfaa0a6c4cb5a6794fb51ec82251", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Python Script From String", "ShowGeometry": true, "Excluded": false, "X": 1275.4902529072726, "Y": 804.6698570128727}, {"Id": "a14c161851f04112b3c2b4d470b9cc8a", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Code Block", "ShowGeometry": false, "Excluded": false, "X": 1574.5396949266851, "Y": 804.9743756909985}, {"Id": "9d829c1029f0471fafac28b7b75dc6c2", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Code Block", "ShowGeometry": true, "Excluded": false, "X": 1894.0439871590377, "Y": 781.0547333606106}, {"Id": "bbd3ed7a08474626bdd2b2711a6fec13", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Code Block", "ShowGeometry": true, "Excluded": false, "X": 1896.1521588906676, "Y": 902.3332577751825}, {"Id": "96b65b7c85a54ac2afe0f9cf6f6bcdc4", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Geometry.Intersect", "ShowGeometry": true, "Excluded": false, "X": 2085.3194659166115, "Y": 819.9241025167694}, {"Id": "3379b1916a744ddda2e46f2952fa2bf3", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Select Model Element", "ShowGeometry": true, "Excluded": false, "X": 1290.2380526094178, "Y": 1384.4689378195321}, {"Id": "58d15517c1ed407296eade4253451254", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Select Linked Element", "ShowGeometry": true, "Excluded": false, "X": 1287.4747598690199, "Y": 1553.029794983814}, {"Id": "ae43ec9cfec34fb799390c6b6ef94916", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Element.Solids", "ShowGeometry": true, "Excluded": false, "X": 1624.7268074089543, "Y": 1338.4285717827609}, {"Id": "fffb6a18bd0347c09528463e3fbd582d", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Element.Solids", "ShowGeometry": false, "Excluded": false, "X": 1596.693959165107, "Y": 1483.814327056824}, {"Id": "c8606ac41a074af1b9c8b4e8000a086c", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Geometry.Intersect", "ShowGeometry": true, "Excluded": false, "X": 2063.5380852915864, "Y": 1512.1602130637038}, {"Id": "6f1a99bfc26f4b88a8a281fe7cb2acdc", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Solid.ByUnion", "ShowGeometry": true, "Excluded": false, "X": 1913.1253457966611, "Y": 1312.717784193499}, {"Id": "daa5d2122f1e4fb1b1bdb855bea65e26", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Code Block", "ShowGeometry": false, "Excluded": false, "X": 1785.1115207232217, "Y": 1642.0212163943042}, {"Id": "a4a8298f2db74c3dadfe2458b42742f2", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Geometry.Transform", "ShowGeometry": true, "Excluded": false, "X": 1852.020563596857, "Y": 1787.4308527084304}, {"Id": "ebead10aba3c4e899bf4c6d27fc76c2b", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Solid.Volume", "ShowGeometry": true, "Excluded": false, "X": 2489.9675613073696, "Y": 1607.072210135484}, {"Id": "8b96e33bfe4f433eae13d3be2e1d7ead", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Convert By Units", "ShowGeometry": true, "Excluded": false, "X": 2493.166915741617, "Y": 1814.270988651846}, {"Id": "8d1400c8b5fa4aa881e5fb04f72b03a1", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Solid.Volume", "ShowGeometry": true, "Excluded": false, "X": 2420.649872850556, "Y": 1281.6521195302064}, {"Id": "47f26c4c541b4b75b9399d339e922e19", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Solid.Volume", "ShowGeometry": true, "Excluded": false, "X": 2431.344089102742, "Y": 1439.5765752651205}, {"Id": "c2b84ca8e5dc4f3fb963f85be5f73bd6", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Convert By Units", "ShowGeometry": true, "Excluded": false, "X": 2740.8211758713683, "Y": 1229.232663997505}, {"Id": "ff4f717bff534d529dcafe2bbc955feb", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Convert By Units", "ShowGeometry": true, "Excluded": false, "X": 2779.105892413166, "Y": 1449.3697841128399}, {"Id": "6257664eaa134930adad288ac471e413", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Python Script", "ShowGeometry": false, "Excluded": true, "X": 1597.4093793101133, "Y": 1046.0460553603862}, {"Id": "cfdb85b0f378498faef2ca8892e8c5f4", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Boolean", "ShowGeometry": true, "Excluded": true, "X": 931.2913108590462, "Y": 1578.1084815085765}, {"Id": "fcdc051a31f9489385321579bd312ea6", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Code Block", "ShowGeometry": true, "Excluded": false, "X": 1530.7541187581933, "Y": 401.81013386962144}, {"Id": "03c0a00648b645b1945f7c845b3b8e84", "IsSetAsInput": false, "IsSetAsOutput": false, "Name": "Code Block", "ShowGeometry": true, "Excluded": false, "X": 1542.476216561012, "Y": 552.9397670472633}], "Annotations": [], "X": -287.88277841783474, "Y": -79.07886784539903, "Zoom": 0.6241273445573919}}