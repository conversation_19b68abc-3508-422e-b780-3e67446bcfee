---
description: Service layer patterns for DCMvn architecture
globs: ["**/services/*.py"]
alwaysApply: true
---

# Service Layer Patterns

## Service Responsibilities
- Handle business logic and data processing
- Manage Revit API operations and filtering
- Implement caching for expensive operations
- Provide clean interfaces to ViewModels
- Handle error management and logging

## Common Service Patterns
- CollectorService: Element collection and filtering
- DetectionService: Spatial relationship detection  
- ReportService: Data processing and output generation
- GeometryService: Complex geometry operations
- ValidationService: Document and element validation

## Service Implementation Guidelines
- Initialize with document reference in constructor
- Cache expensive FilteredElementCollector results
- Use DCMvn.core.framework.List for return types
- Implement proper error handling with Debug.WriteLine
- Expose clean, testable public methods
- Keep services focused on single responsibility

@collector-service-pattern.py