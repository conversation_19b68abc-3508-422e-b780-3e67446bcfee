# coding: utf-8
"""
Simple Filter Deletion - Phiên bản đơn giản không dùng WPF
"""

from DCMvn.core import DB, HOST_APP
from DCMvn.forms import wpfforms, alert
from DCMvn.core.framework import Debug

doc = HOST_APP.doc


def get_all_filters():
    """L<PERSON>y tất cả filters trong document"""
    try:
        filters = []
        
        # Lấy View Filters (ParameterFilterElement)
        view_filters = list(DB.FilteredElementCollector(doc).OfClass(DB.ParameterFilterElement))
        for f in view_filters:
            if f and f.Name:
                filters.append((f, f.Name, "View Filter"))
        
        Debug.WriteLine("Found {} view filters".format(len(view_filters)))
        return filters
        
    except Exception as e:
        Debug.WriteLine("Error getting filters: {}".format(str(e)))
        return []


def simple_delete_filters():
    """Phiên bản đơn giản để xóa filters"""
    try:
        # <PERSON><PERSON><PERSON> danh sách filters
        filters = get_all_filters()
        
        if not filters:
            alert("<PERSON>h<PERSON>ng tìm thấy filter nào trong model.")
            return
        
        # T<PERSON><PERSON> danh sách tên để chọn
        filter_names = []
        for filter_elem, name, filter_type in filters:
            filter_names.append("{} ({})".format(name, filter_type))
        
        # Cho phép chọn nhiều filters
        selected = wpfforms.SelectFromList.show(
            filter_names,
            title="Chọn filters để xóa",
            multiselect=True
        )
        
        if not selected:
            return
        
        # Tìm filters được chọn
        selected_filters = []
        for i, (filter_elem, name, filter_type) in enumerate(filters):
            display_name = "{} ({})".format(name, filter_type)
            if display_name in selected:
                selected_filters.append((filter_elem, name))
        
        if not selected_filters:
            return
        
        # Xác nhận xóa
        confirm_msg = "Bạn có chắc chắn muốn xóa {} filter(s)?\n\n".format(len(selected_filters))
        for filter_elem, name in selected_filters[:10]:
            confirm_msg += "- {}\n".format(name)
        if len(selected_filters) > 10:
            confirm_msg += "... và {} filters khác".format(len(selected_filters) - 10)
        
        if not alert(confirm_msg, yes=True, no=True):
            return
        
        # Xóa filters
        with DB.Transaction(doc, "Delete Selected Filters") as trans:
            trans.Start()
            
            deleted_count = 0
            failed_count = 0
            
            for filter_elem, name in selected_filters:
                try:
                    if filter_elem and filter_elem.IsValidObject:
                        doc.Delete(filter_elem.Id)
                        deleted_count += 1
                        Debug.WriteLine("Deleted: {}".format(name))
                    else:
                        failed_count += 1
                except Exception as e:
                    failed_count += 1
                    Debug.WriteLine("Failed to delete {}: {}".format(name, str(e)))
            
            trans.Commit()
            
            # Báo cáo kết quả
            result_msg = "Kết quả:\n"
            result_msg += "- Đã xóa: {} filters\n".format(deleted_count)
            if failed_count > 0:
                result_msg += "- Không thể xóa: {} filters".format(failed_count)
            
            alert(result_msg)
    
    except Exception as e:
        Debug.WriteLine("Error in simple delete: {}".format(str(e)))
        alert("Lỗi: {}".format(str(e)))


if __name__ == "__main__":
    simple_delete_filters()

