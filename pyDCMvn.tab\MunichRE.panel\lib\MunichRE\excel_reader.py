# coding: utf-8
import clr
import os
import System
import xlsxwriter

from DCMvn.coreutils.assemblyhandler import load_miniexcel
from DCMvn.core.framework import IDictionary, IO
from DCMvn.revit.ui import get_mainwindow
from DCMvn.forms import wpfforms
from DCMvn.io import pick_folder
from DCMvn.core import get_output

load_miniexcel()
from MiniExcelLibs import MiniExcel
from MiniExcelLibs.OpenXml import OpenXmlConfiguration
from .constants import *

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

output = get_output()

class IfcExcelReader:
    def __init__(self, excel_path, sheet_name, fiter_class=None):
        self.filter_class = fiter_class
        self.excel_path = excel_path
        self.sheet_name = sheet_name

    @property
    def data(self):
        data = MiniExcel.Query(self.excel_path, useHeaderRow=True, sheetName=self.sheet_name).Cast[  # noqa
            IDictionary[str, object]]()  # type: IDictionary[str, object]  # noqa
        if self.filter_class:
            data = data.Where(lambda x: getattr(x, IFC_ELEMENT) == self.filter_class) # noqa
        return data

    def get_by_guid(self, guid):
        # type: (str) -> object
        return self.data.FirstOrDefault(lambda x: getattr(x, GLOBAL_ID) == guid)


class RegisterExcelReader:
    def __init__(self, excel_path, sheet_name, start_cell):
        # type: (str, str, str) -> RegisterExcelReader
        config = OpenXmlConfiguration()
        config.FillMergedCells = True
        self.__data = (MiniExcel.Query(excel_path, useHeaderRow=True, startCell=start_cell,
                                      sheetName=sheet_name, configuration=config)
                                .Cast[IDictionary[str, object]]())  # type: IDictionary[str, object]  # noqa

    @property
    def data(self):
        return self.__data

    def get_row_value(self, column_name):
        # type: (str) -> object
        return [getattr(x, column_name) for x in self.__data]


def select_ifc_data(class_name=None):
    # type: (str) -> IfcExcelReader

    select_ifc = wpfforms.SelectFromList.show(os.listdir(IFC_EXCEL_FOLER), "Select Ifc Excel Sheet",
                                              filterfunc=lambda x: x.endswith(".xlsx"))
    if not select_ifc:
        wpfforms.alert("Please select an Excel sheet", exitscript=True)
    
    workbook_path = op.join(IFC_EXCEL_FOLER, select_ifc)
    if not IO.File.Exists(workbook_path):
        wpfforms.alert("Selected file does not exist", exitscript=True)
    
    
    sheet_names = MiniExcel.GetSheetNames(workbook_path)
    
    select_sheet = wpfforms.SelectFromList.show(sheet_names, "Select Sheet from Workbook")
    if not select_sheet:
        wpfforms.alert("Please select a sheet", exitscript=True)
    
    if class_name:
        ifc_reader = IfcExcelReader(workbook_path, select_sheet, class_name)
    else:
        ifc_reader = IfcExcelReader(workbook_path, select_sheet)
    
    return ifc_reader


def select_register_data():
    select_excel = wpfforms.SelectFromList.show(MiniExcel.GetSheetNames(REGISTER_EXCEL), "Select Register Sheet")
    if not select_excel:
        wpfforms.alert("Please select a excel file", exitscript=True)

    ifc_reader = RegisterExcelReader(REGISTER_EXCEL, select_excel)

    return ifc_reader


def save_failed_report_to_excel(failed_items, suffix_):
    try:
        if not os.path.exists(IFC_EXCEL_FOLER):
            os.makedirs(IFC_EXCEL_FOLER)
        
        report_foler = pick_folder("Select Folder to save Report", owner=get_mainwindow())
        if not report_foler:
            output.print_md('# Report not saved')
            return
        
        excel_file_name = suffix_ + '_RoutingReport.xlsx'
        file_path = os.path.join(report_foler, excel_file_name)
        workbook = xlsxwriter.Workbook(file_path)
        worksheet = workbook.add_worksheet('Failed_Routing')
        
        worksheet.write(0, 0, 'GlobalID')
        worksheet.write(0, 1, 'IfcElement')
        worksheet.write(0, 2, 'Name')
        worksheet.write(0, 3, 'ErrorType')
        
        for row_idx, item in enumerate(failed_items, start=1):
            worksheet.write(row_idx, 0, item['id'])
            worksheet.write(row_idx, 1, item['IfcElement'])
            worksheet.write(row_idx, 2, item['Name'])
            worksheet.write(row_idx, 3, item['error'])
        
        workbook.close()
        output.print_md('# Report saved successfully: {}'.format(file_path))
    except AttributeError as e:
        output.print_md('# AttributeError: {}'.format(str(e)))
    except Exception as e:
        output.print_md('# Failed to save Report: {}'.format(str(e)))

