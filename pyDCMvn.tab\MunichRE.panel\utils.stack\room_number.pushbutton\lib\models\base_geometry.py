# coding: utf-8
from DCMvn.core import DB
from DCMvn.revit.geometry import GetElementMergedSolid, GetElementMeshes
from .document_wrapper import DocumentWrapper

class BaseGeometry(object):
    def __init__(self, element, document_wrapper=None):
        # type: (DB.Element, DocumentWrapper) -> None
        self._element = element
        self._document_wrapper = document_wrapper
        self._geometry = None  # type: DB.Solid | list[DB.Mesh] | None
        self._bounding_box = None  # type: DB.BoundingBoxXYZ | None

    @property
    def geometry(self):
        # type: () -> DB.Solid | list[DB.Mesh]
        """Get the geometry of the MEP element.

        Returns:
            DB.Solid | list[DB.Mesh]: The solid geometry or meshes if solid not available
        """
        if self._geometry is None:
            try:
                self._geometry = GetElementMeshes(self._element)
                if self._geometry is None or len(self._geometry) == 0:
                    solid = GetElementMergedSolid(self._element)
                    if solid:
                        self._geometry = solid
            except Exception:
                pass
        return self._geometry

    @property
    def bounding_box(self):
        # type: () -> DB.BoundingBoxXYZ
        """Get the bounding box of the spatial element.

        Returns:
            DB.BoundingBoxXYZ: The bounding box or None if not available
        """
        if self._bounding_box is None:
            if hasattr(self.geometry, "__iter__"):
                self._bounding_box = self.meshes_to_bbox(self._geometry)
            elif isinstance(self.geometry, DB.Solid):
                origin_bbox = self.geometry.GetBoundingBox()
                self._bounding_box = self.bbox_to_world(origin_bbox)
            else:
                origin_bbox = self._element.get_BoundingBox(None)  # type: DB.BoundingBoxXYZ
                if self._document_wrapper is not None:
                    self._bounding_box = (
                        origin_bbox
                        if self._document_wrapper.is_host
                        else self._document_wrapper.transform_bounding_box(origin_bbox)
                    )
                else:
                    self._bounding_box = origin_bbox
        return self._bounding_box

    @staticmethod
    def bbox_to_world(bbox):
        # type: (DB.BoundingBoxXYZ) -> DB.BoundingBoxXYZ
        """
        Convert any BoundingBoxXYZ (even rotated) to a world-aligned one.

        Args:
            bbox (DB.BoundingBoxXYZ): The bounding box to convert.
        Returns:
            DB.BoundingBoxXYZ: The world-aligned bounding box.
        """
        t = bbox.Transform  # basis + origin
        loc_min, loc_max = bbox.Min, bbox.Max

        # local-space corners
        pts_loc = [
            DB.XYZ(loc_min.X, loc_min.Y, loc_min.Z),
            DB.XYZ(loc_max.X, loc_min.Y, loc_min.Z),
            DB.XYZ(loc_min.X, loc_max.Y, loc_min.Z),
            DB.XYZ(loc_max.X, loc_max.Y, loc_min.Z),
            DB.XYZ(loc_min.X, loc_min.Y, loc_max.Z),
            DB.XYZ(loc_max.X, loc_min.Y, loc_max.Z),
            DB.XYZ(loc_min.X, loc_max.Y, loc_max.Z),
            DB.XYZ(loc_max.X, loc_max.Y, loc_max.Z),
        ]

        # blast them into world space
        pts_wld = [t.OfPoint(p) for p in pts_loc]

        xs, ys, zs = zip(*[(p.X, p.Y, p.Z) for p in pts_wld])

        out = DB.BoundingBoxXYZ()
        out.Min = DB.XYZ(min(xs), min(ys), min(zs))
        out.Max = DB.XYZ(max(xs), max(ys), max(zs))
        # Transform defaults to Identity → world-aligned
        return out

    @staticmethod
    def meshes_to_bbox(meshes):
        # type: (list[DB.Mesh]) -> DB.BoundingBoxXYZ
        """
        Compute the bounding box of multiple Revit Mesh objects.
        Args:
            meshes (list[DB.Mesh]): The meshes to compute the bounding box of.
        Returns:
            DB.BoundingBoxXYZ: The bounding box of the meshes.
        """
        min_x = min_y = min_z = float("inf")
        max_x = max_y = max_z = float("-inf")

        for mesh in meshes:
            if mesh is None or mesh.Vertices is None:
                continue
            for v in mesh.Vertices:
                x, y, z = v.X, v.Y, v.Z
                if x < min_x:
                    min_x = x
                if y < min_y:
                    min_y = y
                if z < min_z:
                    min_z = z
                if x > max_x:
                    max_x = x
                if y > max_y:
                    max_y = y
                if z > max_z:
                    max_z = z

        bbox = DB.BoundingBoxXYZ()
        bbox.Min = DB.XYZ(min_x, min_y, min_z)
        bbox.Max = DB.XYZ(max_x, max_y, max_z)
        return bbox