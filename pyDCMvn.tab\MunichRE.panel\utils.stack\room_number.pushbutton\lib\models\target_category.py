# coding: utf-8
from DCMvn.forms.mvvm import ViewModelBase
from DCMvn.core import DB

class TargetCategory(ViewModelBase):
    def __init__(self, category, elements=None):
        # type: (DB.Category, list[DB.Element]) -> None
        """
        Initialize TargetCategory with category and elements
        
        Args:
            category: Revit Category object
            elements: List of elements belonging to this category
        """
        ViewModelBase.__init__(self)
        self.category = category
        self.elements = elements or []
        self.__is_selected = False
    
    @property
    def is_selected(self):
        """Get selection status"""
        return self.__is_selected
    
    @is_selected.setter
    def is_selected(self, value):
        """Set selection status and notify of change"""
        if self.__is_selected != value:
            self.__is_selected = value
            self.RaisePropertyChanged("is_selected")
    
    @property
    def name(self):
        """Get the category name"""
        return self.category.Name if self.category else "Unknown"
    
    @property
    def element_count(self):
        """Get the number of elements in this category"""
        return len(self.elements)
    
    @property
    def display_name(self):
        """Get display name with element count"""
        return "{} ({} elements)".format(self.name, self.element_count)
    
    def __str__(self):
        return self.display_name
    
    def __repr__(self):
        return "TargetCategory(name='{}', count={}, selected={})".format(
            self.name, self.element_count, self.is_selected) 