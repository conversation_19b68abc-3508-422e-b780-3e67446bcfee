import clr
from DCMvn.revit.selection import DSelection
from DCMvn.core import DB, UI
from DCMvn.core.framework import System

from ..clashes import MepMassObject, FloorMassObject
from ..utils import get_element_from_ref, get_arc_from_solid, ArcCurve

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

def test_ray_cast(uidoc, e_filter):
    # type: (UI.UIDocument, DB.ElementFilter) -> DB.Element
    ele2 = DSelection(uidoc, is_linked=False).pick()
    
    view = uidoc.Document.ActiveView
    
    mep_mass_obj = MepMassObject(ele2)
    
    intersector = DB.ReferenceIntersector(e_filter, DB.FindReferenceTarget.Face, view)
    intersector.FindReferencesInRevitLinks = True
    
    solid = mep_mass_obj.get_solid()
    ref = intersector.FindNearest(solid.ComputeCentroid(), DB.XYZ(0, 0, 1))
    ele = get_element_from_ref(ref.GetReference(), uidoc.Document)
    print(ele.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString())
    return ele


def test_arc_curve(floor_masses, uidoc=None):
    # type: (list[MepMassObject],UI.UIDocument) -> list[list[tuple[DB.XYZ, DB.Curve, float, DB.Element]]]  # noqa
    # ele2 = DSelection(uidoc, is_linked=False).pick()

    # mass = MepMassObject(ele2)
    out_value = []
    for mass in floor_masses:
        solid = mass.get_solid()

        arcs = get_arc_from_solid(solid)
        print(len(arcs))
        if len(arcs) > 4:
            continue
        out_value.append([(ar.mid_point, ar.arc, ar.radius, mass.to_element()) for ar in arcs])

    return out_value


def test_single_arc_extend(uidoc, element_filter):
    ele = DSelection(uidoc, is_linked=False).pick()
    mass = FloorMassObject(ele)
    mass_solid = mass.get_solid()
    arcs = get_arc_from_solid(mass_solid)

    intersector = DB.ReferenceIntersector(element_filter, DB.FindReferenceTarget.Element, uidoc.Document.ActiveView)
    intersector.FindReferencesInRevitLinks = True

    elements = mass.get_raycast_elements(intersector, arcs)
    for e in elements:
        print(e.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString())
    return elements

