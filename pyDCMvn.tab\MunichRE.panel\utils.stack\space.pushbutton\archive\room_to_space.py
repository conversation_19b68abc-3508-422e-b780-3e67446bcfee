import clr
import System

from DCMvn.core import DB, get_output, HOST_APP
from DCMvn.forms import wpfforms, alert
from DCMvn.core.framework import Dictionary
from pyrevit import script

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

doc = HOST_APP.doc
output = get_output()

ID = "Id"
NAME = "Name"
NUMBER = "Number"
UPPER_LIMIT = "UpperLimit"
LIMIT_OFFSET = "LimitOffset"
BASE_OFFSET = "BaseOffset"
IFC_GUID = "IfcGUID"
LEVEL = "Level"
LOCATION = "Location"
ROOM_NAME = "RoomName"
ROOM_NUMBER = "RoomNumber"
SPACES_CATE = "Spaces"
ROOMS_CATE = "Rooms"


def spatial_element_data(spatial):
    # type: (DB.SpatialElement) -> dict
    spatial_data = {
        ID: spatial.Id,
        NAME: spatial.get_Parameter(DB.BuiltInParameter.ROOM_NAME).AsString(),
        NUMBER: spatial.get_Parameter(DB.BuiltInParameter.ROOM_NUMBER).AsString(),
        UPPER_LIMIT: spatial.get_Parameter(DB.BuiltInParameter.ROOM_UPPER_LEVEL).AsValueString(),
        LIMIT_OFFSET: spatial.get_Parameter(DB.BuiltInParameter.ROOM_UPPER_OFFSET).AsDouble(),
        BASE_OFFSET: spatial.get_Parameter(DB.BuiltInParameter.ROOM_LOWER_OFFSET).AsDouble(),
        LEVEL: spatial.get_Parameter(DB.BuiltInParameter.LEVEL_NAME).AsString(),
        IFC_GUID : spatial.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString(),
        LOCATION: spatial.Location,
    }
    return spatial_data


def create_space(room, level_mapper, link_room_instance):
    # type: (DB.Architecture.Room, dict, DB.RevitLinkInstance) -> DB.Mechanical.Space

    # room data
    room_data = spatial_element_data(room)
    room_upper_limit = level_mapper.get(room_data.get(UPPER_LIMIT))
    room_number = room_data.get(NUMBER)
    room_name = room_data.get(NAME)
    room_ifc = room_data.get(IFC_GUID)
    limit_offset = room_data.get(LIMIT_OFFSET)
    base_offset = room_data.get(BASE_OFFSET)
    level = level_mapper.get(room_data.get(LEVEL))

    # transform link point
    room_location_point_in_link = room_data.get(LOCATION).Point
    room_location_point_in_host = link_room_instance.GetTotalTransform().OfPoint(room_location_point_in_link)

    # create new space
    new_space = doc.Create.NewSpace(level, DB.UV(room_location_point_in_host.X, room_location_point_in_host.Y))
    
    new_space.get_Parameter(DB.BuiltInParameter.IFC_GUID).Set(room_ifc)
    new_space.get_Parameter(DB.BuiltInParameter.ROOM_NUMBER).Set(room_number)
    new_space.get_Parameter(DB.BuiltInParameter.ROOM_NAME).Set(room_name)
    
    new_space.get_Parameter(DB.BuiltInParameter.ROOM_UPPER_LEVEL).Set(room_upper_limit.Id)
    new_space.get_Parameter(DB.BuiltInParameter.ROOM_UPPER_OFFSET).Set(limit_offset)
    new_space.get_Parameter(DB.BuiltInParameter.ROOM_LOWER_OFFSET).Set(base_offset)
    return new_space


def create_space_separator(room_separator, link_instance, level_mapper, list_view_floor_planes):
    # type: (DB.Architecture.RoomSeparator, DB.RevitLinkInstance, dict, list) -> None
    link_doc = link_instance.GetLinkDocument()
    link_level_id = room_separator.LevelId
    link_level = link_doc.GetElement(link_level_id)
    current_level = level_mapper.get(link_level.Name)

    link_transform = link_instance.GetTotalTransform()
    room_separator_curve = room_separator.GeometryCurve

    curve_array = DB.CurveArray()
    curve_array.Append(room_separator_curve.CreateTransformed(link_transform))

    # Create a sketch plane
    view = list_view_floor_planes.First(lambda x: x.Name == current_level.Name)
    sketch_plane = DB.SketchPlane.Create(doc, current_level.Id)
    doc.Create.NewSpaceBoundaryLines(sketch_plane, curve_array, view)


def level_mapper():
    all_levels = DB.FilteredElementCollector(doc).OfClass(DB.Level).ToElements()
    mapper = {i.Name: i for i in all_levels}
    return mapper


def select_link():
    all_link_instances = (DB.FilteredElementCollector(doc).OfClass(DB.RevitLinkInstance)
                            .Where(lambda x: DB.RevitLinkType.IsLoaded(doc, x.GetTypeId())))
    all_link_docs = [x.GetLinkDocument() for x in all_link_instances]

    link_doc = wpfforms.SelectFromList.show(context=all_link_docs,
                                                title="Select the link",
                                                name_attr="Title",
                                                width=300,
                                                height=300,
                                                button_name="Select Link",
                                                show_min_button=False,
                                                show_maxrestore_button=False)
    link_instance = all_link_instances.Where(lambda x: x.GetLinkDocument().Title == link_doc.Title).First()
    return link_doc, link_instance


def set_view_phase(view, phase_id):
    # type: (DB.View, DB.Phase) -> None
    view.get_Parameter(DB.BuiltInParameter.VIEW_PHASE).Set(phase_id)

def phase_collector(link_doc, phase_id):
    # type: (DB.RevitLinkInstance, DB.Phase) -> DB.FilteredElementCollector

    link_rooms = (DB.FilteredElementCollector(link_doc).OfCategory(DB.BuiltInCategory.OST_Rooms)
                      .WhereElementIsNotElementType())
    link_room_separators = (DB.FilteredElementCollector(link_doc).OfCategory(DB.BuiltInCategory.OST_RoomSeparationLines)
                            .WhereElementIsNotElementType())
    if phase_id is None:
        return link_rooms, link_room_separators

    return link_rooms.Where(lambda x: x.get_Parameter(DB.BuiltInParameter.ROOM_PHASE).AsElementId() == phase_id), \
            link_room_separators.Where(lambda x: x.get_Parameter(DB.BuiltInParameter.PHASE_CREATED).AsElementId() == phase_id)

def main():
    active_view = doc.ActiveView
    if not isinstance(active_view, DB.ViewPlan):
        alert("Please run this script in a floor plan view.", warn_icon=True, exitscript=True)
    
        
    select_phase = wpfforms.CommandSwitchWindow.show(context=["Existing", "New", "Both"], 
                                                     title="Select Phase", width=300, height=300)
    if not select_phase:
        return
    
    mapper = level_mapper()
    arc_doc, arc_link_instance = select_link()
    # phase_map = doc.GetElement(arc_link_instance.GetTypeId()).GetPhaseMap()  # type: Dictionary[DB.ElementId, DB.ElementId]

    phases = DB.FilteredElementCollector(arc_doc).OfCategory(DB.BuiltInCategory.OST_Phases).WhereElementIsNotElementType().ToList()
    exist_phase = phases.Where(lambda x: x.Name.Contains("Existing")).First()
    new_phase = phases.Where(lambda x: x.Name.Contains("New")).First()

    if select_phase == "Existing":
        select_phase = exist_phase.Id
    elif select_phase == "New":
        select_phase = new_phase.Id
    else:
        select_phase = None

    link_rooms, link_room_separators = phase_collector(arc_doc, select_phase)

    output.print_md("### Link Document: **{}**".format(arc_doc.Title))
    output.print_md("**Total Rooms: **{}".format(link_rooms.Count()))
    output.print_md("**Total Room Separators: **{}".format(link_room_separators.Count()))

    with DB.TransactionGroup(doc, "Create spaces") as t:
        t.Start()
        
        with DB.Transaction(doc, "Set view phase") as vt:
            vt.Start()
            set_view_phase(active_view, select_phase)
            vt.Commit()

        with DB.Transaction(doc, "Create space separators") as st:
            st.Start()
            output.print_md("### Create space separators" + 30*"#")

            floorplan_views = (DB.FilteredElementCollector(doc).OfCategory(DB.BuiltInCategory.OST_Views)
                        .WhereElementIsNotElementType()
                        .Where(lambda x: x.ViewType == DB.ViewType.FloorPlan).ToList())
            
            for separator in link_room_separators:
                try:
                    create_space_separator(separator, arc_link_instance, mapper, floorplan_views)
                except Exception:
                    import traceback
                    print("\nSeparator Error: {}".format(traceback.format_exc()))
            st.Commit()

        with DB.Transaction(doc, "Create spaces") as rt:
            rt.Start()
            output.print_md("### Create spaces" + 30*"#")
            for room in link_rooms:
                try:
                    create_space(room, mapper, arc_link_instance)
                except Exception:
                    import traceback
                    output.print_md("\nSpace Error: **{}** - {}".format(room.get_Parameter(DB.BuiltInParameter.IFC_GUID).AsString(), traceback.format_exc()))
            rt.Commit()
        
        t.Assimilate()
                 

if __name__ == '__main__':
    main()