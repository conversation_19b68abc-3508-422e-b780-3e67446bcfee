# coding: utf-8
import os
from DCMvn.forms.wpfforms import WPFWindow


class MappingValidationDialog(WPFWindow):
    """Dialog for displaying mapping configuration preview with validation"""
    
    def __init__(self, preview_content=""):
        # type: (str) -> None
        xaml_file = os.path.join(os.path.dirname(__file__), "mapping_validation_dialog.xaml")
        WPFWindow.__init__(self, xaml_file)
        self.preview_content = preview_content
        self._setup_dialog()
    
    def _setup_dialog(self):
        """Setup the dialog with content and properties"""
        if hasattr(self, 'ContentTextBlock'):
            self.ContentTextBlock.Text = self.preview_content
    
    def OnOkClick(self, sender, e):
        """Handle OK button click"""
        self.DialogResult = True
        self.Close()
    
    def OnCancelClick(self, sender, e):
        """Handle Cancel button click"""
        self.DialogResult = False
        self.Close()
    
    def show_dialog(self, owner=None):
        """Show the dialog modally and return the result"""
        # type: (object) -> bool
        
        try:
            if owner is not None:
                self.Owner = owner
            
            return self.ShowDialog()
            
        except Exception as ex:
            from DCMvn.core.framework import Debug
            Debug.WriteLine("Error in show_dialog: {}".format(str(ex)))
            return False
    
    @staticmethod
    def show_preview_dialog(owner, preview_content):
        """Static method to show the preview dialog easily"""
        # type: (object, str) -> bool
        dialog = MappingValidationDialog(preview_content)
        return dialog.show_dialog(owner) 