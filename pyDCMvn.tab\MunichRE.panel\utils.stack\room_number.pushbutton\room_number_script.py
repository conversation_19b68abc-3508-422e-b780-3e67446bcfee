# coding: utf-8
import clr
from DCMvn.core import DB, HOST_APP, get_output
from DCMvn.core.framework import System
from DCMvn.forms import alert

from lib.ui import MainView
from lib.viewmodel import MainViewModel
from lib.utils import get_element_physical_filter

clr.AddReference("System.Core")
clr.ImportExtensions(System.Linq)

doc = HOST_APP.doc
uidoc = HOST_APP.uidoc
output = get_output()

def validate_document_has_elements():
    """Check if document has physical elements"""
    try:
        physical_filter = get_element_physical_filter(doc)
        elements = (DB.FilteredElementCollector(doc)
                   .WhereElementIsNotElementType()
                   .WherePasses(physical_filter)
                   .ToElements())
        
        if not elements or len(elements) == 0:
            alert("This document does not contain any physical elements (MEP elements) to map room numbers to.\n\n" +
                  "Please open a document with MEP elements and try again.", "No Physical Elements Found")
            return False
        return True
    except Exception as e:
        alert("An error occurred while checking the document:\n\n" + str(e), "Validation Error")
        return False

def show_test_selection():
    """Show test selection dialog when Shift is pressed."""
    try:
        from tests.test_runner import show_test_selection as runner_show_test_selection
        runner_show_test_selection()
    except ImportError as e:
        alert("Error importing test module: {0}".format(str(e)), "Test Import Error")
    except Exception as e:
        alert("Error showing test selection: {0}".format(str(e)), "Test Selection Error")


if __name__ == "__main__":
    # Check if Shift key is pressed to run tests instead of main tool
    if __shiftclick__:  # type: ignore  # noqa
        show_test_selection()
    else:
        if validate_document_has_elements():
            view_model = MainViewModel(doc)
            view = MainView(view_model)
            view.show(modal=False)